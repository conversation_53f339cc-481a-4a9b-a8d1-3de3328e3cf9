export PORT=<number>
export NODE_ENV=<dev | test | prod>
export DHL_CZ_MIDDLEWARE_API_KEY=<string>
export DHL_CZ_AUTH_PORT=<number>
export DHL_CZ_API_PORT=<number>
export DHL_CZ_OAUTH_CLIENT_ID=<string>
export DHL_CZ_OAUTH_CLIENT_SECRET=<string>
export DHL_CZ_BLOQIT_API_KEY=<string>
export DHL_CZ_OAUTH_HOST=<string>
export DHL_CZ_API_HOST=<string>
export DHL_CZ_PARTNER_ID=<string>
export DHL_CZ_MOCK_PARTNER_ID=<string>
export DHL_CZ_BLOQIT_CORE_API_KEY=<string>
export BLOQIT_CORE_PORT=<number>
export USE_STATIC_UUIDS=<number>
export BLOQIT_CORE_HOST=<string>
export MONGO_DB_URL=<string>
export MONGO_DB_NAME=<string>
export DOCUMENT_DATABASE_STRATEGY=<MONGO_DB | IN_PROCESS_MEMORY>
export LOG_TO_CONSOLE_ENABLED=<enabled | disabled>
export REQ_RES_PAYLOAD_LOGGING_ENABLED=<enabled | disabled>
export DATADOG_LOGGING_ENABLED=<enabled | disabled>
export DATADOG_API_KEY=<string>
export DATADOG_SOURCE=<string>
export DATADOG_INTAKE_REGION=<string>
export DHL_IT_API_PORT=<number>
export DHL_IT_API_HOST=<string>
export DHL_IT_PARTNER_ID=<string>
export DHL_IT_MOCK_PARTNER_ID=<string>
export DHL_IT_MIDDLEWARE_API_KEY=<string>
export DHL_IT_AUTH_CLIENT_ID=<string>
export DHL_IT_AUTH_CLIENT_SECRET=<string>
export BLOQIT_COURIER_BYPASS_PIN=<string>
export PARTNER_API_MOCK_SERVER_HOST=<string>
export DHL_IT_BATCH_EXPIRED_RENTS_NOTIFICATION_WINDOW_START=<string> # e.g. 09:00
export DHL_IT_BATCH_EXPIRED_RENTS_NOTIFICATION_WINDOW_END=<string> # e.g. 10:00
export DHL_IT_BATCH_EXPIRED_RENT_NOTIFICATIONS_DRY_RUN_ENABLED=<boolean>
export BLOQIT_CORE_MIDDLEWARE_API_KEY=<string>
export INPOST_PARTNER_ID=string
export INPOST_OAUTH_HOST=string
export INPOST_API_HOST=string
export INPOST_OAUTH_CLIENT_ID=string
export INPOST_OAUTH_CLIENT_SECRET=string
export INPOST_MIDDLEWARE_API_KEY=<string>
export INPOST_BLOQIT_API_KEY=<string>