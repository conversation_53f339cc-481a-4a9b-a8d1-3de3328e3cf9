import { randomUUID } from 'crypto';
import * as jwt from 'jsonwebtoken';

export function simplifyControllerResponse(response: any): { status: number; body: any } {
    return {
        status: response.status,
        body: response.body,
    } satisfies { status: number; body: any };
}

export function createWebhookSignature(props: {
    reqBody: any;
    timestampStr: string;
    secret: string;
}) {
    const { reqBody, timestampStr, secret } = props;
    return jwt.sign(`${timestampStr}.${JSON.stringify(reqBody)}`, secret);
}

export const createCollectItemEvent = (
    args: {
        secret: string;
        rent?: string;
        bloq?: string;
        metadata: any;
        actionInitiatedBy: any;
    } = {
        rent: randomUUID(),
        bloq: randomUUID(),
        secret: '',
        metadata: {
            offlinePickup: true,
            externalID: randomUUID(),
            bloqExternalID: randomUUID(),
        },
        actionInitiatedBy: {
            role: 'Courier',
            id: 'courier-1234',
            source: 'locker',
            session: randomUUID(),
        },
    },
) => {
    const { secret, rent, bloq, actionInitiatedBy, metadata } = args;
    const timestamp = new Date().toString();
    const event = {
        rent,
        bloq,
        code: 50012,
        codeName: 'rent.collect_item',
        description: 'Rent has transited to COLLECT_ITEM state',
        title: 'Rent Collect Item',
        actionInitiatedBy,
        metadata,
    };

    const signature = createWebhookSignature({
        reqBody: event,
        timestampStr: timestamp,
        secret,
    });

    return { event, signature, timestamp };
};
