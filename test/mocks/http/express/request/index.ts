import { type Request } from 'express';

export class FakeExpressRequest {
    body: Record<string, unknown>;
    headers: Record<string, unknown>;
    params: Record<string, unknown>;
    query: Record<string, unknown>;

    constructor(
        {
            body,
            headers,
            params,
            query,
        }: { body?: any; headers?: any; params?: any; query?: any } = {
            body: {},
            headers: {},
            params: {},
            query: {},
        }
    ) {
        this.body = body;
        this.headers = headers;
        this.params = params;
        this.query = query;
    }
}

export default class FakeExpressRequestBuilder {
    body: any;
    headers: any;
    params: any;
    query: any;

    constructor() {
        this.body = {};
        this.headers = {};
        this.params = {};
        this.query = {};
    }

    withBody(body: any): FakeExpressRequestBuilder {
        this.body = body;
        return this;
    }

    withHeaders(headers: any): FakeExpressRequestBuilder {
        this.headers = headers;
        return this;
    }

    withParams(params: any): FakeExpressRequestBuilder {
        this.params = params;
        return this;
    }

    withQuery(query: any): FakeExpressRequestBuilder {
        this.query = query;
        return this;
    }

    build(): Request {
        return new FakeExpressRequest({
            body: this.body,
            headers: this.headers,
            params: this.params,
            query: this.query,
        }) as unknown as Request;
    }
}
