import EventEmitter from 'stream';
import { type Response } from 'express';

export class FakeExpressResponse extends EventEmitter {
    statusCode: number;
    body: object;
    callbacks: Record<string, unknown>;

    constructor(
        { statusCode, body = {} }: { statusCode: number; body?: any } = { statusCode: 200 }
    ) {
        super();
        this.statusCode = statusCode;
        this.body = body;
        this.callbacks = {};
    }

    status(status: number): Response {
        this.statusCode = status;
        return this as unknown as Response;
    }

    json(obj: any): Response {
        return { body: obj, status: this.statusCode } as unknown as Response;
    }

    send(obj: any): Response {
        return { body: obj, status: this.statusCode } as unknown as Response;
    }

    end(): Response {
        return { status: this.statusCode } as unknown as Response;
    }
}

export default class FakeExpressResponseBuilder {
    statusCode: number;
    body: any;

    constructor() {
        this.statusCode = 200;
        this.body = {};
    }

    build(): Response {
        return new FakeExpressResponse() as unknown as Response;
    }
}
