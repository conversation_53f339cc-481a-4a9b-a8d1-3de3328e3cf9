import Pactum from 'pactum';

describe('Health check', () => {
    it('should return 200 OK while up', () => {
        return Pactum.spec().get('http://localhost:3000/healthcheck').expectStatus(200).toss();
    });
});

describe('Partner Authentication', () => {
    it('should reject requests of invalid partner ids', () => {
        return Pactum.spec()
            .get('http://localhost:3000/auth/check')
            .withHeaders({ 'x-partner-id': 'super-hacker-nanana' })
            .expectStatus(401)
            .toss();
    });

    it('should reject request of known partner ids without an API key', () => {
        return Pactum.spec()
            .get('http://localhost:3000/auth/check')
            .withHeaders({ 'x-partner-id': process.env.DHL_CZ_PARTNER_ID })
            .expectStatus(401)
            .toss();
    });

    it('should reject request of known partner ids with an invalid API key', () => {
        return Pactum.spec()
            .get('http://localhost:3000/auth/check')
            .withHeaders({
                'x-partner-id': process.env.DHL_CZ_PARTNER_ID,
                'x-api-key': 'invalid-key',
            })
            .expectStatus(401)
            .toss();
    });

    it('should reject requests from identified partners impersonating other partner', () => {
        const DHL_IT_MIDDLEWARE_API_KEY = process.env.DHL_IT_MIDDLEWARE_API_KEY;
        if (DHL_IT_MIDDLEWARE_API_KEY === undefined) {
            throw new Error('Missing DHL_IT_MIDDLEWARE_API_KEY env var');
        }

        return Pactum.spec()
            .get('http://localhost:3000/auth/check')
            .withHeaders({
                'x-partner-id': process.env.DHL_CZ_PARTNER_ID,
                'x-api-key': Buffer.from(DHL_IT_MIDDLEWARE_API_KEY).toString('base64'),
            })
            .expectStatus(401)
            .toss();
    });

    it('should accept requests from partners with a proper partner identification and API key', () => {
        const DHL_CZ_MIDDLEWARE_API_KEY = process.env.DHL_CZ_MIDDLEWARE_API_KEY;
        if (DHL_CZ_MIDDLEWARE_API_KEY === undefined) {
            throw new Error('Missing DHL_CZ_MIDDLEWARE_API_KEY env var');
        }

        return Pactum.spec()
            .get('http://localhost:3000/auth/check')
            .withHeaders({
                'x-partner-id': process.env.DHL_CZ_PARTNER_ID,
                'x-api-key': Buffer.from(DHL_CZ_MIDDLEWARE_API_KEY).toString('base64'),
            })
            .expectStatus(200)
            .toss();
    });
});
