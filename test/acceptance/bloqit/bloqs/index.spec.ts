import { randomUUID } from 'crypto';
import Pactum from 'pactum';
import { createCollectItemEvent, createWebhookSignature } from '../../../utils';
import { DHLITAPIMockServer } from '../../dhl/it/fixtures/partner-api-mock';
import { http } from 'wirepig';

const BLOQIT_CORE_MIDDLEWARE_API_KEY = process.env.BLOQIT_CORE_MIDDLEWARE_API_KEY;
if (BLOQIT_CORE_MIDDLEWARE_API_KEY === undefined) {
    throw new Error('BLOQIT_CORE_MIDDLEWARE_API_KEY not found in environment variables');
}

const coreAPIKey = Buffer.from(BLOQIT_CORE_MIDDLEWARE_API_KEY).toString('base64');

describe('Bloqit', () => {
    describe('Bloqs', () => {
        describe('DHL CZ', () => {
            describe('POST /bloqs/events/created', () => {
                const bloqitId = randomUUID();
                const sharedId = randomUUID();

                const partnerId = process.env.DHL_CZ_PARTNER_ID;

                it('should store an id mapping for a bloq', () => {
                    const reqTimestamp = new Date().toString();
                    const reqBody = { bloq: bloqitId, metadata: { bloqExternalID: sharedId } };
                    const signature = createWebhookSignature({
                        reqBody,
                        timestampStr: reqTimestamp,
                        secret: coreAPIKey,
                    });

                    return Pactum.spec()
                        .post('http://localhost:3000/bloqs/events/created')
                        .withHeaders({
                            signature,
                            'x-partner-id': partnerId,
                            timestamp: reqTimestamp,
                        })
                        .withBody(reqBody)
                        .expectStatus(204)
                        .toss();
                });
            });
        });

        describe('DHL IT', () => {
            describe('POST /bloqs/events/courier-logout', () => {
                const bloqitBloqId = randomUUID();
                const partnerBloqId = randomUUID();
                const courierId = randomUUID();
                const courierSessionId = randomUUID();
                const bloqitRentId1 = randomUUID();
                const bloqitRentId2 = randomUUID();
                const partnerRentId1 = randomUUID();
                const partnerRentId2 = randomUUID();

                const partnerId = process.env.DHL_IT_PARTNER_ID;
                const DHL_IT_API_PORT = Number(process.env.DHL_IT_API_PORT) ?? 3004;

                let dhlITapi: DHLITAPIMockServer;

                beforeAll(async () => {
                    dhlITapi = new DHLITAPIMockServer({
                        httpInstance: await http({ port: DHL_IT_API_PORT }),
                    });

                    const firstPickup = createCollectItemEvent({
                        secret: coreAPIKey,
                        rent: bloqitRentId1,
                        bloq: bloqitBloqId,
                        actionInitiatedBy: {
                            id: courierId,
                            session: courierSessionId,
                            role: 'Courier',
                        },
                        metadata: {
                            offlinePickup: true,
                            externalID: partnerRentId1,
                            bloqExternalID: partnerBloqId,
                            rentMetadata: {
                                productType: 'product-type-1',
                            },
                        },
                    });

                    const secondPickup = createCollectItemEvent({
                        secret: coreAPIKey,
                        rent: bloqitRentId2,
                        bloq: bloqitBloqId,
                        actionInitiatedBy: {
                            id: courierId,
                            session: courierSessionId,
                            role: 'Courier',
                        },
                        metadata: {
                            offlinePickup: true,
                            externalID: partnerRentId2,
                            bloqExternalID: partnerBloqId,
                            rentMetadata: {
                                productType: 'product-type-2',
                            },
                        },
                    });

                    await Pactum.spec()
                        .post('http://localhost:3000/rents/events/collect-item')
                        .withHeaders({
                            signature: firstPickup.signature,
                            timestamp: firstPickup.timestamp,
                            'x-partner-id': partnerId,
                        })
                        .withBody(firstPickup.event)
                        .expectStatus(204)
                        .toss();

                    await Pactum.spec()
                        .post('http://localhost:3000/rents/events/collect-item')
                        .withHeaders({
                            signature: secondPickup.signature,
                            timestamp: secondPickup.timestamp,
                            'x-partner-id': partnerId,
                        })
                        .withBody(secondPickup.event)
                        .expectStatus(204)
                        .toss();
                });

                afterAll(async () => {
                    await dhlITapi.stop();
                });

                it('should send the aggregated pickup session data to DHL IT', () => {
                    dhlITapi.registerCreateShipmentInteraction({
                        res: {
                            status: 200,
                            body: JSON.stringify({ status: 'Success', errorCode: 0 }),
                        },
                    });

                    const reqTimestamp = new Date().toString();
                    const courierLogoutEvent = {
                        code: 120012,
                        bloq: bloqitBloqId,
                        codeName: 'courier_logout',
                        title: 'Courier logout from Bloq',
                        description: 'Courier has logout from the bloq',
                        metadata: { bloqExternalID: partnerBloqId },
                        actionInitiatedBy: { id: courierId, session: courierSessionId },
                    };

                    const signature = createWebhookSignature({
                        reqBody: courierLogoutEvent,
                        timestampStr: reqTimestamp,
                        secret: coreAPIKey,
                    });

                    return Pactum.spec()
                        .post('http://localhost:3000/bloqs/events/courier-logout')
                        .withHeaders({
                            signature,
                            timestamp: reqTimestamp,
                            'x-partner-id': partnerId,
                        })
                        .withBody(courierLogoutEvent)
                        .expectStatus(204)
                        .toss();
                });
            });
        });
    });
});
