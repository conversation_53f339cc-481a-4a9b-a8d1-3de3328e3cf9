import Pactum from 'pactum';
import { createWebhookSignature } from '../../../utils';

export class BloqitBloqsMiddlewareResource {
    private readonly host: string;
    constructor(deps: { host: string }) {
        this.host = deps.host;
    }

    async sendBloqCreatedEvent(props: {
        bloqId: string;
        partnerFriendlyId: string;
        apiKey: string;
        metadata: { bloqExternalID: string };
    }) {
        const { bloqId, partnerFriendlyId, apiKey, metadata } = props;
        const reqBody = { bloq: bloqId, metadata };
        const reqTimestamp = new Date().toString();
        const signature = createWebhookSignature({
            reqBody,
            timestampStr: reqTimestamp,
            secret: apiKey,
        });

        return Pactum.spec()
            .post(`${this.host}/bloqs/events/created`)
            .withHeaders({
                signature,
                timestamp: reqTimestamp,
                'X-Partner-ID': partnerFriendlyId,
            })
            .withBody(reqBody)
            .expectStatus(204);
    }
}
