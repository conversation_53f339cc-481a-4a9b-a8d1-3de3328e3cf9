import { randomUUID } from 'crypto';
import { http } from 'wirepig';
import Pactum from 'pactum';
import { DHLITAPIMockServer } from '../../dhl/it/fixtures/partner-api-mock';

const DHL_IT_API_PORT = Number(process.env.DHL_IT_API_PORT) ?? 3004;

const BLOQIT_CORE_MIDDLEWARE_API_KEY = process.env.BLOQIT_CORE_MIDDLEWARE_API_KEY;
if (BLOQIT_CORE_MIDDLEWARE_API_KEY === undefined) {
    throw new Error('BLOQIT_CORE_MIDDLEWARE_API_KEY not found in environment variables');
}

const coreAPIKey = Buffer.from(BLOQIT_CORE_MIDDLEWARE_API_KEY).toString('base64');

describe('Bloqit', () => {
    describe('Couriers', () => {
        describe('DHL IT', () => {
            describe('POST /couriers/authenticate', () => {
                let dhlITapi: DHLITAPIMockServer;
                beforeEach(() => {
                    dhlITapi.reset();
                });

                beforeAll(async () => {
                    dhlITapi = new DHLITAPIMockServer({
                        httpInstance: await http({ port: DHL_IT_API_PORT }),
                    });
                });

                afterAll(async () => {
                    await dhlITapi.stop();
                });

                const BLOQIT_CORE_MIDDLEWARE_API_KEY = process.env.BLOQIT_CORE_MIDDLEWARE_API_KEY;
                if (BLOQIT_CORE_MIDDLEWARE_API_KEY === undefined) {
                    throw new Error(
                        'BLOQIT_CORE_MIDDLEWARE_API_KEY not found in environment variables',
                    );
                }

                const partnerId = process.env.DHL_IT_PARTNER_ID;

                const pin = '1234';
                const bloqExternalId = randomUUID();
                const session = randomUUID();

                it('should return an error if bloqExternalId is not provided', async () => {
                    const reqBody = { session, pin, bloqExternalID: undefined };

                    return Pactum.spec()
                        .post('http://localhost:3000/couriers/authenticate')
                        .withHeaders({ 'x-core-api-key': coreAPIKey, 'x-partner-id': partnerId })
                        .withBody(reqBody)
                        .expectStatus(500)
                        .expectJsonLike({ success: false, message: 'bloqExternalId is required' })
                        .toss();
                });

                it('should return an error if courierSessionId is not provided', async () => {
                    const reqBody = { pin, bloqExternalID: bloqExternalId, session: undefined };

                    return Pactum.spec()
                        .post('http://localhost:3000/couriers/authenticate')
                        .withHeaders({ 'x-core-api-key': coreAPIKey, 'x-partner-id': partnerId })
                        .withBody(reqBody)
                        .expectStatus(500)
                        .expectJsonLike({ success: false, message: 'courierSessionId is required' })
                        .toss();
                });

                it('should bypass DHL authentication if PIN is pre-defined bypass pin', () => {
                    const pin = process.env.BLOQIT_COURIER_BYPASS_PIN;
                    const reqBody = { bloqExternalID: bloqExternalId, session, pin };

                    return Pactum.spec()
                        .post('http://localhost:3000/couriers/authenticate')
                        .withHeaders({ 'x-core-api-key': coreAPIKey, 'x-partner-id': partnerId })
                        .withBody(reqBody)
                        .expectStatus(200)
                        .expectJsonLike({ success: true })
                        .toss();
                });

                it('should authenticate a courier successfully', () => {
                    dhlITapi.registerVerifyPinInteraction({
                        res: {
                            status: 200,
                            body: JSON.stringify({ userId_hash: '1234' }),
                        },
                    });

                    const reqBody = { bloqExternalID: bloqExternalId, session, pin };

                    return Pactum.spec()
                        .post('http://localhost:3000/couriers/authenticate')
                        .withHeaders({ 'x-core-api-key': coreAPIKey, 'x-partner-id': partnerId })
                        .withBody(reqBody)
                        .expectStatus(200)
                        .expectJsonLike({ success: true })
                        .toss();
                });

                it('should refuse to authenticate an unauthorized courier', () => {
                    dhlITapi.registerVerifyPinInteraction({
                        res: { status: 401, body: JSON.stringify({ userId_hash: undefined }) },
                    });

                    const reqBody = { bloqExternalID: bloqExternalId, session, pin };

                    return Pactum.spec()
                        .post('http://localhost:3000/couriers/authenticate')
                        .withHeaders({ 'x-core-api-key': coreAPIKey, 'x-partner-id': partnerId })
                        .withBody(reqBody)
                        .expectStatus(200)
                        .expectJsonLike({ success: false })
                        .toss();
                });

                it('should refuse to allow authenticate a courier that does not have access to the resource', () => {
                    dhlITapi.registerVerifyPinInteraction({
                        res: { status: 403, body: JSON.stringify({ userId_hash: undefined }) },
                    });

                    const reqBody = { bloqExternalID: bloqExternalId, session, pin };

                    return Pactum.spec()
                        .post('http://localhost:3000/couriers/authenticate')
                        .withHeaders({ 'x-core-api-key': coreAPIKey, 'x-partner-id': partnerId })
                        .withBody(reqBody)
                        .expectStatus(200)
                        .expectJsonLike({ success: false })
                        .toss();
                });
            });
        });
    });
});
