import { http, helpers, HTTPMockServer } from 'wirepig';

export class BloqitPublicAPIMockServer {
    private readonly server: HTTPMockServer;

    constructor(deps: { httpInstance: HTTPMockServer }) {
        this.server = deps.httpInstance;
    }

    registerUpdateBloqInteraction(props: { bloqId: string; reqBody?: any; res: any }) {
        const { bloqId, res, reqBody } = props;
        this.server.mock({
            res,
            req: { method: 'PUT', pathname: `/bloqs/${bloqId}`, body: JSON.stringify(reqBody) },
        });
    }

    reset() {
        this.server.reset();
    }

    async stop() {
        await this.server.teardown();
    }
}
