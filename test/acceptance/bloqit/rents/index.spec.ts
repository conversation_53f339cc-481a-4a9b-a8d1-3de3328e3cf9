import Pactum from 'pactum';
import { randomUUID } from 'crypto';
import { http, helpers } from 'wirepig';

import { BloqitRentsMiddlewareResource } from '.';
import { BloqitBloqsMiddlewareResource } from '../bloqs';
import { DHLCZMiddlewareResource } from '../../dhl/cz';
import { MOCKED_DHL_CZ_SHIPMENT } from '../../dhl/cz/fixtures/shipment';
import { DHLCZAPIMockServer, DHLCZAuthMockServer } from '../../dhl/cz/partner-api-mock';
import { BloqitPublicAPIMockServer } from '../public-api-mock';
import { createWebhookSignature } from '../../../utils';
import * as mockedDHLITShipments from '../../dhl/it/fixtures/shipment';
import { DHLITAPIMockServer } from '../../dhl/it/fixtures/partner-api-mock';

const MIDDLEWARE_HOST = `http://localhost:${process.env.PORT}`;
const BLOQIT_CORE_PORT = Number(process.env.BLOQIT_CORE_PORT) ?? 3003;

const BLOQIT_CORE_MIDDLEWARE_API_KEY = process.env.BLOQIT_CORE_MIDDLEWARE_API_KEY;
if (BLOQIT_CORE_MIDDLEWARE_API_KEY === undefined) {
    throw new Error('BLOQIT_CORE_MIDDLEWARE_API_KEY not found in environment variables');
}

const coreAPIKey = Buffer.from(BLOQIT_CORE_MIDDLEWARE_API_KEY).toString('base64');

describe('Bloqit', () => {
    describe('rents', () => {
        describe('DHL CZ', () => {
            const DHL_CZ_AUTH_PORT = Number(process.env.DHL_CZ_AUTH_PORT) ?? 3001;
            const DHL_CZ_API_PORT = Number(process.env.DHL_CZ_API_PORT) ?? 3002;

            const DHL_CZ_MIDDLEWARE_API_KEY = process.env.DHL_CZ_MIDDLEWARE_API_KEY;
            if (DHL_CZ_MIDDLEWARE_API_KEY === undefined) {
                throw new Error('DHL_CZ_MIDDLEWARE_API_KEY not found in environment variables');
            }

            const dhlCZAPIKey = Buffer.from(DHL_CZ_MIDDLEWARE_API_KEY).toString('base64');

            const dhlPartnerFriendlyId = process.env.DHL_CZ_PARTNER_ID;
            if (dhlPartnerFriendlyId === undefined) {
                throw new Error('DHL_CZ_PARTNER_ID not found in environment variables');
            }

            const partnerRentId = 'partner-rent-id-1';

            const setupWebhookBoilerplate = async ({
                bloqitBloqId,
                dhlCzBloqId,
                sharedBloqId,
                shouldFail = false,
            }) => {
                const dhlCZapi = new DHLCZAPIMockServer({
                    httpInstance: await http({ port: DHL_CZ_API_PORT }),
                });

                const bloqitPublicAPI = new BloqitPublicAPIMockServer({
                    httpInstance: await http({ port: BLOQIT_CORE_PORT }),
                });

                const dhlCZAuthServer = new DHLCZAuthMockServer({
                    httpInstance: await http({ port: DHL_CZ_AUTH_PORT }),
                });

                dhlCZAuthServer.registerSuccessfulGetTokenInteraction();
                dhlCZapi.registerEventInteraction({
                    res: {
                        statusCode: shouldFail ? 500 : 200,
                        body: JSON.stringify({
                            dataResponse: { status: shouldFail ? 'ERROR' : 'OK' },
                        }),
                    },
                });
                bloqitPublicAPI.registerUpdateBloqInteraction({
                    bloqId: bloqitBloqId,
                    reqBody: helpers.match.json({ active: true }),
                    res: { statusCode: 200 },
                });

                await new BloqitBloqsMiddlewareResource({
                    host: MIDDLEWARE_HOST,
                }).sendBloqCreatedEvent({
                    partnerFriendlyId: dhlPartnerFriendlyId,
                    apiKey: coreAPIKey,
                    bloqId: bloqitBloqId,
                    metadata: { bloqExternalID: sharedBloqId },
                });

                await new DHLCZMiddlewareResource({
                    apiKey: dhlCZAPIKey,
                    host: MIDDLEWARE_HOST,
                    friendlyId: dhlPartnerFriendlyId,
                })
                    .configure({
                        deviceId: sharedBloqId,
                        accessPointId: dhlCzBloqId,
                        command: 'activate',
                    })
                    .expectStatus(200);

                return { dhlCZapi, dhlCZAuthServer, bloqitPublicAPI };
            };

            describe('GET /rents/:partnerRentId?partnerBloqId=:partnerBloqId', () => {
                const bloqitBloqId = randomUUID();
                const dhlCzBloqId = randomUUID();
                const sharedId = randomUUID();

                let dhlCZapi: DHLCZAPIMockServer;
                let dhlCZAuthServer: DHLCZAuthMockServer;
                let bloqitPublicAPI: BloqitPublicAPIMockServer;
                beforeAll(async () => {
                    dhlCZapi = new DHLCZAPIMockServer({
                        httpInstance: await http({ port: DHL_CZ_API_PORT }),
                    });

                    bloqitPublicAPI = new BloqitPublicAPIMockServer({
                        httpInstance: await http({ port: BLOQIT_CORE_PORT }),
                    });

                    dhlCZAuthServer = new DHLCZAuthMockServer({
                        httpInstance: await http({ port: DHL_CZ_AUTH_PORT }),
                    });

                    bloqitPublicAPI.registerUpdateBloqInteraction({
                        bloqId: bloqitBloqId,
                        reqBody: helpers.match.json({ active: true }),
                        res: { statusCode: 200 },
                    });

                    await new BloqitBloqsMiddlewareResource({
                        host: MIDDLEWARE_HOST,
                    }).sendBloqCreatedEvent({
                        partnerFriendlyId: dhlPartnerFriendlyId,
                        apiKey: coreAPIKey,
                        bloqId: bloqitBloqId,
                        metadata: { bloqExternalID: sharedId },
                    });

                    await new DHLCZMiddlewareResource({
                        apiKey: dhlCZAPIKey,
                        host: MIDDLEWARE_HOST,
                        friendlyId: dhlPartnerFriendlyId,
                    })
                        .configure({
                            deviceId: sharedId,
                            accessPointId: dhlCzBloqId,
                            command: 'activate',
                        })
                        .expectStatus(200);
                });

                afterAll(async () => {
                    await bloqitPublicAPI.stop();
                    await dhlCZAuthServer.stop();
                    await dhlCZapi.stop();
                });

                it("should fetch a rent registered at DHL CZ's side", async () => {
                    dhlCZAuthServer.registerSuccessfulGetTokenInteraction();
                    dhlCZapi.registerGetShipmentInteraction({
                        res: { status: 200, body: JSON.stringify(MOCKED_DHL_CZ_SHIPMENT) },
                    });

                    await new BloqitRentsMiddlewareResource({ host: MIDDLEWARE_HOST })
                        .getRentById({
                            partnerId: dhlPartnerFriendlyId,
                            partnerRentId,
                            bloqitBloqId,
                        })
                        .withHeaders({
                            'x-partner-id': dhlPartnerFriendlyId,
                            'x-core-api-key': coreAPIKey,
                        })
                        .expectStatus(200)
                        .expectJsonLike({
                            meta: { partnerId: dhlPartnerFriendlyId },
                            rents: [
                                {
                                    externalID: '87654321',
                                    dropOffCode: '87654321',
                                    dimensions: { length: 10, width: 20, height: 30 },
                                    customer: { name: 'John Doe', email: '<EMAIL>' },
                                },
                            ],
                        })
                        .toss();
                });

                it('should forward DHL-baked errors if HTTP code is 200 OK but custom status is ERROR and there is a status message', async () => {
                    dhlCZAuthServer.registerSuccessfulGetTokenInteraction();
                    dhlCZapi.registerGetShipmentInteraction({
                        res: {
                            status: 200,
                            body: JSON.stringify({
                                dataResponse: {
                                    sessionId: randomUUID(),
                                    timestamp: new Date().toISOString(),
                                    statusMessage: 'DHL-baked, domain specific error message',
                                    status: 'ERROR',
                                    statusCode: '600',
                                },
                            }),
                        },
                    });

                    await new BloqitRentsMiddlewareResource({ host: MIDDLEWARE_HOST })
                        .getRentById({
                            partnerId: dhlPartnerFriendlyId,
                            partnerRentId,
                            bloqitBloqId,
                        })
                        .withHeaders({
                            'x-partner-id': dhlPartnerFriendlyId,
                            'x-core-api-key': coreAPIKey,
                        })
                        .expectStatus(500)
                        .expectJsonLike({
                            error: {
                                code: '600',
                                message: 'DHL-baked, domain specific error message',
                            },
                        })
                        .toss();
                });

                it('should return a generic error if HTTP status code is 200 OK but custom status is ERROR and the status message is empty', async () => {
                    dhlCZAuthServer.registerSuccessfulGetTokenInteraction();
                    dhlCZapi.registerGetShipmentInteraction({
                        res: {
                            status: 200,
                            body: JSON.stringify({
                                dataResponse: {
                                    sessionId: randomUUID(),
                                    timestamp: new Date().toISOString(),
                                    statusMessage: '',
                                    status: 'ERROR',
                                    statusCode: '600',
                                },
                            }),
                        },
                    });

                    await new BloqitRentsMiddlewareResource({ host: MIDDLEWARE_HOST })
                        .getRentById({
                            partnerId: dhlPartnerFriendlyId,
                            partnerRentId,
                            bloqitBloqId,
                        })
                        .withHeaders({
                            'x-partner-id': dhlPartnerFriendlyId,
                            'x-core-api-key': coreAPIKey,
                        })
                        .expectStatus(500)
                        .expectJsonLike({
                            error: {
                                code: 'dhl:cz:get-shipment:error',
                                message: 'qrCodeDontMatchActiveRent',
                            },
                        })
                        .toss();
                });

                it('should return a generic error if HTTP status code is 200 OK but custom status is ERROR and the status message is undefined', async () => {
                    dhlCZAuthServer.registerSuccessfulGetTokenInteraction();
                    dhlCZapi.registerGetShipmentInteraction({
                        res: {
                            status: 200,
                            body: JSON.stringify({
                                dataResponse: {
                                    sessionId: randomUUID(),
                                    timestamp: new Date().toISOString(),
                                    statusMessage: undefined,
                                    status: 'ERROR',
                                    statusCode: '600',
                                },
                            }),
                        },
                    });

                    await new BloqitRentsMiddlewareResource({ host: MIDDLEWARE_HOST })
                        .getRentById({
                            partnerId: dhlPartnerFriendlyId,
                            partnerRentId,
                            bloqitBloqId,
                        })
                        .withHeaders({
                            'x-partner-id': dhlPartnerFriendlyId,
                            'x-core-api-key': coreAPIKey,
                        })
                        .expectStatus(500)
                        .expectJsonLike({
                            error: {
                                code: 'dhl:cz:get-shipment:error',
                                message: 'qrCodeDontMatchActiveRent',
                            },
                        })
                        .toss();
                });

                it('should return a generic error message if HTTP status code is not 200 OK', async () => {
                    dhlCZAuthServer.registerSuccessfulGetTokenInteraction();
                    dhlCZapi.registerGetShipmentInteraction({
                        res: {
                            statusCode: 500,
                            body: JSON.stringify({
                                dataResponse: {
                                    sessionId: randomUUID(),
                                    timestamp: new Date().toISOString(),
                                    statusMessage: 'Shipment not found',
                                    status: 'ERROR',
                                    statusCode: '404',
                                },
                            }),
                        },
                    });

                    await new BloqitRentsMiddlewareResource({ host: MIDDLEWARE_HOST })
                        .getRentById({
                            partnerId: dhlPartnerFriendlyId,
                            partnerRentId,
                            bloqitBloqId,
                        })
                        .withHeaders({
                            'x-partner-id': dhlPartnerFriendlyId,
                            'x-core-api-key': coreAPIKey,
                        })
                        .expectStatus(500)
                        .expectJsonLike({
                            error: {
                                code: 'dhl:cz:get-shipment:error',
                                message: 'qrCodeDontMatchActiveRent',
                            },
                        })
                        .toss();
                });
            });

            describe('Webhook events', () => {
                describe('POST /rents/events/created', () => {
                    const bloqitRentId = randomUUID();
                    const dhlCZRentId = randomUUID();

                    it('should store an id mapping for a rent', () => {
                        const reqBody = {
                            rent: bloqitRentId,
                            metadata: { externalID: dhlCZRentId },
                        };
                        const reqTimestamp = new Date().toString();
                        const signature = createWebhookSignature({
                            reqBody,
                            timestampStr: reqTimestamp,
                            secret: coreAPIKey,
                        });

                        return Pactum.spec()
                            .post('http://localhost:3000/rents/events/created')
                            .withHeaders({
                                signature,
                                timestamp: reqTimestamp,
                                'x-partner-id': dhlPartnerFriendlyId,
                            })
                            .withBody(reqBody)
                            .expectStatus(204)
                            .toss();
                    });
                });

                describe('POST /rents/events/collect-item', () => {
                    const bloqitRentId = randomUUID();
                    const dhlCZRentId = randomUUID();
                    const bloqitBloqId = randomUUID();
                    const dhlCzBloqId = randomUUID();
                    const sharedBloqId = randomUUID();

                    let dhlCZapi: DHLCZAPIMockServer;
                    let dhlCZAuthServer: DHLCZAuthMockServer;
                    let bloqitPublicAPI: BloqitPublicAPIMockServer;
                    beforeAll(async () => {
                        const mockedServers = await setupWebhookBoilerplate({
                            bloqitBloqId,
                            dhlCzBloqId,
                            sharedBloqId,
                        });

                        dhlCZapi = mockedServers.dhlCZapi;
                        dhlCZAuthServer = mockedServers.dhlCZAuthServer;
                        bloqitPublicAPI = mockedServers.bloqitPublicAPI;
                    });

                    afterAll(async () => {
                        await bloqitPublicAPI.stop();
                        await dhlCZAuthServer.stop();
                        await dhlCZapi.stop();
                    });

                    it('should forward a COLLECT_ITEM event to DHL', () => {
                        const reqBody = {
                            rent: bloqitRentId,
                            bloq: bloqitBloqId,
                            code: 50012,
                            codeName: 'rent.collect_item',
                            description: 'Rent has transited to COLLECT_ITEM state',
                            title: 'Rent Collect Item',
                            actionInitiatedBy: {
                                role: 'Courier',
                                id: 'courier-1234',
                                source: 'locker',
                                session: 'session-1234',
                            },
                            metadata: {
                                externalID: dhlCZRentId,
                                offlinePickup: true,
                                lockerExternalID: 'A1',
                            },
                        };

                        const reqTimestamp = new Date().toString();
                        const signature = createWebhookSignature({
                            reqBody,
                            timestampStr: reqTimestamp,
                            secret: coreAPIKey,
                        });

                        return Pactum.spec()
                            .post('http://localhost:3000/rents/events/collect-item')
                            .withHeaders({
                                signature,
                                timestamp: reqTimestamp,
                                'x-partner-id': dhlPartnerFriendlyId,
                            })
                            .withBody(reqBody)
                            .expectStatus(204)
                            .toss();
                    });

                    it('should propertly handle and log errors', () => {
                        const reqBody = {
                            rent: bloqitRentId,
                            bloq: bloqitBloqId,
                            code: 50012,
                            codeName: 'rent.collect_item',
                            description: 'Rent has transited to COLLECT_ITEM state',
                            title: 'Rent Collect Item',
                            actionInitiatedBy: {
                                role: 'Courier',
                                id: 'courier-1234',
                                source: 'locker',
                                session: 'session-1234',
                            },
                            metadata: {
                                externalID: dhlCZRentId,
                                offlinePickup: true,
                                lockerExternalID: 'A1',
                            },
                        };

                        dhlCZapi.registerEventInteraction({
                            res: {
                                statusCode: 500,
                                body: JSON.stringify({
                                    dataResponse: { status: 'ERROR' },
                                }),
                            },
                        });

                        const reqTimestamp = new Date().toString();
                        const signature = createWebhookSignature({
                            reqBody,
                            timestampStr: reqTimestamp,
                            secret: coreAPIKey,
                        });

                        return Pactum.spec()
                            .post('http://localhost:3000/rents/events/collect-item')
                            .withHeaders({
                                signature,
                                timestamp: reqTimestamp,
                                'x-partner-id': dhlPartnerFriendlyId,
                            })
                            .withBody(reqBody)
                            .expectStatus(500)
                            .toss();
                    });
                });

                describe('POST /rents/events/expired', () => {
                    const bloqitRentId = randomUUID();
                    const dhlCZRentId = randomUUID();
                    const bloqitBloqId = randomUUID();
                    const dhlCzBloqId = randomUUID();
                    const sharedBloqId = randomUUID();

                    let dhlCZapi: DHLCZAPIMockServer;
                    let dhlCZAuthServer: DHLCZAuthMockServer;
                    let bloqitPublicAPI: BloqitPublicAPIMockServer;
                    beforeAll(async () => {
                        const mockedServers = await setupWebhookBoilerplate({
                            bloqitBloqId,
                            dhlCzBloqId,
                            sharedBloqId,
                        });

                        dhlCZapi = mockedServers.dhlCZapi;
                        dhlCZAuthServer = mockedServers.dhlCZAuthServer;
                        bloqitPublicAPI = mockedServers.bloqitPublicAPI;
                    });

                    afterAll(async () => {
                        await bloqitPublicAPI.stop();
                        await dhlCZAuthServer.stop();
                        await dhlCZapi.stop();
                    });

                    it('should forward a rent expired event to DHL', () => {
                        const reqBody = {
                            rent: bloqitRentId,
                            bloq: bloqitBloqId,
                            code: 50010,
                            codeName: 'rent.expired',
                            description: 'Rent has transited to EXPIRED state',
                            title: 'Rent Expired',
                            actionInitiatedBy: { role: 'System' },
                            metadata: { externalID: dhlCZRentId },
                        };

                        const reqTimestamp = new Date().toString();
                        const signature = createWebhookSignature({
                            reqBody,
                            timestampStr: reqTimestamp,
                            secret: coreAPIKey,
                        });

                        return Pactum.spec()
                            .post('http://localhost:3000/rents/events/expired')
                            .withHeaders({
                                signature,
                                timestamp: reqTimestamp,
                                'x-partner-id': dhlPartnerFriendlyId,
                            })
                            .withBody(reqBody)
                            .expectStatus(204)
                            .toss();
                    });
                });

                describe('POST /rents/events/finished', () => {
                    const bloqitRentId = randomUUID();
                    const dhlCZRentId = randomUUID();
                    const bloqitBloqId = randomUUID();
                    const dhlCzBloqId = randomUUID();
                    const sharedBloqId = randomUUID();

                    let dhlCZapi: DHLCZAPIMockServer;
                    let dhlCZAuthServer: DHLCZAuthMockServer;
                    let bloqitPublicAPI: BloqitPublicAPIMockServer;
                    beforeAll(async () => {
                        const mockedServers = await setupWebhookBoilerplate({
                            bloqitBloqId,
                            dhlCzBloqId,
                            sharedBloqId,
                        });

                        dhlCZapi = mockedServers.dhlCZapi;
                        dhlCZAuthServer = mockedServers.dhlCZAuthServer;
                        bloqitPublicAPI = mockedServers.bloqitPublicAPI;
                    });

                    afterAll(async () => {
                        await bloqitPublicAPI.stop();
                        await dhlCZAuthServer.stop();
                        await dhlCZapi.stop();
                    });

                    it('should forward a RENT_FINISHED event to DHL', () => {
                        const reqBody = {
                            rent: bloqitRentId,
                            bloq: bloqitBloqId,
                            code: 50010,
                            codeName: 'rent.finished',
                            description: 'Rent has transited to FINISHED state',
                            title: 'Rent Finished',
                            actionInitiatedBy: { role: 'System' },
                            metadata: { externalID: dhlCZRentId },
                        };

                        const reqTimestamp = new Date().toString();
                        const signature = createWebhookSignature({
                            reqBody,
                            timestampStr: reqTimestamp,
                            secret: coreAPIKey,
                        });

                        return Pactum.spec()
                            .post('http://localhost:3000/rents/events/finished')
                            .withHeaders({
                                signature,
                                timestamp: reqTimestamp,
                                'x-partner-id': dhlPartnerFriendlyId,
                            })
                            .withBody(reqBody)
                            .expectStatus(204)
                            .toss();
                    });
                });

                describe('POST /rents/events/door-stuck', () => {
                    const bloqitRentId = randomUUID();
                    const dhlCZRentId = randomUUID();
                    const bloqitBloqId = randomUUID();
                    const dhlCzBloqId = randomUUID();
                    const sharedBloqId = randomUUID();

                    let dhlCZapi: DHLCZAPIMockServer;
                    let dhlCZAuthServer: DHLCZAuthMockServer;
                    let bloqitPublicAPI: BloqitPublicAPIMockServer;
                    beforeAll(async () => {
                        const mockedServers = await setupWebhookBoilerplate({
                            bloqitBloqId,
                            dhlCzBloqId,
                            sharedBloqId,
                        });

                        dhlCZapi = mockedServers.dhlCZapi;
                        dhlCZAuthServer = mockedServers.dhlCZAuthServer;
                        bloqitPublicAPI = mockedServers.bloqitPublicAPI;
                    });

                    afterAll(async () => {
                        await bloqitPublicAPI.stop();
                        await dhlCZAuthServer.stop();
                        await dhlCZapi.stop();
                    });

                    it('should forward a DOOR_STUCK event to DHL', () => {
                        const reqBody = {
                            rent: bloqitRentId,
                            bloq: bloqitBloqId,
                            code: 150058,
                            codeName: 'issue.disable_door_reason_door_not_close',
                            title: 'Users report - Door not closing',
                            description: 'Users reported 3 consecutive "door not closing"',
                            actionInitiatedBy: { role: 'System' },
                            metadata: { externalID: dhlCZRentId },
                        };

                        const reqTimestamp = new Date().toString();
                        const signature = createWebhookSignature({
                            reqBody,
                            timestampStr: reqTimestamp,
                            secret: coreAPIKey,
                        });

                        return Pactum.spec()
                            .post('http://localhost:3000/rents/events/door-stuck')
                            .withHeaders({
                                signature,
                                timestamp: reqTimestamp,
                                'x-partner-id': dhlPartnerFriendlyId,
                            })
                            .withBody(reqBody)
                            .expectStatus(204)
                            .toss();
                    });
                });

                describe('POST /rents/events/violence', () => {
                    let bloqitRentId: string;
                    let dhlCZRentId: string;
                    let bloqitBloqId: string;
                    let dhlCzBloqId: string;
                    let sharedBloqId: string;

                    let dhlCZapi: DHLCZAPIMockServer;
                    let dhlCZAuthServer: DHLCZAuthMockServer;
                    let bloqitPublicAPI: BloqitPublicAPIMockServer;

                    beforeEach(() => {
                        bloqitRentId = randomUUID();
                        dhlCZRentId = randomUUID();
                        bloqitBloqId = randomUUID();
                        dhlCzBloqId = randomUUID();
                        sharedBloqId = randomUUID();
                    });

                    afterEach(async () => {
                        await bloqitPublicAPI.stop();
                        await dhlCZAuthServer.stop();
                        await dhlCZapi.stop();
                    });

                    it('should forward a VIOLENCE event to DHL', async () => {
                        const mockedServers = await setupWebhookBoilerplate({
                            bloqitBloqId,
                            dhlCzBloqId,
                            sharedBloqId,
                        });

                        dhlCZapi = mockedServers.dhlCZapi;
                        dhlCZAuthServer = mockedServers.dhlCZAuthServer;
                        bloqitPublicAPI = mockedServers.bloqitPublicAPI;

                        const reqBody = {
                            rent: bloqitRentId,
                            bloq: bloqitBloqId,
                            code: 30016,
                            codeName: 'locker.spontaneousLockerOpening',
                            title: 'Spontaneous Locker Opening',
                            description: 'The locker opened spontaneously',
                            actionInitiatedBy: { role: 'System' },
                            metadata: { externalID: dhlCZRentId },
                        };

                        const reqTimestamp = new Date().toString();
                        const signature = createWebhookSignature({
                            reqBody,
                            timestampStr: reqTimestamp,
                            secret: coreAPIKey,
                        });

                        return Pactum.spec()
                            .post('http://localhost:3000/rents/events/violence')
                            .withHeaders({
                                signature,
                                timestamp: reqTimestamp,
                                'x-partner-id': dhlPartnerFriendlyId,
                            })
                            .withBody(reqBody)
                            .expectStatus(204)
                            .toss();
                    });

                    it('should handle error cases appropriately', async () => {
                        const mockedServers = await setupWebhookBoilerplate({
                            bloqitBloqId,
                            dhlCzBloqId,
                            sharedBloqId,
                            shouldFail: true,
                        });

                        dhlCZapi = mockedServers.dhlCZapi;
                        dhlCZAuthServer = mockedServers.dhlCZAuthServer;
                        bloqitPublicAPI = mockedServers.bloqitPublicAPI;

                        const reqBody = {
                            rent: bloqitRentId,
                            bloq: bloqitBloqId,
                            code: 30016,
                            codeName: 'locker.spontaneousLockerOpening',
                            title: 'Spontaneous Locker Opening',
                            description: 'The locker opened spontaneously',
                            actionInitiatedBy: { role: 'System' },
                            metadata: { externalID: dhlCZRentId },
                        };

                        const reqTimestamp = new Date().toString();
                        const signature = createWebhookSignature({
                            reqBody,
                            timestampStr: reqTimestamp,
                            secret: coreAPIKey,
                        });

                        return Pactum.spec()
                            .post('http://localhost:3000/rents/events/violence')
                            .withHeaders({
                                signature,
                                timestamp: reqTimestamp,
                                'x-partner-id': dhlPartnerFriendlyId,
                            })
                            .withBody(reqBody)
                            .expectStatus(500)
                            .toss();
                    });
                });
            });
        });

        describe('DHL IT', () => {
            const DHL_IT_API_PORT = Number(process.env.DHL_IT_API_PORT) ?? 3004;

            const DHL_IT_MIDDLEWARE_API_KEY = process.env.DHL_IT_MIDDLEWARE_API_KEY;
            if (DHL_IT_MIDDLEWARE_API_KEY === undefined) {
                throw new Error('DHL_IT_MIDDLEWARE_API_KEY not found in environment variables');
            }

            const DHL_IT_AUTH_CLIENT_ID = process.env.DHL_IT_AUTH_CLIENT_ID;
            if (DHL_IT_AUTH_CLIENT_ID === undefined) {
                throw new Error('DHL_IT_AUTH_CLIENT_ID not found in environment variables');
            }

            const DHL_IT_AUTH_CLIENT_SECRET = process.env.DHL_IT_AUTH_CLIENT_SECRET;
            if (DHL_IT_AUTH_CLIENT_SECRET === undefined) {
                throw new Error('DHL_IT_AUTH_CLIENT_SECRET not found in environment variables');
            }

            const partnerFriendlyId = process.env.DHL_IT_PARTNER_ID;
            if (partnerFriendlyId === undefined) {
                throw new Error('DHL_IT_PARTNER_ID not found in environment variables');
            }

            const partnerRentId = 'partner-rent1';

            describe('GET /rents/:partnerRentId?bloqId=:partnerBloqId', () => {
                const bloqitBloqId = randomUUID();
                const defaultHeaders = {
                    'x-partner-id': partnerFriendlyId,
                    'x-core-api-key': coreAPIKey,
                };

                let dhlITapi: DHLITAPIMockServer;
                beforeAll(async () => {
                    dhlITapi = new DHLITAPIMockServer({
                        httpInstance: await http({ port: DHL_IT_API_PORT }),
                    });
                });

                afterAll(async () => {
                    await dhlITapi.stop();
                });

                describe('customer flow', () => {
                    it("should fetch a rent registered at DHL CZ's side", async () => {
                        dhlITapi.registerGetShipmentInteraction({
                            res: {
                                status: 200,
                                body: JSON.stringify(
                                    mockedDHLITShipments.CUSTOMER_STANDARD_SHIPMENT_WITHOUT_PRIORITY,
                                ),
                            },
                        });

                        await new BloqitRentsMiddlewareResource({ host: MIDDLEWARE_HOST })
                            .getRentById({
                                partnerId: partnerFriendlyId,
                                partnerRentId,
                                bloqitBloqId,
                            })
                            .withHeaders(defaultHeaders)
                            .expectStatus(200)
                            .expectJsonLike({
                                meta: { partnerId: partnerFriendlyId },
                                rents: [
                                    {
                                        priority: 1,
                                        externalID: 'partner-rent1',
                                        dropOffCode: 'partner-rent1',
                                        dimensions: { length: 11.11, width: 440.9, height: 34.45 },
                                        prePickupActions: [],
                                        postPickupAction: [],
                                        customer: {
                                            email: '<EMAIL>',
                                            phone: '+35144995566',
                                        },
                                        metadata: {
                                            productType: 'HS4',
                                        },
                                    },
                                ],
                            })
                            .toss();
                    });

                    it('should map J1 priority to bloqit priority 2', async () => {
                        dhlITapi.registerGetShipmentInteraction({
                            res: {
                                status: 200,
                                body: JSON.stringify(mockedDHLITShipments.CUSTOMER_J1_SHIPMENT),
                            },
                        });

                        await new BloqitRentsMiddlewareResource({ host: MIDDLEWARE_HOST })
                            .getRentById({
                                partnerId: partnerFriendlyId,
                                partnerRentId,
                                bloqitBloqId,
                            })
                            .withHeaders(defaultHeaders)
                            .expectStatus(200)
                            .expectJsonLike({ rents: [{ priority: 2 }] })
                            .toss();
                    });

                    it('should map J4 priority to bloqit priority 1', async () => {
                        dhlITapi.registerGetShipmentInteraction({
                            res: {
                                status: 200,
                                body: JSON.stringify(mockedDHLITShipments.CUSTOMER_J4_SHIPMENT),
                            },
                        });

                        await new BloqitRentsMiddlewareResource({ host: MIDDLEWARE_HOST })
                            .getRentById({
                                partnerId: partnerFriendlyId,
                                partnerRentId,
                                bloqitBloqId,
                            })
                            .withHeaders(defaultHeaders)
                            .expectStatus(200)
                            .expectJsonLike({ rents: [{ priority: 1 }] })
                            .toss();
                    });

                    it('should handle error states', async () => {
                        dhlITapi.registerGetShipmentInteraction({
                            res: {
                                status: 200,
                                body: JSON.stringify({ status: 'Fail', message: 'Error message' }),
                            },
                        });

                        await new BloqitRentsMiddlewareResource({ host: MIDDLEWARE_HOST })
                            .getRentById({
                                partnerId: partnerFriendlyId,
                                partnerRentId,
                                bloqitBloqId,
                            })
                            .withHeaders(defaultHeaders)
                            .expectStatus(500)
                            .toss();
                    });

                    it('should not return a customer if email is an empty string', async () => {
                        const body = JSON.stringify(
                            mockedDHLITShipments.CUSTOMER_SHIPMENT_WITHOUT_EMAIL,
                        );

                        dhlITapi.registerGetShipmentInteraction({ res: { status: 200, body } });

                        const response = await new BloqitRentsMiddlewareResource({
                            host: MIDDLEWARE_HOST,
                        })
                            .getRentById({
                                partnerId: partnerFriendlyId,
                                partnerRentId,
                                bloqitBloqId,
                            })
                            .withHeaders(defaultHeaders)
                            .expectStatus(200)
                            .toss();

                        expect(response.body.customer).toBeUndefined();
                    });
                });

                describe('courier flow', () => {
                    const courierId = randomUUID();

                    it("should fetch a rent registered at DHL IT's side", async () => {
                        dhlITapi.registerAcquireShipmentInteraction({
                            res: {
                                status: 200,
                                body: JSON.stringify(
                                    mockedDHLITShipments.COURIER_STANDARD_SHIPMENT_WITHOUT_PRIORITY,
                                ),
                            },
                        });

                        await new BloqitRentsMiddlewareResource({ host: MIDDLEWARE_HOST })
                            .getRentById({
                                partnerId: partnerFriendlyId,
                                courierId,
                                partnerRentId,
                                bloqitBloqId,
                            })
                            .withHeaders(defaultHeaders)
                            .expectStatus(200)
                            .expectJsonLike({
                                meta: { partnerId: partnerFriendlyId },
                                rents: [
                                    {
                                        priority: 1,
                                        prePickupActions: [],
                                        postPickupAction: [],
                                        expiryDate: /2023-09-27/,
                                        externalID: 'partner-rent1',
                                        dropOffCode: 'partner-rent1',
                                        dimensions: { length: 11.11, width: 440.9, height: 34.45 },
                                        customer: { email: '<EMAIL>' },
                                        metadata: { productType: 'PKX' },
                                    },
                                ],
                            })
                            .toss();
                    });

                    it('should not return customer data if receiverEmail is not present', async () => {
                        dhlITapi.registerAcquireShipmentInteraction({
                            res: {
                                status: 200,
                                body: JSON.stringify(
                                    mockedDHLITShipments.COURIER_SHIPMENT_WITHOUT_CUSTOMER_DATA,
                                ),
                            },
                        });

                        const response = await new BloqitRentsMiddlewareResource({
                            host: MIDDLEWARE_HOST,
                        })
                            .getRentById({
                                partnerId: partnerFriendlyId,
                                courierId,
                                partnerRentId,
                                bloqitBloqId,
                            })
                            .withHeaders(defaultHeaders)
                            .expectStatus(200)
                            .toss();

                        expect(response.body.rents[0].customer).toBeUndefined();
                    });

                    it('should map J1 priority to bloqit priority 2', async () => {
                        dhlITapi.registerAcquireShipmentInteraction({
                            res: {
                                status: 200,
                                body: JSON.stringify(mockedDHLITShipments.COURIER_J1_SHIPMENT),
                            },
                        });

                        await new BloqitRentsMiddlewareResource({ host: MIDDLEWARE_HOST })
                            .getRentById({
                                courierId,
                                partnerRentId,
                                bloqitBloqId,
                                partnerId: partnerFriendlyId,
                            })
                            .withHeaders(defaultHeaders)
                            .expectStatus(200)
                            .expectJsonLike({ rents: [{ priority: 2 }] })
                            .toss();
                    });

                    it('should map J4 priority to bloqit priority 2', async () => {
                        dhlITapi.registerAcquireShipmentInteraction({
                            res: {
                                status: 200,
                                body: JSON.stringify(mockedDHLITShipments.COURIER_J4_SHIPMENT),
                            },
                        });

                        await new BloqitRentsMiddlewareResource({ host: MIDDLEWARE_HOST })
                            .getRentById({
                                courierId,
                                partnerRentId,
                                bloqitBloqId,
                                partnerId: partnerFriendlyId,
                            })
                            .withHeaders(defaultHeaders)
                            .expectStatus(200)
                            .expectJsonLike({ rents: [{ priority: 1 }] })
                            .toss();
                    });

                    it('should handle unexpected errors', async () => {
                        dhlITapi.registerAcquireShipmentInteraction({
                            res: {
                                status: 200,
                                body: JSON.stringify({
                                    status: 'Fail',
                                    message: 'Success message',
                                }),
                            },
                        });

                        await new BloqitRentsMiddlewareResource({ host: MIDDLEWARE_HOST })
                            .getRentById({
                                partnerId: partnerFriendlyId,
                                courierId,
                                partnerRentId,
                                bloqitBloqId,
                            })
                            .withHeaders(defaultHeaders)
                            .expectStatus(500)
                            .toss();
                    });
                });
            });

            describe('POST /rents/events/expired/batch', () => {
                const defaultHeaders = {
                    'x-partner-id': partnerFriendlyId,
                    'x-core-api-key': coreAPIKey,
                };

                let dhlITapi: DHLITAPIMockServer;
                beforeAll(async () => {
                    dhlITapi = new DHLITAPIMockServer({
                        httpInstance: await http({ port: DHL_IT_API_PORT }),
                    });
                });

                afterAll(async () => {
                    await dhlITapi.stop();
                });

                it('should process and forward the event', async () => {
                    dhlITapi.registerNotifyExpiredParcelInteraction({
                        res: {
                            status: 200,
                            body: JSON.stringify({ status: 'Success', errorCode: 0 }),
                        },
                    });

                    dhlITapi.registerNotifyExpiredParcelInteraction({
                        res: {
                            status: 200,
                            body: JSON.stringify({ status: 'Success', errorCode: 0 }),
                        },
                    });

                    await Pactum.spec()
                        .post(`${MIDDLEWARE_HOST}/rents/events/expired/batch`)
                        .withHeaders(defaultHeaders)
                        .withBody({ batchSize: 1 })
                        .expectStatus(204)
                        .toss();
                });
            });

            describe('Webhook events', () => {
                const bloqitRentId = randomUUID();
                const bloqitBloqId = randomUUID();
                const partnerBloqId = randomUUID();
                const dhlITRentId = randomUUID();
                const bloqitLockerId = randomUUID();
                const courierId = randomUUID();
                const courierSessionId = randomUUID();
                const baseEventData = {
                    code: 50031,
                    title: 'Rent Drop Off Confirmation',
                    codeName: 'rent.drop_off_confirmation',
                    description: 'The drop off process has concluded with success',
                    rent: bloqitRentId,
                    bloq: bloqitBloqId,
                    locker: bloqitLockerId,
                    metadata: {
                        externalID: dhlITRentId,
                        bloqExternalID: partnerBloqId,
                        rentMetadata: { productType: randomUUID() },
                    },
                    customer: {
                        email: randomUUID(),
                    },
                };

                describe('POST /rents/events/drop-off-confirmation', () => {
                    let dhlITapi: DHLITAPIMockServer;

                    beforeAll(async () => {
                        dhlITapi = new DHLITAPIMockServer({
                            httpInstance: await http({ port: DHL_IT_API_PORT }),
                        });
                    });

                    afterAll(async () => {
                        await dhlITapi.stop();
                    });

                    it('should notify a customer drop off', () => {
                        dhlITapi.registerDropOffStatusUpdateInteraction({
                            res: {
                                status: 200,
                                body: JSON.stringify({ status: 'Success', errorCode: 0 }),
                            },
                        });

                        const reqBody = {
                            ...baseEventData,
                            actionInitiatedBy: { role: 'Customer' },
                        };

                        const reqTimestamp = new Date().toString();
                        const signature = createWebhookSignature({
                            reqBody,
                            timestampStr: reqTimestamp,
                            secret: coreAPIKey,
                        });

                        return Pactum.spec()
                            .post(`${MIDDLEWARE_HOST}/rents/events/drop-off-confirmation`)
                            .withHeaders({
                                signature,
                                timestamp: reqTimestamp,
                                'x-partner-id': partnerFriendlyId,
                            })
                            .withBody(reqBody)
                            .expectStatus(204)
                            .toss();
                    });

                    it('should notify a courier drop off', () => {
                        dhlITapi.registerCheckShipmentInteraction({
                            res: {
                                status: 200,
                                body: JSON.stringify({ status: 'Success', errorCode: 0 }),
                            },
                        });

                        const reqBody = {
                            ...baseEventData,
                            actionInitiatedBy: { role: 'Courier' },
                            metadata: {
                                externalID: dhlITRentId,
                                bloqExternalID: partnerBloqId,
                                pickUpCode: '1234',
                                rentMetadata: { productType: randomUUID() },
                            },
                        };

                        const reqTimestamp = new Date().toString();
                        const signature = createWebhookSignature({
                            reqBody,
                            timestampStr: reqTimestamp,
                            secret: coreAPIKey,
                        });

                        return Pactum.spec()
                            .post(`${MIDDLEWARE_HOST}/rents/events/drop-off-confirmation`)
                            .withHeaders({
                                signature,
                                timestamp: reqTimestamp,
                                'x-partner-id': partnerFriendlyId,
                            })
                            .withBody(reqBody)
                            .expectStatus(204)
                            .toss();
                    });

                    it('should handle DHL API errors', () => {
                        dhlITapi.registerDropOffStatusUpdateInteraction({
                            res: {
                                status: 500,
                                body: JSON.stringify({ status: 'Error', errorCode: 1 }),
                            },
                        });

                        const reqBody = {
                            code: 50031,
                            title: 'Rent Drop Off Confirmation',
                            codeName: 'rent.drop_off_confirmation',
                            description: 'The drop off process has concluded with success',
                            rent: bloqitRentId,
                            bloq: bloqitBloqId,
                            locker: bloqitLockerId,
                            actionInitiatedBy: { role: 'System' },
                            metadata: {
                                externalID: dhlITRentId,
                                bloqExternalID: partnerBloqId,
                                rentMetadata: { productType: randomUUID() },
                            },
                            customer: {
                                email: randomUUID(),
                            },
                        };

                        const reqTimestamp = new Date().toString();
                        const signature = createWebhookSignature({
                            reqBody,
                            timestampStr: reqTimestamp,
                            secret: coreAPIKey,
                        });

                        return Pactum.spec()
                            .post(`${MIDDLEWARE_HOST}/rents/events/drop-off-confirmation`)
                            .withHeaders({
                                signature,
                                timestamp: reqTimestamp,
                                'x-partner-id': partnerFriendlyId,
                            })
                            .withBody(reqBody)
                            .expectStatus(500)
                            .toss();
                    });
                });

                describe('POST /rents/events/expired', () => {
                    const dhlITRentId = randomUUID();

                    let dhlITapi: DHLITAPIMockServer;
                    beforeAll(async () => {
                        dhlITapi = new DHLITAPIMockServer({
                            httpInstance: await http({ port: DHL_IT_API_PORT }),
                        });
                    });

                    afterAll(async () => {
                        await dhlITapi.stop();
                    });

                    it('should store the rent information locally', () => {
                        const reqBody = {
                            rent: bloqitRentId,
                            bloq: bloqitBloqId,
                            code: 50012,
                            codeName: 'rent.collect_item',
                            description: 'Rent has transited to COLLECT_ITEM state',
                            title: 'Rent Collect Item',
                            actionInitiatedBy: {
                                role: 'Customer',
                                id: 'customer-1234',
                                source: 'locker',
                            },
                            metadata: {
                                externalID: dhlITRentId,
                                offlinePickup: true,
                                bloqExternalID: partnerBloqId,
                                rentMetadata: { productType: randomUUID() },
                            },
                        };

                        const reqTimestamp = new Date().toString();
                        const signature = createWebhookSignature({
                            reqBody,
                            timestampStr: reqTimestamp,
                            secret: coreAPIKey,
                        });

                        return Pactum.spec()
                            .post('http://localhost:3000/rents/events/expired')
                            .withHeaders({
                                signature,
                                timestamp: reqTimestamp,
                                'x-partner-id': partnerFriendlyId,
                            })
                            .withBody(reqBody)
                            .expectStatus(204)
                            .toss();
                    });
                });

                describe('POST /rents/events/finished', () => {
                    const partnerRentId = randomUUID();
                    const rentMetadata = { productType: 'HS4' };
                    const baseRentFinishedEvent = {
                        rent: bloqitRentId,
                        bloq: bloqitBloqId,
                        code: 50010,
                        codeName: 'rent.finished',
                        description: 'Rent has transited to FINISHED state',
                        title: 'Rent Finished',
                        actionInitiatedBy: {
                            role: 'Courier',
                            id: courierId,
                            session: courierSessionId,
                        },
                    };

                    let dhlITapi: DHLITAPIMockServer;
                    beforeAll(async () => {
                        dhlITapi = new DHLITAPIMockServer({
                            httpInstance: await http({ port: DHL_IT_API_PORT }),
                        });
                    });

                    afterAll(async () => {
                        await dhlITapi.stop();
                    });

                    it('should handle regular products', () => {
                        const reqBody = {
                            ...baseRentFinishedEvent,
                            metadata: { rentMetadata, externalID: partnerRentId },
                        };

                        const reqTimestamp = new Date().toString();
                        const signature = createWebhookSignature({
                            reqBody,
                            timestampStr: reqTimestamp,
                            secret: coreAPIKey,
                        });

                        return Pactum.spec()
                            .post('http://localhost:3000/rents/events/finished')
                            .withHeaders({
                                signature,
                                timestamp: reqTimestamp,
                                'x-partner-id': partnerFriendlyId,
                            })
                            .withBody(reqBody)
                            .expectStatus(204)
                            .toss();
                    });

                    it('should handle missing products', () => {
                        const reqBody = {
                            ...baseRentFinishedEvent,
                            metadata: {
                                rentMetadata,
                                externalID: partnerRentId,
                                pickUpInfo: { success: false, errorReasons: ['missing_parcel'] },
                            },
                        };

                        const reqTimestamp = new Date().toString();
                        const signature = createWebhookSignature({
                            reqBody,
                            timestampStr: reqTimestamp,
                            secret: coreAPIKey,
                        });

                        return Pactum.spec()
                            .post('http://localhost:3000/rents/events/finished')
                            .withHeaders({
                                signature,
                                timestamp: reqTimestamp,
                                'x-partner-id': partnerFriendlyId,
                            })
                            .withBody(reqBody)
                            .expectStatus(204)
                            .toss();
                    });

                    it('should handle expired products', () => {
                        const reqBody = {
                            ...baseRentFinishedEvent,
                            metadata: {
                                rentMetadata,
                                externalID: partnerRentId,
                                pickUpInfo: { success: false, errorReasons: ['expired'] },
                            },
                        };

                        const reqTimestamp = new Date().toString();
                        const signature = createWebhookSignature({
                            reqBody,
                            timestampStr: reqTimestamp,
                            secret: coreAPIKey,
                        });

                        return Pactum.spec()
                            .post('http://localhost:3000/rents/events/finished')
                            .withHeaders({
                                signature,
                                timestamp: reqTimestamp,
                                'x-partner-id': partnerFriendlyId,
                            })
                            .withBody(reqBody)
                            .expectStatus(204)
                            .toss();
                    });

                    it('should handle unexpected product codes at finish', () => {
                        const unexpectedCode = randomUUID();
                        const reqBody = {
                            ...baseRentFinishedEvent,
                            metadata: {
                                rentMetadata,
                                externalID: partnerRentId,
                                pickUpInfo: {
                                    success: false,
                                    codeRead: unexpectedCode,
                                    errorReasons: ['different_codes'],
                                },
                            },
                        };

                        const reqTimestamp = new Date().toString();
                        const signature = createWebhookSignature({
                            reqBody,
                            timestampStr: reqTimestamp,
                            secret: coreAPIKey,
                        });

                        return Pactum.spec()
                            .post('http://localhost:3000/rents/events/finished')
                            .withHeaders({
                                signature,
                                timestamp: reqTimestamp,
                                'x-partner-id': partnerFriendlyId,
                            })
                            .withBody(reqBody)
                            .expectStatus(204)
                            .toss();
                    });
                });
            });
        });
    });
});
