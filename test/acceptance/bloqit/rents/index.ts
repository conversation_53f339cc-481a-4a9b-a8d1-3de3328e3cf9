import Pactum from 'pactum';

export class BloqitRentsMiddlewareResource {
    private readonly host: string;
    constructor(deps: { host: string }) {
        this.host = deps.host;
    }

    getRentById(props: {
        partnerId: string;
        partnerRentId: string;
        bloqitBloqId: string;
        courierId?: string;
    }) {
        const { partnerId, partnerRentId, bloqitBloqId, courierId } = props;

        let url = `${this.host}/rents/${partnerRentId}?bloqId=${bloqitBloqId}`;
        if (courierId !== undefined) url += `&courier=${courierId}`;

        return Pactum.spec().get(url).withHeaders({ 'X-Partner-ID': partnerId });
    }
}
