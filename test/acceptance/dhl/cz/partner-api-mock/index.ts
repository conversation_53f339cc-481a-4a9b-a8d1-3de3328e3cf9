import { HTTPMockServer } from 'wirepig';

export class DHLCZAuthMockServer {
    private readonly server: HTTPMockServer;

    constructor(deps: { httpInstance: HTTPMockServer }) {
        this.server = deps.httpInstance;
    }

    registerSuccessfulGetTokenInteraction() {
        this.server.mock({
            req: {
                method: 'POST',
                pathname: `/auth/realms/HIPP_ext_auth/protocol/openid-connect/token`,
            },
            res: {
                statusCode: 200,
                body: JSON.stringify({
                    access_token: 'dummy-access-token',
                    expires_in: 100000,
                    refresh_expires_in: 10000,
                    token_type: 'jwt',
                    'not-before-policy': 0,
                    scope: 'bloqit',
                }),
            },
        });
    }

    async stop() {
        await this.server.teardown();
    }
}

export class DHLCZAPIMockServer {
    private readonly server: HTTPMockServer;

    constructor(deps: { httpInstance: HTTPMockServer }) {
        this.server = deps.httpInstance;
    }

    registerGetShipmentInteraction(props: {
        res: { status?: number; statusCode?: number; headers?: any; body?: any };
    }) {
        this.server.mock({
            req: { method: 'POST', pathname: `/AccessPoint/v1/shipment` },
            res: props.res,
        });
    }

    registerEventInteraction(props: {
        res: { status?: number; statusCode?: number; headers?: any; body?: any };
    }) {
        this.server.mock({
            req: { method: 'POST', pathname: `/AccessPoint/v1/event` },
            res: props.res,
        });
    }

    async stop() {
        await this.server.teardown();
    }
}
