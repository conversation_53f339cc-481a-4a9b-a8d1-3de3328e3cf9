import { randomUUID } from 'crypto';
import Pactum from 'pactum';

export class DHLCZMiddlewareResource {
    private readonly host: string;
    private readonly friendlyId: string;
    private readonly apiKey: string;

    constructor(deps: { apiKey: string; host: string; friendlyId: string }) {
        this.apiKey = deps.apiKey;
        this.host = deps.host;
        this.friendlyId = deps.friendlyId;
    }

    configure(props: { deviceId: string; accessPointId: string; command: string }) {
        const { deviceId, accessPointId, command } = props;
        return Pactum.spec()
            .post(`${this.host}/dhl/cz/configure`)
            .withHeaders({ 'X-Partner-ID': this.friendlyId, 'X-API-Key': this.apiKey })
            .withBody({
                dataConfig: {
                    deviceId,
                    command,
                    posTerminal: { TID: 'pos-id-1234', SN: 'sn-12343' },
                    gps: { Lat: '-12.34243', Lon: '46.432432' },
                    address: {
                        street1: 'That st.',
                        street1Nr: '123',
                        postcode: '12345',
                        city: 'That City',
                        name: '<PERSON>',
                        firstName: '<PERSON>',
                        additionalName: 'Doe',
                    },
                },
                dataRequest: {
                    accessPointId,
                    externalPartner: 'Bloqit',
                    businessType: 'PPL',
                    sessionId: randomUUID(),
                },
            });
    }
}
