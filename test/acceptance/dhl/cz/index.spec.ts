import { randomUUID } from 'crypto';
import Pactum from 'pactum';
import { http } from 'wirepig';
import { BloqitPublicAPIMockServer } from '../../bloqit/public-api-mock';
import { DHLCZMiddlewareResource } from '.';
import { BloqitBloqsMiddlewareResource } from '../../bloqit/bloqs';

const MIDDLEWARE_HOST = `http://localhost:${process.env.PORT}`;
const BLOQIT_CORE_PORT = Number(process.env.BLOQIT_CORE_PORT) ?? 3003;

const DHL_CZ_MIDDLEWARE_API_KEY = process.env.DHL_CZ_MIDDLEWARE_API_KEY;
if (DHL_CZ_MIDDLEWARE_API_KEY === undefined) {
    throw new Error('DHL_CZ_MIDDLEWARE_API_KEY not found in environment variables');
}

const dhlczAPIKey = Buffer.from(DHL_CZ_MIDDLEWARE_API_KEY).toString('base64');

const BLOQIT_CORE_MIDDLEWARE_API_KEY = process.env.BLOQIT_CORE_MIDDLEWARE_API_KEY;
if (BLOQIT_CORE_MIDDLEWARE_API_KEY === undefined) {
    throw new Error('BLOQIT_CORE_MIDDLEWARE_API_KEY not found in environment variables');
}

const bloqitCoreAPIKey = Buffer.from(BLOQIT_CORE_MIDDLEWARE_API_KEY).toString('base64');

const partnerFriendlyId = process.env.DHL_CZ_PARTNER_ID;
if (partnerFriendlyId === undefined) {
    throw new Error('DHL_CZ_PARTNER_ID not found in environment variables');
}

describe('DHL CZ', () => {
    const bloqitBloqId = randomUUID();
    const sharedId = randomUUID();

    describe('configure', () => {
        const dhlCzMiddlewareResource = new DHLCZMiddlewareResource({
            host: MIDDLEWARE_HOST,
            friendlyId: partnerFriendlyId,
            apiKey: dhlczAPIKey,
        });

        let mockedBloqitPublicAPI: BloqitPublicAPIMockServer;
        beforeAll(async () => {
            mockedBloqitPublicAPI = new BloqitPublicAPIMockServer({
                httpInstance: await http({ port: BLOQIT_CORE_PORT }),
            });

            const bloqsResource = new BloqitBloqsMiddlewareResource({
                host: 'http://localhost:3000',
            });

            await bloqsResource.sendBloqCreatedEvent({
                bloqId: bloqitBloqId,
                metadata: { bloqExternalID: sharedId },
                partnerFriendlyId,
                apiKey: bloqitCoreAPIKey,
            });
        });

        afterEach(() => {
            mockedBloqitPublicAPI.reset();
        });

        afterAll(async () => {
            await mockedBloqitPublicAPI.stop();
        });

        it("should allow for activating a bloq at Bloqit's side", async () => {
            mockedBloqitPublicAPI.registerUpdateBloqInteraction({
                bloqId: bloqitBloqId,
                reqBody: { active: true },
                res: { status: 200 },
            });

            await dhlCzMiddlewareResource
                .configure({
                    deviceId: sharedId,
                    accessPointId: bloqitBloqId,
                    command: 'activate',
                })
                .expectStatus(200)
                .toss();
        });

        it("should allow for deactivating a bloq at Bloqit's side", async () => {
            mockedBloqitPublicAPI.registerUpdateBloqInteraction({
                bloqId: bloqitBloqId,
                reqBody: { active: false },
                res: { status: 200 },
            });

            await dhlCzMiddlewareResource
                .configure({
                    deviceId: sharedId,
                    accessPointId: bloqitBloqId,
                    command: 'deactivate',
                })
                .expectStatus(200)
                .toss();
        });

        it("should allow for update a bloq metadata at Bloqit's side", async () => {
            mockedBloqitPublicAPI.registerUpdateBloqInteraction({
                bloqId: bloqitBloqId,
                reqBody: { metadata: { PPL_CUST_ID: bloqitBloqId } },
                res: { status: 200 },
            });

            await dhlCzMiddlewareResource
                .configure({
                    deviceId: sharedId,
                    accessPointId: bloqitBloqId,
                    command: 'update',
                })
                .expectStatus(200)
                .toss();
        });

        it('should refuse to operate using unknown / invalid commands', async () => {
            await dhlCzMiddlewareResource
                .configure({
                    deviceId: sharedId,
                    accessPointId: bloqitBloqId,
                    command: 'invalid-command',
                })
                .expectStatus(405)
                .toss();
        });
    });
});
