export const MOCKED_DHL_CZ_SHIPMENT = {
    dataResponse: {
        status: 'OK',
        sessionId: 'ce2934dd-b0a0-41e0-a98e-d1ddf8060e3d',
        statusMessage: 'Request accepted',
        timestamp: '2019-10-18T14:04:05.185+02:0',
        statusCode: '200',
    },
    dataElement: {
        version: '0200',
        dataElementType: 'xPAN',
        parcelOriginOrganization: 'CZ-7001',
        parcelDestinationOrganization: 'CZ-7001',
        dataElementOriginOrganization: 'CZ-7001',
        distributionTimestamp: '2019-10-18T13:11:43',
        payment: { paymentStatus: 'paid' },
        general: {
            product: 'ParcelEurope.parcelconnect',
            parcelIdentifier: ['********'],
            routingCode: '2LCZ35002+********',
            timestamp: '2019-10-18T13:11:43',
        },
        xPAN: {
            addresses: {
                sender: [{ type: 'sender', email: '<EMAIL>', name: '<PERSON>' }],
            },
            features: {
                cod: { nonSepa: { amount: '607.0', currency: 'CZK' } },
                physical: {
                    grossWeight: '7.11',
                    length: '100',
                    width: '200',
                    height: '300',
                },
                identityCheck: {
                    trueOrFalse: 'true',
                    typeOfDocument: 'PIN',
                    documentIdentifcationNr: '123456',
                },
            },
            featureCodes: {
                feature: [
                    { name: ['SmartPIN'], textValue: ['ABC123'] },
                    { name: ['LabelLess'], textValue: ['true'] },
                    { name: ['BiggestPsSize'], textValue: ['XXL'] },
                ],
            },
        },
    },
};
