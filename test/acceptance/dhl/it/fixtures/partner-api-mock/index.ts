import { HTT<PERSON>ockServer } from 'wirepig';

export type InteractionResponseArgs = {
    status?: number;
    statusCode?: number;
    headers?: any;
    body?: any;
};

export class DHLITAPIMockServer {
    private readonly server: HTTPMockServer;

    constructor(deps: { httpInstance: HTTPMockServer }) {
        this.server = deps.httpInstance;
    }

    private registerPostInteraction(args: {
        pathname: string;
        res: { status?: number; statusCode?: number; headers?: any; body?: any };
    }) {
        const { pathname, res } = args;
        this.server.mock({ req: { pathname, method: 'POST' }, res });
    }

    registerGetShipmentInteraction(args: { res: InteractionResponseArgs }) {
        this.registerPostInteraction({ pathname: `/customer/dropOff`, res: args.res });
    }

    registerAcquireShipmentInteraction(args: { res: InteractionResponseArgs }) {
        this.registerPostInteraction({ pathname: `/shipment/acquire`, res: args.res });
    }

    registerDropOffStatusUpdateInteraction(args: { res: InteractionResponseArgs }) {
        this.registerPostInteraction({ pathname: `/customer/dropOffStatusUpdate`, res: args.res });
    }

    registerCreateShipmentInteraction(args: { res: InteractionResponseArgs }) {
        this.registerPostInteraction({ pathname: `/shipment/create`, res: args.res });
    }

    registerCustomerPickUpUpdateInteraction(args: { res: InteractionResponseArgs }) {
        this.registerPostInteraction({ pathname: `/customer/pickUpStatusUpdate`, res: args.res });
    }

    registerCheckShipmentInteraction(args: { res: InteractionResponseArgs }) {
        this.registerPostInteraction({ pathname: `/shipment/check`, res: args.res });
    }

    registerVerifyPinInteraction(args: { res: InteractionResponseArgs }) {
        this.registerPostInteraction({ pathname: `/pins/verify`, res: args.res });
    }

    registerNotifyExpiredParcelInteraction(args: { res: InteractionResponseArgs }) {
        this.registerPostInteraction({ pathname: `/parcel/notify`, res: args.res });
    }

    reset() {
        this.server.reset();
    }

    async stop() {
        await this.server.teardown();
    }
    
}
