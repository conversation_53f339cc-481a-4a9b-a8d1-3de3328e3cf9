{"info": {"_postman_id": "cbf0238e-fac1-4d95-b041-6d292d42895c", "name": "Partner Integrations Middleware", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "30404297", "_collection_link": "https://bloqit.postman.co/workspace/Bloqit-Workspace~9c6ddf89-1096-49cb-8b39-f366bbdcbc4b/collection/30404297-cbf0238e-fac1-4d95-b041-6d292d42895c?action=share&source=collection_link&creator=30404297"}, "item": [{"name": "DHL IT", "item": [{"name": "Courier flow", "item": [{"name": "Rent", "event": [{"listen": "test", "script": {"exec": ["", "pm.test(\"Response status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Validate the 'rents' array is present\", function () {", "    const responseData = pm.response.json();", "", "    pm.expect(responseData.rents).to.exist.and.to.be.an('array');", "    pm.expect(responseData.rents.length).to.be.at.least(1, \"'rents' array should contain at least one element\");", "});", "", "pm.test(\"External ID is correct\", function () {", "    const responseData = pm.response.json();", "", "    pm.expect(responseData.rents).to.be.an('array');", "    responseData.rents.forEach(function (rent) {", "        pm.expect(rent.externalID).to.be.a('string').to.be.eq(\"9C11441044680\")", "    });", "});", "", "", "pm.test(\"Drop-off code contains the correct value\", function () {", "    const responseData = pm.response.json();", "", "    pm.expect(responseData.rents).to.be.an('array');", "", "    responseData.rents.forEach(function (rent) {", "        pm.expect(rent.dropOffCode).to.be.a('string').to.be.eq(\"9C11441044680\");", "    });", "});", "", "pm.test(\"Dimensions contains value\", function () {", "    const responseData = pm.response.json();", "", "    pm.expect(responseData.rents[0].dimensions.length).not.empty", "    pm.expect(responseData.rents[0].dimensions.width).not.empty", "    pm.expect(responseData.rents[0].dimensions.height).not.empty", "", "});", "", "pm.test(\"Response time is in an acceptable range\", function () {", "    pm.expect(pm.response.responseTime)", "        .to.be", "        .below(Number.parseInt(pm.environment.get('response_time_slo')));", "});", ""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "x-partner-id", "value": "{{dhl_it_partner_id}}", "type": "text"}, {"key": "x-core-api-key", "value": "{{core_api_key}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/rents/{{courier_drop_off_code}}?bloqId={{bloq_external_id}}&courier=Test", "host": ["{{url}}"], "path": ["rents", "{{courier_drop_off_code}}"], "query": [{"key": "bloqId", "value": "{{bloq_external_id}}"}, {"key": "courier", "value": "Test"}]}}, "response": []}, {"name": "Drop-off confirmation", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 204\", function () {", "    pm.response.to.have.status(204);", "});", "", "pm.test(\"Response time is in an acceptable range\", function () {", "    pm.expect(pm.response.responseTime).to.be", "        .below(Number.parseInt(pm.environment.get('response_time_slo')));", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "x-partner-id", "value": "6523f797bc202b39006f5136", "type": "text"}, {"key": "x-core-api-key", "value": "c3EwdWttNGZxc254MGpoZjZldmVxNXcxdTFlNm9taXI=", "type": "text"}, {"key": "signature", "value": "eyJhbGciOiJIUzI1NiJ9.MjAyMy0xMC0xMlQwMDowMDowMFoueyJjb2RlIjo1MDAzMSwidGl0bGUiOiJSZW50IERyb3AgT2ZmIENvbmZpcm1hdGlvbiIsImNvZGVOYW1lIjoicmVudC5kcm9wX29mZl9jb25maXJtYXRpb24iLCJkZXNjcmlwdGlvbiI6IlRoZSBkcm9wIG9mZiBwcm9jZXNzIGhhcyBjb25jbHVkZWQgd2l0aCBzdWNjZXNzIiwicmVudCI6IkVGODIwMjMwOTE1MDAiLCJibG9xIjoiNjUyM2Y4NDFiYzIwMmIzOTAwNmY1MjRkIiwibG9ja2VyIjoiYmxvcWl0TG9ja2VySWQiLCJtZXRhZGF0YSI6eyJleHRlcm5hbElEIjoiRUY4MjAyMzA5MTUwMCIsImJsb3FFeHRlcm5hbElEIjoiTElUMzMiLCJwaWNrVXBDb2RlIjoiMTIzNCJ9LCJhY3Rpb25Jbml0aWF0ZWRCeSI6eyJyb2xlIjoiQ291cmllciJ9LCJ0aW1lc3RhbXAiOiIyMDIzLTEwLTEyVDE3OjAwOjA2LjkxMloifQ.w-LLvjYr6PjVYpSoAI0ZVTScU0TPo5pPU30FoWkz-wg", "type": "text"}, {"key": "timestamp", "value": "2023-10-18T00:00:00Z", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"code\": 50031,\n    \"title\": \"Rent Drop Off Confirmation\",\n    \"codeName\": \"rent.drop_off_confirmation\",\n    \"description\": \"The drop off process has concluded with success\",\n    \"rent\": \"EF82023091500\",\n    \"bloq\": \"6523f841bc202b39006f524d\",\n    \"locker\": \"bloqitLockerId\",\n    \"metadata\": {\n        \"externalID\": \"EF82023091500\",\n        \"bloqExternalID\": \"LIT33\",\n        \"pickUpCode\":\"1234\"\n    },\n    \"actionInitiatedBy\": {\n        \"role\": \"Courier\"\n    },\n    \"timestamp\": \"2023-10-12T17:00:06.912Z\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/rents/events/drop-off-confirmation", "host": ["{{url}}"], "path": ["rents", "events", "drop-off-confirmation"]}}, "response": []}, {"name": "Authenticate", "event": [{"listen": "test", "script": {"exec": ["", "pm.test(\"Response status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "", "pm.test(\"Response has the required fields\", function () {", "    const responseData = pm.response.json();", "", "    pm.expect(responseData).to.be.an('object');", "    pm.expect(responseData.success).to.exist;", "    pm.expect(responseData.meta).to.exist;", "});", "", "", "pm.test(\"Partner ID is correct\", function () {", "    const responseData = pm.response.json();", "", "    pm.expect(responseData.meta.partnerId).to.exist.and.to.be.a('string').and.eq(\"6523f797bc202b39006f5136\");", "});", "", "", "pm.test(\"Verify that the 'success' field is true\", function () {", "    const responseData = pm.response.json();", "", "    pm.expect(responseData.success).to.be.true;", "});", "", "", "pm.test(\"Response time is in an acceptable range\", function () {", "    pm.expect(pm.response.responseTime).to.be", "        .below(Number.parseInt(pm.environment.get('response_time_slo')));", "});", "", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "x-partner-id", "value": "{{dhl_it_partner_id}}", "type": "text"}, {"key": "x-core-api-key", "value": "{{core_api_key}}", "type": "text"}], "body": {"mode": "raw", "raw": "{ \n    \"bloq\":\"6523f841bc202b39006f524d\",\n    \"bloqExternalID\":\"LIT33\", \n    \"session\": \"ba77480d-e853-42a4-a50a-ededff4ddddc\", \n    \"pin\": \"343642\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/couriers/authenticate", "host": ["{{url}}"], "path": ["couriers", "authenticate"]}}, "response": []}, {"name": "Pick-up confirmation", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 204\", function () {", "    pm.response.to.have.status(204);", "});", "", "pm.test(\"Response time is in an acceptable range\", function () {", "    pm.expect(pm.response.responseTime).to.be", "        .below(Number.parseInt(pm", "            .environment", "            .get('pick_up_confirmation_response_time_slo'))", "        );", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "x-partner-id", "value": "6523f797bc202b39006f5136", "type": "text"}, {"key": "x-core-api-key", "value": "c3EwdWttNGZxc254MGpoZjZldmVxNXcxdTFlNm9taXI=", "type": "text"}, {"key": "signature", "value": "eyJhbGciOiJIUzI1NiJ9.MjAyMy0xMC0xMlQwMDowMDowMFoueyJjb2RlIjo1MDAxMiwiY29kZU5hbWUiOiJyZW50LmNvbGxlY3RfaXRlbSIsImRlc2NyaXB0aW9uIjoiUmVudCBoYXMgdHJhbnNpdGVkIHRvIENPTExFQ1RfSVRFTSBzdGF0ZSIsInRpdGxlIjoiUmVudCBDb2xsZWN0IEl0ZW0iLCJyZW50IjoiOUMxMTQ0MTA0NDY4MCIsImJsb3EiOiI2NTIzZjg0MWJjMjAyYjM5MDA2ZjUyNGQiLCJtZXRhZGF0YSI6eyJleHRlcm5hbElEIjoiOUMxMTQ0MTA0NDY4MCIsImJsb3FFeHRlcm5hbElEIjoiTElUMzMifSwiYWN0aW9uSW5pdGlhdGVkQnkiOnsicm9sZSI6IkNvdXJpZXIiLCJpZCI6ImNvdXJpZXItMTIzNCIsInNvdXJjZSI6ImxvY2tlciIsInNlc3Npb24iOiJzZXNzaW9uLTEyMzQifSwidGltZXN0YW1wIjoiMjAyMy0xMC0xMlQxNzowMDowNi45MTJaIn0.jFid2vhAnZts2Q7Mk6SGH-HuDNo2FMXs5bAww5whwsI", "type": "text"}, {"key": "timestamp", "value": "2023-10-18T00:00:00Z", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"code\": 50012,\n    \"codeName\": \"rent.collect_item\",\n    \"description\": \"<PERSON><PERSON> has transited to COLLECT_ITEM state\",\n    \"title\": \"Rent Collect Item\",\n    \"rent\": \"9C11441044680\",\n    \"bloq\": \"6523f841bc202b39006f524d\",\n    \"metadata\": {\n        \"externalID\": \"9C11441044680\",\n        \"bloqExternalID\": \"LIT33\"\n    },\n    \"actionInitiatedBy\": {\n        \"role\": \"Courier\",\n        \"id\": \"courier-1234\",\n        \"source\": \"locker\",\n        \"session\": \"session-1234\"\n    },\n    \"timestamp\": \"2023-10-12T17:00:06.912Z\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/rents/events/collect-item", "host": ["{{url}}"], "path": ["rents", "events", "collect-item"]}}, "response": []}]}, {"name": "Customer", "item": [{"name": "Rent", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["var dhl_it_rent_id = pm.environment.get('customer_rent_external_id');", "", "", "pm.test(\"Response status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Validate the 'rents' array is present\", function () {", "    const responseData = pm.response.json();", "", "    pm.expect(responseData.rents).to.exist.and.to.be.an('array');", "    pm.expect(responseData.rents.length).to.be.at.least(1, \"'rents' array should contain at least one element\");", "});", "", "pm.test(\"External ID is correct\", function () {", "    const responseData = pm.response.json();", "", "    pm.expect(responseData.rents).to.be.an('array');", "    responseData.rents.forEach(function (rent) {", "        pm.expect(rent.externalID).to.be.a('string').to.be.eq(dhl_it_rent_id)", "    });", "});", "", "", "pm.test(\"Drop-off code contains the correct value\", function () {", "    const responseData = pm.response.json();", "", "    pm.expect(responseData.rents).to.be.an('array');", "", "    responseData.rents.forEach(function (rent) {", "        pm.expect(rent.dropOffCode).to.be.a('string').to.be.eq(dhl_it_rent_id);", "    });", "});", "", "pm.test(\"Response time is in an acceptable range\", function () {", "    pm.expect(pm.response.responseTime).to.be", "        .below(Number.parseInt(pm.environment.get('response_time_slo')));", "});", "", ""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "x-partner-id", "value": "{{dhl_it_partner_id}}", "type": "text"}, {"key": "x-core-api-key", "value": "{{core_api_key}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/rents/{{customer_rent_external_id}}?bloqId={{bloq_external_id}}", "host": ["{{url}}"], "path": ["rents", "{{customer_rent_external_id}}"], "query": [{"key": "bloqId", "value": "{{bloq_external_id}}"}]}}, "response": []}, {"name": "Drop-off confirmation", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 204\", function () {", "    pm.response.to.have.status(204);", "});", "", "pm.test(\"Response time is in an acceptable range\", function () {", "    pm.expect(pm.response.responseTime).to.be", "        .below(Number.parseInt(pm.environment.get('response_time_slo')));", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "x-partner-id", "value": "6523f797bc202b39006f5136", "type": "text"}, {"key": "x-core-api-key", "value": "c3EwdWttNGZxc254MGpoZjZldmVxNXcxdTFlNm9taXI=", "type": "text"}, {"key": "signature", "value": "eyJhbGciOiJIUzI1NiJ9.MjAyMy0xMC0xOFQwMDowMDowMFoueyJjb2RlIjo1MDAzMSwidGl0bGUiOiJSZW50IERyb3AgT2ZmIENvbmZpcm1hdGlvbiIsImNvZGVOYW1lIjoicmVudC5kcm9wX29mZl9jb25maXJtYXRpb24iLCJkZXNjcmlwdGlvbiI6IlRoZSBkcm9wIG9mZiBwcm9jZXNzIGhhcyBjb25jbHVkZWQgd2l0aCBzdWNjZXNzIiwicmVudCI6IkVGODIwMjMwOTE1MDAiLCJibG9xIjoiNjUyM2Y4NDFiYzIwMmIzOTAwNmY1MjRkIiwibG9ja2VyIjoiYmxvcWl0TG9ja2VySWQiLCJtZXRhZGF0YSI6eyJleHRlcm5hbElEIjoiRUY4MjAyMzA5MTUwMCIsImJsb3FFeHRlcm5hbElEIjoiTElUMzMifSwiYWN0aW9uSW5pdGlhdGVkQnkiOnsicm9sZSI6IkN1c3RvbWVyIn19._GYfCYaBlO6Ze9JBoQay6h7kisPzeFDbzqERJuXcIW4", "type": "text"}, {"key": "timestamp", "value": "2023-10-18T00:00:00Z", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"code\": 50031,\n    \"title\": \"Rent Drop Off Confirmation\",\n    \"codeName\": \"rent.drop_off_confirmation\",\n    \"description\": \"The drop off process has concluded with success\",\n    \"rent\": \"EF82023091500\",\n    \"bloq\":\"6523f841bc202b39006f524d\",\n    \"locker\": \"bloqitLockerId\",\n    \"metadata\": {\n        \"externalID\": \"EF82023091500\",\n        \"bloqExternalID\": \"LIT33\"\n    },\n    \"actionInitiatedBy\": {\n        \"role\": \"Customer\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/rents/events/drop-off-confirmation", "host": ["{{url}}"], "path": ["rents", "events", "drop-off-confirmation"]}}, "response": []}, {"name": "Pick-up confirmation", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 204\", function () {", "    pm.response.to.have.status(204);", "});", "", "pm.test(\"Response time is in an acceptable range\", function () {", "    pm.expect(pm.response.responseTime).to.be", "        .below(Number.parseInt(pm", "            .environment", "            .get('pick_up_confirmation_response_time_slo'))", "        );", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "x-partner-id", "value": "6523f797bc202b39006f5136", "type": "text"}, {"key": "x-core-api-key", "value": "c3EwdWttNGZxc254MGpoZjZldmVxNXcxdTFlNm9taXI=", "type": "text"}, {"key": "signature", "value": "eyJhbGciOiJIUzI1NiJ9.MjAyMy0xMC0xMlQwMDowMDowMFoueyJjb2RlIjo1MDAxMiwiY29kZU5hbWUiOiJyZW50LmNvbGxlY3RfaXRlbSIsImRlc2NyaXB0aW9uIjoiUmVudCBoYXMgdHJhbnNpdGVkIHRvIENPTExFQ1RfSVRFTSBzdGF0ZSIsInRpdGxlIjoiUmVudCBDb2xsZWN0IEl0ZW0iLCJyZW50IjoiOUMxMTQ0MTA0NDY4MCIsImJsb3EiOiI2NTIzZjg0MWJjMjAyYjM5MDA2ZjUyNGQiLCJtZXRhZGF0YSI6eyJleHRlcm5hbElEIjoiOUMxMTQ0MTA0NDY4MCIsImJsb3FFeHRlcm5hbElEIjoiTElUMzMifSwiYWN0aW9uSW5pdGlhdGVkQnkiOnsicm9sZSI6IkN1c3RvbWVyIiwiaWQiOiJjb3VyaWVyLTEyMzQiLCJzb3VyY2UiOiJsb2NrZXIiLCJzZXNzaW9uIjoic2Vzc2lvbi0xMjM0In0sInRpbWVzdGFtcCI6IjIwMjMtMTAtMTJUMTc6MDA6MDYuOTEyWiJ9.5qt9w6fTWXOiJg70No5M8xmEdRsBzgeDHG7_SsN10v0", "type": "text"}, {"key": "timestamp", "value": "2023-10-18T00:00:00Z", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"code\": 50012,\n    \"codeName\": \"rent.collect_item\",\n    \"description\": \"<PERSON><PERSON> has transited to COLLECT_ITEM state\",\n    \"title\": \"Rent Collect Item\",\n    \"rent\": \"9C11441044680\",\n    \"bloq\": \"6523f841bc202b39006f524d\",\n    \"metadata\": {\n        \"externalID\": \"9C11441044680\",\n        \"bloqExternalID\": \"LIT33\"\n    },\n    \"actionInitiatedBy\": {\n        \"role\": \"Customer\",\n        \"id\": \"courier-1234\",\n        \"source\": \"locker\",\n        \"session\": \"session-1234\"\n    },\n    \"timestamp\": \"2023-10-12T17:00:06.912Z\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/rents/events/collect-item", "host": ["{{url}}"], "path": ["rents", "events", "collect-item"]}}, "response": []}]}], "description": "Middleware implementation of DHL IT's API client, based on their specification documents."}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "dropOffCode", "value": "", "type": "string"}, {"key": "bloqExternalID", "value": "", "type": "string"}, {"key": "urlDemo", "value": "", "type": "string"}, {"key": "urlLocal", "value": "", "type": "string"}, {"key": "x-core-api-key_demo", "value": "", "type": "string"}, {"key": "x-core-api-key_local", "value": "", "type": "string"}, {"key": "x-partner-id_demo", "value": "", "type": "string"}, {"key": "x-partner-id_local", "value": "", "type": "string"}, {"key": "dropOffCode_Courier", "value": "", "type": "string"}]}