import Pactum from 'pactum';
import http from 'http';
import { http as mockHTTP } from 'wirepig';
import mongoose from 'mongoose';
import * as jwt from 'jsonwebtoken';
import { randomUUID } from 'crypto';
import { MongoMemoryServer } from 'mongodb-memory-server';

import * as ENV from '../../src/core/env';
import * as APP from '../../src/http/app';
import { DHLITAPIMockServer } from '../acceptance/dhl/it/fixtures/partner-api-mock';

const APPLICATION_HOST = `http://localhost:${process.env.PORT}`;

const BLOQIT_CORE_MIDDLEWARE_API_KEY = process.env.BLOQIT_CORE_MIDDLEWARE_API_KEY;
if (BLOQIT_CORE_MIDDLEWARE_API_KEY === undefined) {
    throw new Error('BLOQIT_CORE_MIDDLEWARE_API_KEY not found in environment variables');
}

const coreAPIKey = Buffer.from(BLOQIT_CORE_MIDDLEWARE_API_KEY).toString('base64');

describe('Bloqit', () => {
    describe('rents', () => {
        describe('DHL IT', () => {
            const DHL_IT_API_PORT = Number(process.env.DHL_IT_API_PORT) ?? 3004;

            const DHL_IT_MIDDLEWARE_API_KEY = process.env.DHL_IT_MIDDLEWARE_API_KEY;
            if (DHL_IT_MIDDLEWARE_API_KEY === undefined) {
                throw new Error('DHL_IT_MIDDLEWARE_API_KEY not found in environment variables');
            }

            const DHL_IT_AUTH_CLIENT_ID = process.env.DHL_IT_AUTH_CLIENT_ID;
            if (DHL_IT_AUTH_CLIENT_ID === undefined) {
                throw new Error('DHL_IT_AUTH_CLIENT_ID not found in environment variables');
            }

            const DHL_IT_AUTH_CLIENT_SECRET = process.env.DHL_IT_AUTH_CLIENT_SECRET;
            if (DHL_IT_AUTH_CLIENT_SECRET === undefined) {
                throw new Error('DHL_IT_AUTH_CLIENT_SECRET not found in environment variables');
            }

            const partnerFriendlyId = process.env.DHL_IT_PARTNER_ID;
            if (partnerFriendlyId === undefined) {
                throw new Error('DHL_IT_PARTNER_ID not found in environment variables');
            }

            let app: any;
            let appServer: any;

            describe('expired rents', () => {
                const defaultHeaders = {
                    'x-partner-id': partnerFriendlyId,
                    'x-core-api-key': coreAPIKey,
                };

                let dhlITapi: DHLITAPIMockServer;
                let dbClient: mongoose.Connection;
                beforeAll(async () => {
                    await MongoMemoryServer.create({
                        instance: { port: 56930, dbName: process.env.MONGO_DB_NAME },
                    });

                    dhlITapi = new DHLITAPIMockServer({
                        httpInstance: await mockHTTP({ port: DHL_IT_API_PORT }),
                    });

                    const env = ENV.setup();
                    app = await APP.setup({ env });

                    await new Promise<void>(resolve => {
                        appServer = http.createServer(app).listen(process.env.PORT, () => {
                            console.log(`Server running at ${process.env.PORT?.toString()} 🚀`);
                            resolve();
                        });
                    });

                    dbClient = mongoose.connection;

                    await Pactum.spec()
                        .post(`${APPLICATION_HOST}/healthcheck`)
                        .expectStatus(200)
                        .toss();
                });

                beforeEach(async () => {
                    await dbClient.collection('expiredrents').deleteMany();
                    await dbClient.collection('expiredrentsnotificationbatches').deleteMany();
                });

                afterAll(async () => {
                    appServer.close();
                    await dhlITapi.stop();
                });

                it('should store the rent in the database to be dispatched later', async () => {
                    function createWebhookSignature(props: {
                        reqBody: any;
                        timestampStr: string;
                        secret: string;
                    }) {
                        const { reqBody, timestampStr, secret } = props;
                        return jwt.sign(`${timestampStr}.${JSON.stringify(reqBody)}`, secret);
                    }

                    const bloqitRentId = randomUUID();
                    const bloqitBloqId = randomUUID();
                    const partnerBloqId = randomUUID();
                    const dhlITRentId = randomUUID();

                    const reqBody = {
                        rent: bloqitRentId,
                        bloq: bloqitBloqId,
                        code: 50012,
                        codeName: 'rent.collect_item',
                        description: 'Rent has transited to COLLECT_ITEM state',
                        title: 'Rent Collect Item',
                        actionInitiatedBy: {
                            role: 'Customer',
                            id: 'customer-1234',
                            source: 'locker',
                        },
                        metadata: {
                            externalID: dhlITRentId,
                            offlinePickup: true,
                            bloqExternalID: partnerBloqId,
                            rentMetadata: { productType: randomUUID() },
                        },
                    };

                    const reqTimestamp = new Date().toString();
                    const signature = createWebhookSignature({
                        reqBody,
                        timestampStr: reqTimestamp,
                        secret: coreAPIKey,
                    });

                    await Pactum.spec()
                        .post(`${APPLICATION_HOST}/rents/events/expired`)
                        .withHeaders({
                            signature,
                            timestamp: reqTimestamp,
                            'x-partner-id': partnerFriendlyId,
                        })
                        .withBody(reqBody)
                        .expectStatus(204)
                        .toss();

                    const rents = await dbClient.collection('expiredrents').find().toArray();
                    expect(rents).toHaveLength(1);

                    const [storedRent] = rents;
                    expect(storedRent.rentId).toEqual(bloqitRentId);
                });

                describe('failures on IT side', () => {
                    it('should keep the rent in the database after dispatching it', async () => {
                        function createWebhookSignature(props: {
                            reqBody: any;
                            timestampStr: string;
                            secret: string;
                        }) {
                            const { reqBody, timestampStr, secret } = props;
                            return jwt.sign(`${timestampStr}.${JSON.stringify(reqBody)}`, secret);
                        }

                        const bloqitRentId = randomUUID();
                        const bloqitBloqId = randomUUID();
                        const partnerBloqId = randomUUID();
                        const dhlITRentId = randomUUID();

                        const reqBody = {
                            rent: bloqitRentId,
                            bloq: bloqitBloqId,
                            code: 50012,
                            codeName: 'rent.collect_item',
                            description: 'Rent has transited to COLLECT_ITEM state',
                            title: 'Rent Collect Item',
                            actionInitiatedBy: {
                                role: 'Customer',
                                id: 'customer-1234',
                                source: 'locker',
                            },
                            metadata: {
                                externalID: dhlITRentId,
                                offlinePickup: true,
                                bloqExternalID: partnerBloqId,
                                rentMetadata: { productType: randomUUID() },
                            },
                        };

                        const reqTimestamp = new Date().toString();
                        const signature = createWebhookSignature({
                            reqBody,
                            timestampStr: reqTimestamp,
                            secret: coreAPIKey,
                        });

                        await Pactum.spec()
                            .post(`${APPLICATION_HOST}/rents/events/expired`)
                            .withHeaders({
                                signature,
                                timestamp: reqTimestamp,
                                'x-partner-id': partnerFriendlyId,
                            })
                            .withBody(reqBody)
                            .expectStatus(204)
                            .toss();

                        dhlITapi.registerNotifyExpiredParcelInteraction({
                            res: {
                                statusCode: 500,
                                body: JSON.stringify({ status: 'Error', errorCode: 0 }),
                            },
                        });

                        await Pactum.spec()
                            .post(`${APPLICATION_HOST}/rents/events/expired/batch`)
                            .withHeaders(defaultHeaders)
                            .withBody({ batchSize: 1 })
                            .expectStatus(204)
                            .toss();

                        const rents = await dbClient.collection('expiredrents').find().toArray();
                        expect(rents).toHaveLength(1);

                        const [storedRent] = rents;
                        expect(storedRent.rentId).toEqual(bloqitRentId);
                    });
                });
            });
        });
    });
});
