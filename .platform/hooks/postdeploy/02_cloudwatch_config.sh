#!/bin/bash
set -e
if [ -z "$METRICS_ENV" ]; then
  echo "METRICS_ENV is not defined"
  exit 1
fi

SOURCE_TEMPLATE=".platform/files/amazon-cloudwatch-agent.json.template"
TEMP_CONFIG="/tmp/amazon-cloudwatch-agent.json"
FINAL_DEST="/opt/aws/amazon-cloudwatch-agent/etc/amazon-cloudwatch-agent.json"
TOKEN=$(curl -sS -X PUT "http://169.254.169.254/latest/api/token" -H "X-aws-ec2-metadata-token-ttl-seconds: 21600") 
INSTANCE_ID=$(curl -sS -H "X-aws-ec2-metadata-token: $TOKEN" http://169.254.169.254/latest/meta-data/instance-id)


sudo mkdir -p "$(dirname "$FINAL_DEST")"
sed -i "s/\\\${aws:InstanceId}/$INSTANCE_ID/" "$SOURCE_TEMPLATE" > "$TEMP_CONFIG"
sed "s/\"namespace\": *\"[^\"]*\"/\"namespace\": \"PIS-${METRICS_ENV}\"/" "$SOURCE_TEMPLATE" > "$TEMP_CONFIG"
sudo cp "$TEMP_CONFIG" "$FINAL_DEST"
sudo chmod +x "$FINAL_DEST"
sudo /opt/aws/amazon-cloudwatch-agent/bin/amazon-cloudwatch-agent-ctl -a fetch-config -m ec2 -c file:"$FINAL_DEST" -s

