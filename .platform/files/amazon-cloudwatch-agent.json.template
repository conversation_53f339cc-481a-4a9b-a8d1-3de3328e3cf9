{"agent": {"metrics_collection_interval": 60, "logfile": "/opt/aws/amazon-cloudwatch-agent/logs/amazon-cloudwatch-agent.log"}, "metrics": {"namespace": "PIS-{{METRICS_ENV}}", "metrics_collected": {"mem": {"measurement": ["mem_used_percent"], "append_dimensions": {"InstanceId": "${aws:InstanceId}"}, "metrics_collection_interval": 60}, "disk": {"measurement": ["disk_used_percent"], "metrics_collection_interval": 60, "append_dimensions": {"InstanceId": "${aws:InstanceId}"}, "resources": ["/"]}, "cpu": {"measurement": ["cpu_usage_user", "cpu_usage_system"], "metrics_collection_interval": 60, "totalcpu": true, "append_dimensions": {"InstanceId": "${aws:InstanceId}"}, "resources": ["*"]}, "net": {"measurement": ["bytes_sent", "bytes_recv"], "metrics_collection_interval": 60, "append_dimensions": {"InstanceId": "${aws:InstanceId}"}, "resources": ["*"]}}}}