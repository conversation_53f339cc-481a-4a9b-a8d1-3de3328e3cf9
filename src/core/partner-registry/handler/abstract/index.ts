import { type <PERSON><PERSON><PERSON><PERSON> } from '../..';
import {
    type BloqChangeState,
    type BloqChangeStateResponse,
    type BloqEvent,
} from '../../../../bloqit/bloqs/models';
import { CourierAccessCodesResponse } from '../../../../bloqit/bloqs/models/courier-access-codes';
import { type AuthenticationResult } from '../../../../bloqit/couriers/models/authentication-result';
import type {
    LockerMaintenanceStatusUpdateEvent,
    LockerChangeState,
    LockerChangeStateResponse,
} from '../../../../bloqit/lockers/models';
import {
    type RentEvent,
    type Rent,
    type OperationType,
} from '../../../../bloqit/rents/models/rent';

export class MethodInvocationOutOfContextError extends Error {
    constructor(deps: { methodName: string; partnerId: string }) {
        const { methodName, partnerId } = deps;
        super(
            `${partnerId} has no corresponding method  implementation for the following method ${methodName}`,
        );
    }
}

export abstract class AbstractPartnerHandler implements PartnerHandler {
    protected readonly partnerFriendlyId: string;

    constructor(props: { partnerFriendlyId: string }) {
        this.partnerFriendlyId = props.partnerFriendlyId;
    }

    async notifyExpiredRents(_args: { batchSize: number }): Promise<void> {
        throw new MethodInvocationOutOfContextError({
            methodName: 'notifyExpiredRents',
            partnerId: this.partnerFriendlyId,
        });
    }

    async notifyCollectItem(_event: RentEvent): Promise<void> {
        throw new MethodInvocationOutOfContextError({
            methodName: 'notifyCollectItem',
            partnerId: this.partnerFriendlyId,
        });
    }

    async getRentById(_props: {
        partnerRentId: string;
        bloqId: string;
        courier?: string;
        locale: string;
    }): Promise<Rent[]> {
        throw new MethodInvocationOutOfContextError({
            methodName: 'getRentById',
            partnerId: this.partnerFriendlyId,
        });
    }

    async canBePickedUp(_props: {
        partnerRentId: string;
        bloqId: string;
        operationType: OperationType;
    }): Promise<boolean> {
        throw new MethodInvocationOutOfContextError({
            methodName: 'canBePickedUp',
            partnerId: this.partnerFriendlyId,
        });
    }

    async setConfig(
        _changeState: BloqChangeState | LockerChangeState,
    ): Promise<BloqChangeStateResponse | LockerChangeStateResponse> {
        throw new MethodInvocationOutOfContextError({
            methodName: 'setConfig',
            partnerId: this.partnerFriendlyId,
        });
    }

    async authenticateCourier(_args: {
        pin: string;
        bloq?: string;
        bloqExternalId?: string;
        courierSessionId?: string;
    }): Promise<AuthenticationResult> {
        throw new MethodInvocationOutOfContextError({
            methodName: 'authenticateCourier',
            partnerId: this.partnerFriendlyId,
        });
    }

    async notifyDropOffConfirmation(_event: RentEvent): Promise<void> {
        throw new MethodInvocationOutOfContextError({
            methodName: 'notifyDropOffConfirmation',
            partnerId: this.partnerFriendlyId,
        });
    }

    async notifyDropOffCancellation(_event: RentEvent): Promise<void> {
        throw new MethodInvocationOutOfContextError({
            methodName: 'notifyDropOffCancellation',
            partnerId: this.partnerFriendlyId,
        });
    }

    async notifyExpiredRent(_event: RentEvent): Promise<void> {
        throw new MethodInvocationOutOfContextError({
            methodName: 'notifyExpiredRent',
            partnerId: this.partnerFriendlyId,
        });
    }

    async notifyStuckRent(_event: RentEvent): Promise<void> {
        throw new MethodInvocationOutOfContextError({
            methodName: 'notifyStuckRent',
            partnerId: this.partnerFriendlyId,
        });
    }

    async notifyDirtyDoor(_event: RentEvent): Promise<void> {
        throw new MethodInvocationOutOfContextError({
            methodName: 'notifyDirtyDoor',
            partnerId: this.partnerFriendlyId,
        });
    }

    async notifyViolence(_event: RentEvent): Promise<void> {
        throw new MethodInvocationOutOfContextError({
            methodName: 'notifyViolence',
            partnerId: this.partnerFriendlyId,
        });
    }

    async notifyPing(_event: BloqChangeState): Promise<void> {
        throw new MethodInvocationOutOfContextError({
            methodName: 'notifyPing',
            partnerId: this.partnerFriendlyId,
        });
    }

    async associateLabel(_props: {
        partnerRentId: string;
        bloqId: string;
        operationType: OperationType;
        courier: string;
        locale: string;
        code: string;
        bloqExternalId?: string;
        country?: string;
    }): Promise<boolean> {
        throw new MethodInvocationOutOfContextError({
            methodName: 'canBePickedUp',
            partnerId: this.partnerFriendlyId,
        });
    }

    async handleCourierLogout(_event: BloqEvent): Promise<void> {
        throw new MethodInvocationOutOfContextError({
            methodName: 'handleCourierLogout',
            partnerId: this.partnerFriendlyId,
        });
    }

    async handleRentFinishedEvent(_event: RentEvent): Promise<void> {
        throw new MethodInvocationOutOfContextError({
            methodName: 'handleRentFinishedEvent',
            partnerId: this.partnerFriendlyId,
        });
    }

    async handleRentStartingEvent(_event: RentEvent): Promise<void> {
        throw new MethodInvocationOutOfContextError({
            methodName: 'handleRentStartingEvent',
            partnerId: this.partnerFriendlyId,
        });
    }

    async handleRentFlowInterruptedEvent(_event: RentEvent): Promise<void> {
        throw new MethodInvocationOutOfContextError({
            methodName: 'handleRentFlowInterruptedEvent',
            partnerId: this.partnerFriendlyId,
        });
    }

    async getCouriersAccessCodes(args: {
        bloqId: string;
        bloqExternalId?: string;
        carrier?: string;
        country?: string;
    }): Promise<CourierAccessCodesResponse> {
        throw new MethodInvocationOutOfContextError({
            methodName: 'getCouriersAccessCodes',
            partnerId: this.partnerFriendlyId,
        });
    }

    async maintenanceStatusUpdate(_event: LockerMaintenanceStatusUpdateEvent): Promise<void> {
        throw new MethodInvocationOutOfContextError({
            methodName: 'maintenanceStatusUpdate',
            partnerId: this.partnerFriendlyId,
        });
    }

    async handleBloqCreatedEvent(_event: BloqEvent): Promise<void> {
        throw new MethodInvocationOutOfContextError({
            methodName: 'handleBloqCreatedEvent',
            partnerId: this.partnerFriendlyId,
        });
    }

    async handleBloqUpdatedEvent(_event: BloqEvent): Promise<void> {
        throw new MethodInvocationOutOfContextError({
            methodName: 'handleBloqUpdatedEvent',
            partnerId: this.partnerFriendlyId,
        });
    }
}
