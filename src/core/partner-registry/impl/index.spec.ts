import { PartnerRegistryImpl } from '.';
import { FakePartnerHandler } from '../fake/handler';

describe('PartnerRegistryImpl', () => {
    describe('get', () => {
        const partnerId = 'partner-id';

        it('should throw an error if no concrete impl was found for a handler', () => {
            const registry = new PartnerRegistryImpl();
            expect(() => registry.get(partnerId)).toThrow(
                `No concrete implementation for a handler was found in the registry for "${partnerId}"`,
            );
        });

        it('should return an instance of a registered handler', () => {
            const handlerInstance = new FakePartnerHandler();

            const registry = new PartnerRegistryImpl();
            registry.register(partnerId, handlerInstance);
            const retrievedHandler = registry.get(partnerId);

            expect(retrievedHandler).toBe(handlerInstance);
        });
    });
});
