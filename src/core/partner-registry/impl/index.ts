import { type PartnerRegistry, type <PERSON><PERSON><PERSON><PERSON> } from '..';

export class PartnerRegistryImpl implements PartnerRegistry {
    private entries: any = {};

    constructor() {
        this.entries = {};
        this.get = this.get.bind(this);
    }

    register(id: string, handler: PartnerHandler): void {
        this.entries[id] = handler;
    }

    get(id: string): PartnerHandler | undefined {
        const instance = this.entries[id];
        if (instance === undefined) {
            throw new Error(
                `No concrete implementation for a handler was found in the registry for "${id}"`,
            );
        }

        return instance;
    }
}
