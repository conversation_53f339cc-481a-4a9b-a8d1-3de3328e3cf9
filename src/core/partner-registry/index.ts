import type {
    BloqChangeState,
    BloqChangeStateResponse,
    BloqEvent,
} from '../../bloqit/bloqs/models';
import type {
    LockerChangeState,
    LockerChangeStateResponse,
    LockerMaintenanceStatusUpdateEvent,
} from '../../bloqit/lockers/models';
import { type AuthenticationResult } from '../../bloqit/couriers/models/authentication-result';
import type { Rent, RentEvent, OperationType } from '../../bloqit/rents/models/rent';
import { CourierAccessCodesResponse } from '../../bloqit/bloqs/models/courier-access-codes';

export interface PartnerHandler {
    getRentById: (props: {
        partnerRentId: string;
        bloqId: string;
        courier?: string;
        locale: string;
        nodeId: string;
        country?: string;
    }) => Promise<Rent[]>;

    canBePickedUp: (props: {
        partnerRentId: string;
        bloqId: string;
        operationType: OperationType;
    }) => Promise<boolean>;

    setConfig: (
        changeState: BloqChangeState | LockerChangeState,
    ) => Promise<BloqChangeStateResponse | LockerChangeStateResponse>;

    authenticateCourier: (args: {
        pin: string;
        bloq?: string;
        bloqExternalId?: string;
        courierSessionId?: string;
        password?: string;
        courierId?: string;
        country?: string;
    }) => Promise<AuthenticationResult>;

    notifyDropOffConfirmation: (event: RentEvent) => Promise<void>;

    notifyDropOffCancellation: (event: RentEvent) => Promise<void>;

    notifyCollectItem: (event: RentEvent) => Promise<void>;

    notifyExpiredRent: (event: RentEvent) => Promise<void>;

    notifyExpiredRents: (args: { batchSize: number }) => Promise<void>;

    notifyStuckRent: (event: RentEvent) => Promise<void>;

    notifyViolence: (event: RentEvent) => Promise<void>;

    notifyPing: (event: BloqChangeState) => Promise<void>;

    notifyDirtyDoor: (event: RentEvent) => Promise<void>;

    associateLabel: (props: {
        partnerRentId: string;
        bloqId: string;
        operationType: OperationType;
        courier: string;
        locale: string;
        code: string;
        bloqExternalId?: string;
        country?: string;
    }) => Promise<boolean>;

    handleCourierLogout: (_event: BloqEvent) => Promise<void>;

    handleRentFinishedEvent: (_event: RentEvent) => Promise<void>;

    handleRentStartingEvent: (_event: RentEvent) => Promise<void>;

    handleRentFlowInterruptedEvent: (_event: RentEvent) => Promise<void>;

    getCouriersAccessCodes: (args: {
        bloqId: string;
        bloqExternalId?: string;
        carrier?: string;
        country?: string;
    }) => Promise<CourierAccessCodesResponse>;

    maintenanceStatusUpdate: (event: LockerMaintenanceStatusUpdateEvent) => Promise<void>;

    handleBloqCreatedEvent: (event: BloqEvent) => Promise<void>;

    handleBloqUpdatedEvent: (event: BloqEvent) => Promise<void>;
}

export interface PartnerRegistry {
    register: (id: string, handler: PartnerHandler) => void;
    get: (id: string) => PartnerHandler | undefined;
}
