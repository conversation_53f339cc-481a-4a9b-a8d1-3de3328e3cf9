import { type <PERSON><PERSON><PERSON><PERSON> } from '../..';
import {
    type BloqChangeState,
    type BloqChangeStateResponse,
    type BloqEvent,
} from '../../../../bloqit/bloqs/models';
import { CourierAccessCodesResponse } from '../../../../bloqit/bloqs/models/courier-access-codes';
import { type AuthenticationResult } from '../../../../bloqit/couriers/models/authentication-result';
import {
    LockerMaintenanceStatusUpdateEvent,
    type LockerChangeState,
    type LockerChangeStateResponse,
} from '../../../../bloqit/lockers/models';
import type { OperationType, Rent, RentEvent } from '../../../../bloqit/rents/models/rent';

export class FakePartnerHandler implements PartnerHandler {
    async notifyExpiredRents(_args: { batchSize: number }): Promise<void> {
        throw new Error('Method not implemented.');
    }

    async handleRentFinishedEvent(_event: RentEvent): Promise<void> {
        throw new Error('Method not implemented.');
    }

    async handleRentStartingEvent(_event: RentEvent): Promise<void> {
        throw new Error('Method not implemented.');
    }

    async handleRentFlowInterruptedEvent(_event: RentEvent): Promise<void> {
        throw new Error('Method not implemented.');
    }

    async getRentById(_props: {
        partnerRentId: string;
        bloqId: string;
        courier?: string;
        locale: string;
    }): Promise<Rent[]> {
        throw new Error('Method not implemented.');
    }

    async setConfig(
        _changeState: BloqChangeState | LockerChangeState,
    ): Promise<BloqChangeStateResponse | LockerChangeStateResponse> {
        throw new Error('Method not implemented.');
    }

    async authenticateCourier(_args: {
        pin: string;
        bloq?: string;
        bloqExternalId?: string;
        courierSessionId?: string;
        password?: string;
        courierId?: string;
    }): Promise<AuthenticationResult> {
        throw new Error('Method not implemented.');
    }

    async notifyDropOffConfirmation(_event: RentEvent): Promise<void> {
        throw new Error('Method not implemented.');
    }

    async notifyDropOffCancellation(_event: RentEvent): Promise<void> {
        throw new Error('Method not implemented.');
    }

    async notifyCollectItem(_event: RentEvent): Promise<void> {
        throw new Error('Method not implemented.');
    }

    async notifyExpiredRent(_event: RentEvent): Promise<void> {
        throw new Error('Method not implemented.');
    }

    async notifyStuckRent(_event: RentEvent): Promise<void> {
        throw new Error('Method not implemented.');
    }

    async notifyDirtyDoor(_event: RentEvent): Promise<void> {
        throw new Error('Method not implemented.');
    }

    async notifyViolence(_event: RentEvent): Promise<void> {
        throw new Error('Method not implemented.');
    }

    async notifyPing(_event: BloqChangeState): Promise<void> {
        throw new Error('Method not implemented.');
    }

    async canBePickedUp(_props: {
        partnerRentId: string;
        bloqId: string;
        operationType: OperationType;
    }): Promise<boolean> {
        throw new Error('Method not implemented.');
    }

    async associateLabel(_props: {
        partnerRentId: string;
        bloqId: string;
        operationType: OperationType;
        courier: string;
        locale: string;
        code: string;
        bloqExternalId?: string;
        country?: string;
    }): Promise<boolean> {
        throw new Error('Method not implemented.');
    }

    async handleCourierLogout(_event: BloqEvent): Promise<void> {
        throw new Error('Method not implemented.');
    }

    async getCouriersAccessCodes(args: {
        bloqId: string;
        bloqExternalId?: string;
        carrier?: string;
        country?: string;
    }): Promise<CourierAccessCodesResponse> {
        throw new Error('Method not implemented.');
    }

    async maintenanceStatusUpdate(_event: LockerMaintenanceStatusUpdateEvent): Promise<void> {
        throw new Error('Method not implemented.');
    }

    handle(): void {
        throw new Error('Method not implemented.');
    }

    async handleBloqCreatedEvent(_event: BloqEvent): Promise<void> {
        throw new Error('Method not implemented.');
    }

    async handleBloqUpdatedEvent(_event: BloqEvent): Promise<void> {
        throw new Error('Method not implemented.');
    }
}
