import { type PartnerHandler, type PartnerRegistry } from '..';

export class FakePartnerRegistry implements PartnerRegistry {
    register(_id: string, _handler: PartnerHandler): void {
        throw new Error('Not implemented. Please override it for your test purposes');
    }

    get(_id: string): PartnerHandler | undefined {
        throw new Error('Not implemented. Please override it for your test purposes');
    }
}
