export class BloqitIdAlreadyExistsError extends Error {
    constructor(bloqitId: string) {
        super(
            `Failed to store mapping record. An id mapping containing bloqitId "${bloqitId}" already exists.`,
        );
    }
}

export class SharedIdAlreadyExistsError extends Error {
    constructor(sharedId: string) {
        super(
            `Failed to store mapping record. An id mapping containing sharedId "${sharedId}" already exists.`,
        );
    }
}

export class PartnerIdAlreadyExistsError extends Error {
    constructor(partnerId: string) {
        super(
            `Failed to store mapping record. An id mapping containing partnerId "${partnerId}" already exists.`,
        );
    }
}

export class InvalidIdMappingDirectionError extends Error {
    constructor() {
        super(
            'Invalid matching direction. Expected one of the following values: PARTNER | SHARED | BLOQIT.',
        );
    }
}
