import { IdMappingRepositoryImpl } from '.';
import { type IdMapping, IdMappingDirection, IdMappingType } from '..';
import { FakeDocumentDatabaseClient } from '../../../data-access/document/fake';
import { FakeLogger } from '../../../logger/fake';

describe('IdMappingRepository', () => {
    const bloqitId = 'bloqit-obj-id-123';
    const partnerId = 'partner-obj-id-123';
    const sharedId = 'shared-123';
    const partnerFriendlyId = 'dhl-cz';
    const logger = new FakeLogger();

    beforeEach(() => {
        jest.spyOn(logger, 'error').mockReturnValue(undefined);
    });

    afterEach(() => jest.clearAllMocks());

    describe('persisting', () => {
        const idMappingCollection = new FakeDocumentDatabaseClient<IdMapping>();
        beforeEach(() => {
            jest.spyOn(idMappingCollection, 'findBy').mockResolvedValue(undefined);
            jest.spyOn(idMappingCollection, 'save').mockResolvedValue(undefined);
        });

        afterEach(() => jest.clearAllMocks());

        it('should store a bloq id mapping', async () => {
            const repo = new IdMappingRepositoryImpl({ logger, idMappingCollection });
            await repo.storeBloqIdMapping({ bloqitId, sharedId, partnerId, partnerFriendlyId });

            expect(idMappingCollection.save).toHaveBeenCalledWith({
                bloqitId,
                partnerId,
                sharedId,
                partnerFriendlyId,
                idType: IdMappingType.BLOQ,
            });
        });

        it('should store a rent id mapping', async () => {
            const repo = new IdMappingRepositoryImpl({ logger, idMappingCollection });
            await repo.storeRentIdMapping({ bloqitId, partnerId, partnerFriendlyId });

            expect(idMappingCollection.save).toHaveBeenCalledWith({
                bloqitId,
                partnerId,
                partnerFriendlyId,
                idType: IdMappingType.RENT,
            });
        });

        it('should store a locker id mapping', async () => {
            const repo = new IdMappingRepositoryImpl({ logger, idMappingCollection });
            await repo.storeLockerIdMapping({ bloqitId, partnerId, partnerFriendlyId });

            expect(idMappingCollection.save).toHaveBeenCalledWith({
                bloqitId,
                partnerId,
                partnerFriendlyId,
                idType: IdMappingType.LOCKER,
            });
        });
    });

    describe('fetching', () => {
        const idMappingRecord = {
            bloqitId,
            partnerId,
            sharedId,
            partnerFriendlyId,
            idType: IdMappingType.BLOQ,
        };

        const idMappingCollection = new FakeDocumentDatabaseClient<IdMapping>();
        beforeEach(() => {
            jest.spyOn(idMappingCollection, 'findBy').mockResolvedValueOnce(idMappingRecord);
        });

        afterEach(() => jest.clearAllMocks());

        describe('bloqit -> shared', () => {
            it('should retrieve a shared id based on a bloqit-baked id', async () => {
                const repo = new IdMappingRepositoryImpl({ logger, idMappingCollection });

                const receivedId = await repo.fetch({
                    direction: IdMappingDirection.SHARED,
                    matching: { bloqitId },
                });

                expect(receivedId).toBe(sharedId);
                expect(idMappingCollection.findBy).toHaveBeenCalledWith({ bloqitId });
            });
        });

        describe('bloqit -> partner', () => {
            it('should retrieve a partner-baked id based on a bloqit-baked id', async () => {
                const repo = new IdMappingRepositoryImpl({ logger, idMappingCollection });

                const receivedId = await repo.fetch({
                    direction: IdMappingDirection.PARTNER,
                    matching: { bloqitId },
                });

                expect(receivedId).toBe(partnerId);
                expect(idMappingCollection.findBy).toHaveBeenCalledWith({ bloqitId });
            });
        });

        describe('partner -> shared', () => {
            it('should retrieve a shared id based on a bloqit-baked id', async () => {
                const repo = new IdMappingRepositoryImpl({ logger, idMappingCollection });

                const receivedId = await repo.fetch({
                    direction: IdMappingDirection.SHARED,
                    matching: { partnerId },
                });

                expect(receivedId).toBe(sharedId);
                expect(idMappingCollection.findBy).toHaveBeenCalledWith({ partnerId });
            });
        });

        describe('partner -> bloqit', () => {
            it('should retrieve a bloqit-baked id based on a partner-baked id', async () => {
                const repo = new IdMappingRepositoryImpl({ logger, idMappingCollection });

                const receivedId = await repo.fetch({
                    direction: IdMappingDirection.BLOQIT,
                    matching: { partnerId },
                });

                expect(receivedId).toBe(bloqitId);
                expect(idMappingCollection.findBy).toHaveBeenCalledWith({ partnerId });
            });
        });
    });

    describe('overriding', () => {
        it('should allow to append an existing record for a bloq', async () => {
            const idMappingCollection = new FakeDocumentDatabaseClient<IdMapping>();
            jest.spyOn(idMappingCollection, 'updateOne').mockResolvedValue(undefined);

            const repo = new IdMappingRepositoryImpl({ logger, idMappingCollection });
            await repo.patchBloqIdMapping({
                append: { partnerId: 'new-partner-id' },
                matching: { bloqitId, sharedId, partnerFriendlyId },
            });

            expect(idMappingCollection.updateOne).toHaveBeenCalledWith({
                filter: { bloqitId, sharedId, partnerFriendlyId, idType: IdMappingType.BLOQ },
                set: { partnerId: 'new-partner-id' },
            });
        });

        it('should allow to append an existing record for a rent', async () => {
            const idMappingCollection = new FakeDocumentDatabaseClient<IdMapping>();
            jest.spyOn(idMappingCollection, 'updateOne').mockResolvedValue(undefined);

            const repo = new IdMappingRepositoryImpl({ logger, idMappingCollection });
            await repo.patchRentIdMapping({
                append: { partnerId: 'new-partner-id' },
                matching: { bloqitId, partnerFriendlyId },
            });

            expect(idMappingCollection.updateOne).toHaveBeenCalledWith({
                filter: { bloqitId, partnerFriendlyId, idType: IdMappingType.RENT },
                set: { partnerId: 'new-partner-id' },
            });
        });

        it('should allow to append an existing record for a locker', async () => {
            const idMappingCollection = new FakeDocumentDatabaseClient<IdMapping>();
            jest.spyOn(idMappingCollection, 'updateOne').mockResolvedValue(undefined);

            const repo = new IdMappingRepositoryImpl({ logger, idMappingCollection });
            await repo.patchLockerIdMapping({
                append: { partnerId: 'new-partner-id' },
                matching: { bloqitId, partnerFriendlyId },
            });

            expect(idMappingCollection.updateOne).toHaveBeenCalledWith({
                filter: { bloqitId, partnerFriendlyId, idType: IdMappingType.LOCKER },
                set: { partnerId: 'new-partner-id' },
            });
        });
    });

    describe('consistency', () => {
        afterEach(() => {
            jest.clearAllMocks();
        });

        it('should not allow multiple entries for the same bloqitId', async () => {
            const existingBloqitId = 'existing-bloqit-id-123';
            const idMappingRecord = {
                bloqitId: existingBloqitId,
                sharedId: 'inexistent-shared-id-123',
                partnerId: 'inexistent-partner-id-123',
                partnerFriendlyId,
            };

            const idMappingCollection = new FakeDocumentDatabaseClient<IdMapping>();
            jest.spyOn(idMappingCollection, 'findBy').mockResolvedValue(idMappingRecord);

            const repo = new IdMappingRepositoryImpl({ logger, idMappingCollection });

            await expect(
                repo.storeLockerIdMapping({
                    bloqitId: existingBloqitId,
                    partnerId,
                    partnerFriendlyId,
                }),
            ).rejects.toThrow(
                `Failed to store mapping record. An id mapping containing bloqitId "${existingBloqitId}" already exists.`,
            );

            expect(idMappingCollection.findBy).toHaveBeenCalledWith({
                bloqitId: existingBloqitId,
            });
        });

        it('should not allow multiple entries for the same partnerId', async () => {
            const existingPartnerId = 'existing-partner-id-123';
            const idMappingRecord = {
                partnerId: existingPartnerId,
                sharedId,
                bloqitId,
                partnerFriendlyId,
            };

            const idMappingCollection = new FakeDocumentDatabaseClient<IdMapping>();
            jest.spyOn(idMappingCollection, 'findBy')
                .mockResolvedValueOnce(undefined)
                .mockResolvedValueOnce(idMappingRecord);

            const repo = new IdMappingRepositoryImpl({ logger, idMappingCollection });

            await expect(
                repo.storeLockerIdMapping({
                    partnerId: existingPartnerId,
                    sharedId: 'inexistent-shared-id',
                    bloqitId: 'inexistent-bloqit-id',
                    partnerFriendlyId,
                }),
            ).rejects.toThrow(
                `Failed to store mapping record. An id mapping containing partnerId "${existingPartnerId}" already exists.`,
            );

            expect(idMappingCollection.findBy).toHaveBeenCalledWith({
                partnerId: existingPartnerId,
            });
        });

        it('should not allow multiple entries for the same sharedId', async () => {
            const existingSharedId = 'existing-shared-id-123';
            const idMappingRecord = {
                sharedId: existingSharedId,
                bloqitId,
                partnerId,
                partnerFriendlyId,
            };

            const idMappingCollection = new FakeDocumentDatabaseClient<IdMapping>();
            jest.spyOn(idMappingCollection, 'findBy')
                .mockResolvedValueOnce(undefined)
                .mockResolvedValueOnce(undefined)
                .mockResolvedValueOnce(idMappingRecord);

            const repo = new IdMappingRepositoryImpl({ logger, idMappingCollection });

            await expect(
                repo.storeLockerIdMapping({
                    sharedId: existingSharedId,
                    bloqitId: 'inexistent-bloqit-id',
                    partnerId: 'inexistent-partner-id',
                    partnerFriendlyId,
                }),
            ).rejects.toThrow(
                `Failed to store mapping record. An id mapping containing sharedId "${existingSharedId}" already exists.`,
            );

            expect(idMappingCollection.findBy).toHaveBeenCalledWith({
                sharedId: existingSharedId,
            });
        });
    });

    describe('dropping', () => {
        it('should drop an id mapping', async () => {
            const idMappingCollection = new FakeDocumentDatabaseClient<IdMapping>();
            jest.spyOn(idMappingCollection, 'removeOne').mockResolvedValue(undefined);

            const repo = new IdMappingRepositoryImpl({ logger, idMappingCollection });
            await repo.drop({
                idType: IdMappingType.BLOQ,
                matching: { bloqitId, sharedId, partnerFriendlyId },
            });

            expect(idMappingCollection.removeOne).toHaveBeenCalledWith({
                bloqitId,
                sharedId,
                partnerFriendlyId,
                idType: IdMappingType.BLOQ,
            });
        });

        it('should handle unexpected collection errors', async () => {
            const error = new Error('Unexpected error');
            const idMappingCollection = new FakeDocumentDatabaseClient<IdMapping>();
            jest.spyOn(idMappingCollection, 'removeOne').mockRejectedValueOnce(error);

            const repo = new IdMappingRepositoryImpl({ idMappingCollection, logger });

            await expect(
                repo.drop({
                    idType: IdMappingType.BLOQ,
                    matching: { bloqitId, sharedId, partnerFriendlyId },
                }),
            ).rejects.toThrow('Failed to drop id mapping record. An unexpected error has occurred');

            expect(logger.error).toHaveBeenCalledTimes(1);
            expect(logger.error).toHaveBeenCalledWith({ message: error.message });
            expect(idMappingCollection.removeOne).toHaveBeenCalledWith({
                bloqitId,
                sharedId,
                partnerFriendlyId,
                idType: IdMappingType.BLOQ,
            });
        });
    });
});
