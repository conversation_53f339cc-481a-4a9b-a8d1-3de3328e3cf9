import { type IdMappingRepository, type IdMapping, IdMappingType, IdMappingDirection } from '..';
import { type DocumentDatabaseClient } from '../../../data-access/document';
import { type Logger } from '../../../logger';
import {
    InvalidIdMappingDirectionError,
    BloqitIdAlreadyExistsError,
    SharedIdAlreadyExistsError,
    PartnerIdAlreadyExistsError,
} from '../../errors';

export class IdMappingRepositoryImpl implements IdMappingRepository {
    private readonly idMappingCollection: DocumentDatabaseClient<IdMapping>;
    private readonly logger: Logger;

    constructor(deps: { logger: Logger; idMappingCollection: DocumentDatabaseClient<IdMapping> }) {
        this.idMappingCollection = deps.idMappingCollection;
        this.logger = deps.logger;
    }

    async drop(props: {
        idType: IdMappingType;
        matching: {
            bloqitId?: string;
            sharedId?: string;
            partnerId?: string;
            partnerFriendlyId: string;
        };
    }): Promise<void> {
        try {
            await this.idMappingCollection.removeOne({ ...props.matching, idType: props.idType });
        } catch (ex) {
            const err = ex as Error;
            this.logger.error({ message: err.message });
            throw new Error('Failed to drop id mapping record. An unexpected error has occurred');
        }
    }

    async fetch(props: {
        direction: IdMappingDirection;
        matching: { bloqitId?: string; sharedId?: string; partnerId?: string };
    }): Promise<string | undefined> {
        const { direction, matching } = props;
        const data = await this.idMappingCollection.findBy(matching);

        switch (direction) {
            case IdMappingDirection.PARTNER:
                return data?.partnerId;
            case IdMappingDirection.SHARED:
                return data?.sharedId;
            case IdMappingDirection.BLOQIT:
                return data?.bloqitId;
            default:
                throw new InvalidIdMappingDirectionError();
        }
    }

    async patchLockerIdMapping(props: {
        matching: Omit<IdMapping, 'idType'>;
        append: Omit<IdMapping, 'idType' | 'partnerFriendlyId'>;
    }): Promise<void> {
        const { matching, append } = props;
        await this.patch({ matching, append, idType: IdMappingType.LOCKER });
    }

    async patchRentIdMapping(props: {
        matching: Omit<IdMapping, 'idType'>;
        append: Omit<IdMapping, 'idType' | 'partnerFriendlyId'>;
    }): Promise<void> {
        const { matching, append } = props;
        await this.patch({ matching, append, idType: IdMappingType.RENT });
    }

    async patchBloqIdMapping(props: {
        matching: Omit<IdMapping, 'idType'>;
        append: Omit<IdMapping, 'idType' | 'partnerFriendlyId'>;
    }): Promise<void> {
        const { matching, append } = props;
        await this.patch({ matching, append, idType: IdMappingType.BLOQ });
    }

    async patch(props: {
        matching: Omit<IdMapping, 'idType'>;
        append: Omit<IdMapping, 'idType' | 'partnerFriendlyId'>;
        idType: IdMappingType;
    }): Promise<void> {
        const { matching, append, idType } = props;
        await this.idMappingCollection.updateOne({ filter: { ...matching, idType }, set: append });
    }

    async storeRentIdMapping(props: Omit<IdMapping, 'idType'>): Promise<void> {
        await this.store({ ...props, idType: IdMappingType.RENT });
    }

    async storeBloqIdMapping(props: Omit<IdMapping, 'idType'>): Promise<void> {
        await this.store({ ...props, idType: IdMappingType.BLOQ });
    }

    async storeLockerIdMapping(props: Omit<IdMapping, 'idType'>): Promise<void> {
        await this.store({ ...props, idType: IdMappingType.LOCKER });
    }

    async store(data: IdMapping): Promise<void> {
        const { bloqitId, sharedId, partnerId } = data;

        if (bloqitId !== undefined) await this.validateBloqitIdIsUnique(bloqitId);
        if (partnerId !== undefined) await this.validatePartnerIdIsUnique(partnerId);
        if (sharedId !== undefined) await this.validateSharedIdIsUnique(sharedId);

        await this.idMappingCollection.save(data);
    }

    private async validateBloqitIdIsUnique(bloqitId: string): Promise<void> {
        const existingBloqitIdMapping = await this.idMappingCollection.findBy({ bloqitId });
        if (existingBloqitIdMapping !== undefined) {
            throw new BloqitIdAlreadyExistsError(bloqitId);
        }
    }

    private async validateSharedIdIsUnique(sharedId: string): Promise<void> {
        const existingSharedIdMapping = await this.idMappingCollection.findBy({ sharedId });
        if (existingSharedIdMapping !== undefined) {
            throw new SharedIdAlreadyExistsError(sharedId);
        }
    }

    private async validatePartnerIdIsUnique(partnerId: string): Promise<void> {
        const existingPartnerIdMapping = await this.idMappingCollection.findBy({ partnerId });
        if (existingPartnerIdMapping !== undefined) {
            throw new PartnerIdAlreadyExistsError(partnerId);
        }
    }
}
