export enum IdMappingDirection {
    PARTNER = 'PARTNER',
    SHARED = 'SHARED',
    BLOQIT = 'BLOQIT',
}

export enum IdMappingType {
    BLOQ = 'BLOQ',
    RENT = 'RENT',
    LOCKER = 'LOCKER',
}

export interface IdMapping {
    bloqitId?: string;
    sharedId?: string;
    partnerId?: string;
    partnerFriendlyId: string;
    idType: IdMappingType;
}

export interface IdMappingRepository {
    fetch: (props: {
        direction: IdMappingDirection;
        matching: { bloqitId?: string; sharedId?: string; partnerId?: string };
    }) => Promise<string | undefined>;

    patchLockerIdMapping: (props: {
        matching: Omit<IdMapping, 'idType'>;
        append: Omit<IdMapping, 'idType' | 'partnerFriendlyId'>;
    }) => Promise<void>;

    patchRentIdMapping: (props: {
        matching: Omit<IdMapping, 'idType'>;
        append: Omit<IdMapping, 'idType' | 'partnerFriendlyId'>;
    }) => Promise<void>;

    patchBloqIdMapping: (props: {
        matching: Omit<IdMapping, 'idType'>;
        append: Omit<IdMapping, 'idType' | 'partnerFriendlyId'>;
    }) => Promise<void>;

    patch: (props: {
        matching: Omit<IdMapping, 'idType'>;
        append: Omit<IdMapping, 'idType' | 'partnerFriendlyId'>;
        idType: IdMappingType;
    }) => Promise<void>;

    storeRentIdMapping: (props: Omit<IdMapping, 'idType'>) => Promise<void>;

    storeBloqIdMapping: (props: Omit<IdMapping, 'idType'>) => Promise<void>;

    storeLockerIdMapping: (props: Omit<IdMapping, 'idType'>) => Promise<void>;

    store: (data: IdMapping) => Promise<void>;

    drop: (props: {
        idType: IdMappingType;
        matching: {
            bloqitId?: string;
            sharedId?: string;
            partnerId?: string;
            partnerFriendlyId: string;
        };
    }) => Promise<void>;
}
