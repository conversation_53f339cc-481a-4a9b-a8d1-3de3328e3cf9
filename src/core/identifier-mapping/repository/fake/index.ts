import {
    type IdMappingType,
    type IdMapping,
    type IdMappingDirection,
    type IdMappingRepository,
} from '..';

export class FakeIdMappingRepository implements IdMappingRepository {
    async patchLockerIdMapping(_props: {
        matching: Omit<IdMapping, 'idType'>;
        append: Omit<IdMapping, 'idType' | 'partnerFriendlyId'>;
    }): Promise<void> {
        throw new Error('Method not implemented. Please override it for your test purposes.');
    }

    async patchRentIdMapping(_props: {
        matching: Omit<IdMapping, 'idType'>;
        append: Omit<IdMapping, 'idType' | 'partnerFriendlyId'>;
    }): Promise<void> {
        throw new Error('Method not implemented. Please override it for your test purposes.');
    }

    async patchBloqIdMapping(_props: {
        matching: Omit<IdMapping, 'idType'>;
        append: Omit<IdMapping, 'idType' | 'partnerFriendlyId'>;
    }): Promise<any> {
        throw new Error('Method not implemented. Please override it for your test purposes.');
    }

    async patch(_props: {
        matching: Omit<IdMapping, 'idType'>;
        append: Omit<IdMapping, 'idType' | 'partnerFriendlyId'>;
        idType: IdMappingType;
    }): Promise<void> {
        throw new Error('Method not implemented. Please override it for your test purposes.');
    }

    async storeRentIdMapping(_props: Omit<IdMapping, 'idType'>): Promise<void> {
        throw new Error('Method not implemented. Please override it for your test purposes.');
    }

    async storeBloqIdMapping(_props: Omit<IdMapping, 'idType'>): Promise<void> {
        throw new Error('Method not implemented. Please override it for your test purposes.');
    }

    async storeLockerIdMapping(_props: Omit<IdMapping, 'idType'>): Promise<void> {
        throw new Error('Method not implemented. Please override it for your test purposes.');
    }

    async store(_data: IdMapping): Promise<void> {
        throw new Error('Method not implemented. Please override it for your test purposes.');
    }

    async fetch(_props: {
        direction: IdMappingDirection;
        matching: {
            bloqitId?: string | undefined;
            sharedId?: string | undefined;
            partnerId?: string | undefined;
        };
    }): Promise<string | undefined> {
        throw new Error('Method not implemented. Please override it for your test purposes.');
    }

    async drop(_props: {
        idType: IdMappingType;
        matching: {
            bloqitId?: string;
            sharedId?: string;
            partnerId?: string;
            partnerFriendlyId: string;
        };
    }): Promise<void> {
        throw new Error('Method not implemented. Please override it for your test purposes.');
    }
}
