import os from 'os';
import winston from 'winston';
import Express<PERSON>inston from 'express-winston';
import DatadogWinston from 'datadog-winston';
import { type Handler } from 'express';

import { type ApplicationEnvironment } from '../../../env';
import { type HTTPRequest, type HTTPResponse } from '../../../../http/models/extensions/express';
import { simplifyControllerResponse } from '../../../../../test/utils';

ExpressWinston.requestWhitelist.push('body');
ExpressWinston.responseWhitelist.push('body');
ExpressWinston.bodyBlacklist.push(
    'password',
    'password1',
    'password2',
    'newPassword1',
    'newPassword2',
    'oldPassword',
    'newPassword',
);

export function setup(props: { env: ApplicationEnvironment }): {
    applicationLogger: winston.Logger;
    reqResLogger: Handler;
    errorLogger: Handler;
} {
    const { env } = props;
    const transports: any[] = [
        new winston.transports.Console({
            format: winston.format.combine(winston.format.colorize(), winston.format.simple()),
        }),
    ];

    if (env.DATADOG_LOGGING_ENABLED) {
        transports.push(
            new DatadogWinston({
                level: 'debug',
                hostname: os.hostname(),
                service: `partner-integrations-middleware-${env.NODE_ENV}`,
                apiKey: env.DATADOG_API_KEY,
                ddsource: env.DATADOG_SOURCE,
                intakeRegion: env.DATADOG_INTAKE_REGION,
            }),
        );
    }

    const applicationLogger = winston.createLogger({ transports, format: winston.format.json() });

    const errorLogger = ExpressWinston.logger({ transports, format: winston.format.json() });
    const reqResLogger = ExpressWinston.logger({
        transports,
        format: winston.format.json(),
        meta: true,
        headerBlacklist: ['authorization', 'secret', 'x-api-key', 'x-core-api-key'],
        expressFormat: true,
        skip(req: HTTPRequest, res: HTTPResponse) {
            const simplifiedRes = simplifyControllerResponse(res);
            return (
                req.method === 'OPTIONS' ||
                (simplifiedRes.body !== undefined && typeof simplifiedRes.body !== 'object')
            );
        },
    });

    return { applicationLogger, reqResLogger, errorLogger };
}
