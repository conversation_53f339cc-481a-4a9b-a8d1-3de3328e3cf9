import type winston from 'winston';
import { type Logger } from '..';

export class <PERSON><PERSON>og<PERSON> implements Logger {
    private readonly winstonInstance: winston.Logger;

    constructor(deps: { winstonInstance: winston.Logger }) {
        this.winstonInstance = deps.winstonInstance;
    }

    info(message: object): void {
        this.winstonInstance.info(JSON.stringify(message));
    }

    error(message: object): void {
        this.winstonInstance.error(JSON.stringify(message));
    }
}
