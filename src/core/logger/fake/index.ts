import { type Logger } from '..';

export class FakeLogger implements Logger {
    private readonly showOutput: boolean;

    constructor(deps: { showOutput: boolean } = { showOutput: false }) {
        this.showOutput = deps.showOutput;

        this.info = this.info.bind(this);
        this.error = this.error.bind(this);
    }

    info(message: object): void {
        if (this.showOutput) console.log(`INFO: ${JSON.stringify(message)}`);
    }

    error(message: object): void {
        if (this.showOutput) console.log(`INFO: ${JSON.stringify(message)}`);
    }
}
