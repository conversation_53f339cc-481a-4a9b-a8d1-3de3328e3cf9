import * as UUIDGenerator from '.';

describe('UUI generator', () => {
    describe('generate', () => {
        afterEach(() => {
            process.env.USE_STATIC_UUIDS = undefined;
        });

        it('should generate a static UUID if env var is set to 1', () => {
            process.env.USE_STATIC_UUIDS = '1';
            expect(UUIDGenerator.generate()).toBe('static-uuid');
        });

        it('should generate regular UUIDs if env var is set to any other value', () => {
            process.env.USE_STATIC_UUIDS = undefined;
            expect(UUIDGenerator.generate()).not.toBe('static-uuid');
        });
    });
});
