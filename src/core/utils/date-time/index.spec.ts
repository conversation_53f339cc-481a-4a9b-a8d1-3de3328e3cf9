import * as DateTime from '.';

describe('DateTime', () => {
    describe('daysInTheFuture', () => {
        it('should create a date n days from now', () => {
            jest.spyOn(DateTime, 'now').mockReturnValue(new Date('2023-01-01'));
            const futureDate = DateTime.daysInTheFuture(90);
            expect(futureDate).toEqual(new Date('2023-04-01'));
        });
    });

    describe('getLocalTimeAt', () => {
        it('should return the local time at the given timezone (non-daylight saving time)', () => {
            jest.spyOn(DateTime, 'utc').mockReturnValue(new Date('2024-01-01T10:00:00Z'));
            const time = DateTime.getLocalTimeAt('Europe/Rome');
            expect(time).toEqual([11, 0]);
        });

        it('should return the local time at the given timezone (daylight saving time)', () => {
            jest.spyOn(DateTime, 'utc').mockReturnValue(new Date('2024-04-01T10:00:00Z'));
            const time = DateTime.getLocalTimeAt('Europe/Rome');
            expect(time).toEqual([12, 0]);
        });
    });
});
