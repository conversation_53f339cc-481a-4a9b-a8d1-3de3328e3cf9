export enum WorldTimeZones {
    ROME = 'Europe/Rome',
}

export const now = (): Date => new Date();

export const utc = (): Date => {
    const n = now();
    return new Date(
        Date.UTC(
            n.getFullYear(),
            n.getMonth(),
            n.getDate(),
            n.getHours(),
            n.getMinutes(),
            n.getSeconds(),
            n.getMilliseconds(),
        ),
    );
};

export const extractHourMinuteFromTimeStr = (time: string): number[] => {
    return time.split(':').map(s => parseInt(s));
};

export const daysInTheFuture = (days: number): Date => {
    return new Date(now().getTime() + days * 24 * 60 * 60 * 1000);
};

export const getLocalTimeAt = (timeZone: string): number[] => {
    return extractHourMinuteFromTimeStr(
        /* 
            For documentation o the options, see:
            https://tc39.es/proposal-intl-datetime-style/#sec-properties-of-intl-datetimeformat-instances
        */
        utc().toLocaleTimeString('en-US', { timeZone, timeStyle: 'short', hourCycle: 'h23' }),
    );
};
