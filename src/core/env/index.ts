export interface ApplicationEnvironment {
    PORT: number;
    NODE_ENV: string;
    DHL_CZ_PARTNER_ID: string;
    DHL_CZ_MOCK_PARTNER_ID?: string;
    DHL_CZ_MIDDLEWARE_API_KEY: string;
    DHL_CZ_OAUTH_HOST: string;
    DHL_CZ_API_HOST: string;
    DHL_CZ_OAUTH_CLIENT_ID: string;
    DHL_CZ_OAUTH_CLIENT_SECRET: string;
    DHL_CZ_BLOQIT_API_KEY: string;
    BLOQIT_CORE_MIDDLEWARE_API_KEY: string;
    MONGO_DB_URL: string;
    MONGO_DB_NAME: string;
    DOCUMENT_DATABASE_STRATEGY: string;
    LOG_TO_CONSOLE_ENABLED: boolean;
    DATADOG_LOGGING_ENABLED: boolean;
    DATADOG_API_KEY: string;
    DATADOG_SOURCE: string;
    DATADOG_INTAKE_REGION: string;
    REQ_RES_PAYLOAD_LOGGING_ENABLED: boolean;
    DHL_IT_MIDDLEWARE_API_KEY: string;
    DHL_IT_API_HOST: string;
    DHL_IT_AUTH_CLIENT_ID: string;
    DHL_IT_AUTH_CLIENT_SECRET: string;
    BLOQIT_COURIER_BYPASS_PIN: string;
    DHL_IT_PARTNER_ID: string;
    DHL_IT_MOCK_PARTNER_ID?: string;
    DHL_IT_BATCH_EXPIRED_RENTS_NOTIFICATION_WINDOW_START: string;
    DHL_IT_BATCH_EXPIRED_RENTS_NOTIFICATION_WINDOW_END: string;
    DHL_IT_BATCH_EXPIRED_RENT_NOTIFICATIONS_DRY_RUN_ENABLED: boolean;
    PARTNER_API_MOCK_SERVER_HOST?: string;
    GLOBAL_HTTP_CLIENT_TIMEOUT_MS: number;
    INPOST_PARTNER_ID: string;
    INPOST_OAUTH_HOST: string;
    INPOST_API_HOST: string;
    INPOST_OAUTH_CLIENT_ID: string;
    INPOST_OAUTH_CLIENT_SECRET: string;
    GENERIC_API_KEY?: string;
    INPOST_AUTH_HOST?: string;
    INPOST_MIDDLEWARE_API_KEY: string;
    INPOST_BLOQIT_API_KEY: string;
}

export function setup(): ApplicationEnvironment {
    const PORT = Number(process.env.PORT) ?? 3000;

    const DHL_CZ_MIDDLEWARE_API_KEY = process.env.DHL_CZ_MIDDLEWARE_API_KEY;
    if (DHL_CZ_MIDDLEWARE_API_KEY === undefined) {
        throw new Error('DHL_CZ_MIDDLEWARE_API_KEY not found in environment variables');
    }

    const BLOQIT_CORE_MIDDLEWARE_API_KEY = process.env.BLOQIT_CORE_MIDDLEWARE_API_KEY;
    if (BLOQIT_CORE_MIDDLEWARE_API_KEY === undefined) {
        throw new Error('BLOQIT_CORE_MIDDLEWARE_API_KEY not found in environment variables');
    }

    const DHL_CZ_PARTNER_ID = process.env.DHL_CZ_PARTNER_ID;
    if (DHL_CZ_PARTNER_ID === undefined) {
        throw new Error('DHL_CZ_PARTNER_ID not found in environment variables');
    }

    const MONGO_DB_URL = process.env.MONGO_DB_URL;
    if (MONGO_DB_URL === undefined) {
        throw new Error('MONGO_DB_URL not found in environment variables');
    }

    const MONGO_DB_NAME = process.env.MONGO_DB_NAME;
    if (MONGO_DB_NAME === undefined) {
        throw new Error('MONGO_DB_NAME not found in environment variables');
    }

    const DOCUMENT_DATABASE_STRATEGY = process.env.DOCUMENT_DATABASE_STRATEGY;
    if (DOCUMENT_DATABASE_STRATEGY === undefined) {
        throw new Error('DOCUMENT_DATABASE_STRATEGY not found in environment variables');
    }

    const LOG_TO_CONSOLE_ENABLED = process.env.LOG_TO_CONSOLE_ENABLED ?? 'enabled';
    const REQ_RES_PAYLOAD_LOGGING_ENABLED =
        process.env.REQ_RES_PAYLOAD_LOGGING_ENABLED ?? 'disabled';

    const DATADOG_LOGGING_ENABLED = process.env.DATADOG_LOGGING_ENABLED ?? 'disabled';
    const DATADOG_API_KEY = process.env.DATADOG_API_KEY;
    const DATADOG_SOURCE = process.env.DATADOG_SOURCE;
    const DATADOG_INTAKE_REGION = process.env.DATADOG_INTAKE_REGION;

    if (DATADOG_LOGGING_ENABLED === 'enabled') {
        if (DATADOG_API_KEY === undefined) {
            throw new Error(
                'DATADOG_API_KEY is required because DATADOG_LOGGING_ENABLED is set to "enabled", but it was not found in the list of environment variables',
            );
        }

        if (DATADOG_SOURCE === undefined) {
            throw new Error(
                'DATADOG_SOURCE is required because DATADOG_LOGGING_ENABLED is set to "enabled", but it was not found in the list of environment variables',
            );
        }

        if (DATADOG_INTAKE_REGION === undefined) {
            throw new Error(
                'DATADOG_INTAKE_REGION is required because DATADOG_LOGGING_ENABLED is set to "enabled", but it was not found in the list of environment variables',
            );
        }
    }

    const NODE_ENV = process.env.NODE_ENV;
    if (NODE_ENV === undefined) {
        throw new Error('NODE_ENV not found in environment variables');
    }

    const DHL_IT_MIDDLEWARE_API_KEY = process.env.DHL_IT_MIDDLEWARE_API_KEY;
    if (DHL_IT_MIDDLEWARE_API_KEY === undefined) {
        throw new Error('DHL_IT_MIDDLEWARE_API_KEY not found in environment variables');
    }

    const DHL_IT_API_HOST = process.env.DHL_IT_API_HOST;
    if (DHL_IT_API_HOST === undefined) {
        throw new Error('DHL_IT_API_HOST not found in environment variables');
    }

    const DHL_IT_AUTH_CLIENT_ID = process.env.DHL_IT_AUTH_CLIENT_ID;
    if (DHL_IT_AUTH_CLIENT_ID === undefined) {
        throw new Error('DHL_IT_AUTH_CLIENT_ID not found in environment variables');
    }

    const DHL_IT_AUTH_CLIENT_SECRET = process.env.DHL_IT_AUTH_CLIENT_SECRET;
    if (DHL_IT_AUTH_CLIENT_SECRET === undefined) {
        throw new Error('DHL_IT_AUTH_CLIENT_SECRET not found in environment variables');
    }

    const BLOQIT_COURIER_BYPASS_PIN = process.env.BLOQIT_COURIER_BYPASS_PIN;
    if (BLOQIT_COURIER_BYPASS_PIN === undefined) {
        throw new Error('BLOQIT_COURIER_BYPASS_PIN not found in environment variables');
    }

    const DHL_IT_PARTNER_ID = process.env.DHL_IT_PARTNER_ID;
    if (DHL_IT_PARTNER_ID === undefined) {
        throw new Error('DHL_IT_PARTNER_ID not found in environment variables');
    }

    const DHL_IT_BATCH_EXPIRED_RENTS_NOTIFICATION_WINDOW_START =
        process.env.DHL_IT_BATCH_EXPIRED_RENTS_NOTIFICATION_WINDOW_START;

    if (DHL_IT_BATCH_EXPIRED_RENTS_NOTIFICATION_WINDOW_START === undefined) {
        throw new Error(
            'DHL_IT_BATCH_EXPIRED_RENTS_NOTIFICATION_WINDOW_START not found in environment variables',
        );
    }

    const DHL_IT_BATCH_EXPIRED_RENTS_NOTIFICATION_WINDOW_END =
        process.env.DHL_IT_BATCH_EXPIRED_RENTS_NOTIFICATION_WINDOW_END;

    if (DHL_IT_BATCH_EXPIRED_RENTS_NOTIFICATION_WINDOW_END === undefined) {
        throw new Error(
            'DHL_IT_BATCH_EXPIRED_RENTS_NOTIFICATION_WINDOW_END not found in environment variables',
        );
    }
    const DHL_IT_BATCH_EXPIRED_RENT_NOTIFICATIONS_DRY_RUN_ENABLED =
        process.env.DHL_IT_BATCH_EXPIRED_RENT_NOTIFICATIONS_DRY_RUN_ENABLED !== undefined;

    const DHL_CZ_OAUTH_HOST = process.env.DHL_CZ_OAUTH_HOST;
    if (DHL_CZ_OAUTH_HOST === undefined) {
        throw new Error('DHL_CZ_OAUTH_HOST not found in environment variables');
    }

    const DHL_CZ_API_HOST = process.env.DHL_CZ_API_HOST;
    if (DHL_CZ_API_HOST === undefined) {
        throw new Error('DHL_CZ_API_HOST not found in environment variables');
    }

    const DHL_CZ_OAUTH_CLIENT_ID = process.env.DHL_CZ_OAUTH_CLIENT_ID;
    if (DHL_CZ_OAUTH_CLIENT_ID === undefined) {
        throw new Error('DHL_CZ_OAUTH_CLIENT_ID not found in environment variables');
    }

    const DHL_CZ_OAUTH_CLIENT_SECRET = process.env.DHL_CZ_OAUTH_CLIENT_SECRET;
    if (DHL_CZ_OAUTH_CLIENT_SECRET === undefined) {
        throw new Error('DHL_CZ_OAUTH_CLIENT_SECRET not found in environment variables');
    }

    const DHL_CZ_BLOQIT_API_KEY = process.env.DHL_CZ_BLOQIT_API_KEY;
    if (DHL_CZ_BLOQIT_API_KEY === undefined) {
        throw new Error('DHL_CZ_BLOQIT_API_KEY not found in environment variables');
    }

    const INPOST_PARTNER_ID = process.env.INPOST_PARTNER_ID;
    if (INPOST_PARTNER_ID === undefined) {
        throw new Error('INPOST_PARTNER_ID not found in environment variables');
    }

    const INPOST_OAUTH_HOST = process.env.INPOST_OAUTH_HOST;
    if (INPOST_OAUTH_HOST === undefined) {
        throw new Error('INPOST_OAUTH_HOST not found in environment variables');
    }

    const INPOST_OAUTH_CLIENT_ID = process.env.INPOST_OAUTH_CLIENT_ID;
    if (INPOST_OAUTH_CLIENT_ID === undefined) {
        throw new Error('INPOST_OAUTH_CLIENT_ID not found in environment variables');
    }

    const INPOST_OAUTH_CLIENT_SECRET = process.env.INPOST_OAUTH_CLIENT_SECRET;
    if (INPOST_OAUTH_CLIENT_SECRET === undefined) {
        throw new Error('INPOST_OAUTH_CLIENT_SECRET not found in environment variables');
    }

    const INPOST_MIDDLEWARE_API_KEY = process.env.INPOST_MIDDLEWARE_API_KEY;
    if (INPOST_MIDDLEWARE_API_KEY === undefined) {
        throw new Error('INPOST_MIDDLEWARE_API_KEY not found in environment variables');
    }

    let globalHttpClientTimeoutMs = Number(process.env.GLOBAL_HTTP_CLIENT_TIMEOUT_MS);
    if (Number.isNaN(globalHttpClientTimeoutMs)) {
        globalHttpClientTimeoutMs = 5000;
    }

    const INPOST_API_HOST = process.env.INPOST_API_HOST;
    if (INPOST_API_HOST === undefined) {
        throw new Error('INPOST_API_HOST not found in environment variables');
    }

    const INPOST_BLOQIT_API_KEY = process.env.INPOST_BLOQIT_API_KEY;
    if (INPOST_BLOQIT_API_KEY === undefined) {
        throw new Error('INPOST_BLOQIT_API_KEY not found in environment variables');
    }

    return {
        PORT,
        NODE_ENV,
        DHL_CZ_PARTNER_ID,
        DHL_CZ_MOCK_PARTNER_ID: process.env.DHL_CZ_MOCK_PARTNER_ID,
        DHL_CZ_MIDDLEWARE_API_KEY,
        DHL_CZ_OAUTH_HOST,
        DHL_CZ_API_HOST,
        DHL_CZ_OAUTH_CLIENT_ID,
        DHL_CZ_OAUTH_CLIENT_SECRET,
        DHL_CZ_BLOQIT_API_KEY,
        BLOQIT_CORE_MIDDLEWARE_API_KEY,
        MONGO_DB_URL,
        MONGO_DB_NAME,
        DOCUMENT_DATABASE_STRATEGY,
        LOG_TO_CONSOLE_ENABLED: LOG_TO_CONSOLE_ENABLED === 'enabled',
        DATADOG_LOGGING_ENABLED: DATADOG_LOGGING_ENABLED === 'enabled',
        DATADOG_API_KEY: DATADOG_API_KEY ?? '',
        DATADOG_SOURCE: DATADOG_SOURCE ?? '',
        DATADOG_INTAKE_REGION: DATADOG_INTAKE_REGION ?? '',
        REQ_RES_PAYLOAD_LOGGING_ENABLED: REQ_RES_PAYLOAD_LOGGING_ENABLED === 'enabled',
        DHL_IT_MIDDLEWARE_API_KEY,
        DHL_IT_API_HOST,
        DHL_IT_AUTH_CLIENT_ID,
        DHL_IT_AUTH_CLIENT_SECRET,
        BLOQIT_COURIER_BYPASS_PIN,
        DHL_IT_PARTNER_ID,
        DHL_IT_MOCK_PARTNER_ID: process.env.DHL_IT_MOCK_PARTNER_ID,
        DHL_IT_BATCH_EXPIRED_RENTS_NOTIFICATION_WINDOW_START,
        DHL_IT_BATCH_EXPIRED_RENTS_NOTIFICATION_WINDOW_END,
        DHL_IT_BATCH_EXPIRED_RENT_NOTIFICATIONS_DRY_RUN_ENABLED,
        PARTNER_API_MOCK_SERVER_HOST: process.env.PARTNER_API_MOCK_SERVER_HOST,
        GLOBAL_HTTP_CLIENT_TIMEOUT_MS: globalHttpClientTimeoutMs,
        INPOST_PARTNER_ID,
        INPOST_OAUTH_HOST,
        INPOST_API_HOST,
        INPOST_OAUTH_CLIENT_ID,
        INPOST_OAUTH_CLIENT_SECRET,
        GENERIC_API_KEY: process.env.GENERIC_API_KEY,
        INPOST_AUTH_HOST: process.env.INPOST_AUTH_HOST,
        INPOST_MIDDLEWARE_API_KEY,
        INPOST_BLOQIT_API_KEY,
    };
}
