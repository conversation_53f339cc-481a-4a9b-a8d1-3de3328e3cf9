import { type DocumentDatabaseClient } from '..';

export class InProcessMemoryDocumentDatabaseClient<T> implements DocumentDatabaseClient<T> {
    private readonly data: T[] = [];

    constructor() {
        this.data = [];
    }

    async findByIdAndDelete(id: string): Promise<void> {
        await this.removeOne({ id });
    }

    async findByIdAndUpdate(id: string, set: any): Promise<void> {
        await this.updateOne({ filter: { id }, set });
    }

    async list(_criteria: any): Promise<T[]> {
        return this.data;
    }

    async removeOne(filter: any): Promise<void> {
        this.data.splice(this.findIndex(filter), 1);
    }

    async save(obj: T): Promise<void> {
        this.data.push(obj);
    }

    async findBy(filter: any): Promise<T | undefined> {
        return await Promise.resolve(
            this.data.find(obj =>
                Object.entries(filter as ArrayLike<unknown>).every(
                    ([key, value]) => obj[key] === value,
                ),
            ),
        );
    }

    async updateOne(props: { filter: any; set: any }): Promise<void> {
        const { filter, set } = props;
        const record = await this.findBy(filter);
        const recordIndex = this.findIndex(filter);

        if (recordIndex === -1)
            throw new Error(
                'Failed to process update operation. The requested record was not found.',
            );

        this.data[recordIndex] = { ...record, ...set };
    }

    private findIndex(filter: any): number {
        return this.data.findIndex(obj =>
            Object.entries(filter as ArrayLike<unknown>).every(
                ([key, value]) => obj[key] === value,
            ),
        );
    }
}
