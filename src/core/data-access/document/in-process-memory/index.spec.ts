import { InProcessMemoryDocumentDatabaseClient } from '.';

interface DummyDataStructure {
    field1: string;
    field2: string;
    field3?: string;
}

describe('InProcessMemoryDocumentDatabaseClient', () => {
    it('should allow for storing and retrieving an object on the local process memory', async () => {
        const dataRecord1 = { field1: 'test1', field2: 'test1' };
        const dataRecord2 = { field1: 'test2', field2: 'test2' };
        const dataRecord3 = { field1: 'test3', field2: 'test3', field3: 'test3' };

        const client = new InProcessMemoryDocumentDatabaseClient<DummyDataStructure>();
        await client.save(dataRecord1);
        await client.save(dataRecord2);
        await client.save(dataRecord3);

        expect(await client.findBy({ field1: 'test1' })).toEqual(dataRecord1);
        expect(await client.findBy({ field2: 'test1' })).toEqual(dataRecord1);

        expect(await client.findBy({ field1: 'test2' })).toEqual(dataRecord2);
        expect(await client.findBy({ field2: 'test2' })).toEqual(dataRecord2);

        expect(await client.findBy({ field1: 'test3' })).toEqual(dataRecord3);
        expect(await client.findBy({ field3: 'test3' })).toEqual(dataRecord3);
    });

    it('should allow to update an object matching a filter', async () => {
        const dataRecord1 = { field1: 'test1', field2: 'test2' };

        const client = new InProcessMemoryDocumentDatabaseClient<DummyDataStructure>();
        await client.save(dataRecord1);

        await client.updateOne({
            filter: { field1: 'test1', field2: 'test2' },
            set: { field3: 'test3' },
        });

        expect(await client.findBy({ field1: 'test1', field2: 'test2', field3: 'test3' })).toEqual({
            field1: 'test1',
            field2: 'test2',
            field3: 'test3',
        });
    });

    it('should allow to delete a record matching a filter', async () => {
        const dataRecord1 = { field1: 'test1', field2: 'test1' };

        const client = new InProcessMemoryDocumentDatabaseClient<DummyDataStructure>();
        await client.save(dataRecord1);

        await client.removeOne({ field1: 'test1' });
        const retrieved = await client.findBy({ field1: 'test1' });
        expect(retrieved).not.toBeDefined();
    });

    it('should throw an error if record was not found', async () => {
        const client = new InProcessMemoryDocumentDatabaseClient<DummyDataStructure>();

        await expect(
            client.updateOne({ filter: { field1: 'test1' }, set: { field3: 'test3' } }),
        ).rejects.toThrow(
            'Failed to process update operation. The requested record was not found.',
        );
    });
});
