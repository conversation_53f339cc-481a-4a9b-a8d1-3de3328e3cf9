import { type DocumentDatabaseClient, DocumentDatabaseImplementationStrategy } from '..';
import * as MongoDB from '../mongodb';
import { type IdMapping } from '../../../identifier-mapping/repository';
import { InProcessMemoryDocumentDatabaseClient } from '../in-process-memory';
import { type Logger } from '../../../logger';
import {
    type ExpiredRent,
    type CourierPickupSession,
    type ExpiredRentsNotificationBatch,
} from '../../../../dhl/it/models';
import type { MappedLocker } from '../../../../bloqit/lockers/models';
import { LabelAssociation } from '../../../../inpost/models';

export async function setup(props: {
    dbURL: string;
    dbName: string;
    strategy: DocumentDatabaseImplementationStrategy;
    logger: Logger;
}): Promise<{
    idMappingCollection: DocumentDatabaseClient<IdMapping>;
    expiredRentsCollection: DocumentDatabaseClient<ExpiredRent>;
    expiredRentsNotificationBatchCollection: DocumentDatabaseClient<ExpiredRentsNotificationBatch>;
    courierPickupSessionsCollection: DocumentDatabaseClient<CourierPickupSession>;
    mappedLockersCollection: DocumentDatabaseClient<MappedLocker>;
    labelAssociationCollection: DocumentDatabaseClient<LabelAssociation>;
}> {
    const { dbURL, dbName, logger, strategy } = props;
    let collections: {
        idMappingCollection: DocumentDatabaseClient<IdMapping>;
        expiredRentsCollection: DocumentDatabaseClient<ExpiredRent>;
        expiredRentsNotificationBatchCollection: DocumentDatabaseClient<ExpiredRentsNotificationBatch>;
        courierPickupSessionsCollection: DocumentDatabaseClient<CourierPickupSession>;
        mappedLockersCollection: DocumentDatabaseClient<MappedLocker>;
        labelAssociationCollection: DocumentDatabaseClient<LabelAssociation>;
    };

    switch (strategy) {
        case DocumentDatabaseImplementationStrategy.MONGODB:
            collections = await MongoDB.setup({ dbURL, dbName });
            break;
        case DocumentDatabaseImplementationStrategy.IN_PROCESS_MEMORY:
            collections = {
                idMappingCollection: new InProcessMemoryDocumentDatabaseClient<IdMapping>(),
                expiredRentsCollection: new InProcessMemoryDocumentDatabaseClient<ExpiredRent>(),
                expiredRentsNotificationBatchCollection:
                    new InProcessMemoryDocumentDatabaseClient<ExpiredRentsNotificationBatch>(),
                courierPickupSessionsCollection:
                    new InProcessMemoryDocumentDatabaseClient<CourierPickupSession>(),
                mappedLockersCollection: new InProcessMemoryDocumentDatabaseClient<MappedLocker>(),
                labelAssociationCollection:
                    new InProcessMemoryDocumentDatabaseClient<LabelAssociation>(),
            };
            break;
        default:
            throw new Error(`Unknown DocumentDatabaseImplementationStrategy`);
    }

    logger.info({ message: `Using ${strategy.toString()} as document database` });
    return collections;
}
