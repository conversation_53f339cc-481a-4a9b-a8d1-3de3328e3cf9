export enum DocumentDatabaseImplementationStrategy {
    MONGODB = 'MONGO_DB',
    IN_PROCESS_MEMORY = 'IN_PROCESS_MEMORY',
}

export interface DocumentDatabaseClient<T> {
    save: (value: T) => Promise<void>;
    findBy: (filter: any) => Promise<T | undefined>;
    updateOne: (props: { filter: any; set: any }) => Promise<void>;
    removeOne: (filter: any) => Promise<void>;
    list: (criteria: any) => Promise<T[]>;
    findByIdAndUpdate: (id: string, set: any) => Promise<void>;
    findByIdAndDelete: (id: string) => Promise<void>;
}
