import { type DocumentDatabaseClient } from '..';

export class FakeDocumentDatabaseClient<T> implements DocumentDatabaseClient<T> {
    async findByIdAndDelete(_id: string): Promise<void> {
        throw new Error('Method not implemented. Please override it for your test purposes.');
    }

    async findByIdAndUpdate(_id: string, _set: any): Promise<void> {
        throw new Error('Method not implemented. Please override it for your test purposes.');
    }

    async list(_criteria: any): Promise<T[]> {
        throw new Error('Method not implemented. Please override it for your test purposes.');
    }

    async removeOne(_filter: any): Promise<void> {
        throw new Error('Method not implemented. Please override it for your test purposes.');
    }

    async save(_value: T): Promise<void> {
        throw new Error('Method not implemented. Please override it for your test purposes.');
    }

    async findBy<T = any>(_filter: object): Promise<T> {
        throw new Error('Method not implemented. Please override it for your test purposes.');
    }

    async updateOne(_props: { filter: any; set: T }): Promise<void> {
        throw new Error('Method not implemented. Please override it for your test purposes.');
    }
}
