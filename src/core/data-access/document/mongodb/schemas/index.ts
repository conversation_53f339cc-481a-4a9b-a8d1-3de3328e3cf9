import mongoose from 'mongoose';
import { IdMappingType } from '../../../../identifier-mapping/repository';
import { LockerType, LockerUsageType } from '../../../../../bloqit/lockers/models';

export const idMappingSchema = new mongoose.Schema({
    bloqitId: String,
    sharedId: String,
    partnerId: String,
    partnerFriendlyId: String,
    idType: { type: String, enum: Object.values(IdMappingType) },
});

export const courierPickupSessionSchema = new mongoose.Schema({
    courierId: String,
    sessionId: String,
    products: [
        {
            productId: String,
            product: String,
            isMissing: Boolean,
            isExpired: Boolean,
        },
    ],
});
export interface ExpiredRent {
    rentId: string;
    partnerFriendlyId: string;
    metadata: any;
}
export const expiredRentSchema = new mongoose.Schema({
    rentId: String,
    partnerFriendlyId: String,
    metadata: Object,
});

export const expiredRentsNotificationBatchSchema = new mongoose.Schema({
    failureCount: Number,
    rentIds: [String],
    createdAt: Date,
});

export const mappedLockerSchema = new mongoose.Schema({
    bloqId: String,
    sharedId: String,
    layout: [
        {
            lockerId: String,
            lockerTitle: String,
            partnerName: String,
            usageType: { type: String, enum: Object.values(LockerUsageType) },
            partnerUsageType: String,
            size: { type: String, enum: Object.values(LockerType) },
            partnerSize: String,
            column: Number,
            row: Number,
        },
    ],
});

export const labelAssociationSchema = new mongoose.Schema({
    partnerRentId: String,
    label: String,
    createdAt: Date,
});
