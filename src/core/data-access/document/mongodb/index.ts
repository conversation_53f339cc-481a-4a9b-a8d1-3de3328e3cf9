import mongoose from 'mongoose';
import * as schemas from './schemas';
import { type DocumentDatabaseClient } from '..';
import { type IdMapping } from '../../../identifier-mapping/repository';
import {
    CourierPickupSessionsDatabaseClient,
    ExpiredRentsDatabaseClient,
    ExpiredRentsNotificationBatchDatabaseClient,
    IdMappingDatabaseClient,
    MappedLockersDatabaseClient,
    LabelAssociationDatabaseClient,
} from './collections';
import {
    type ExpiredRent,
    type CourierPickupSession,
    type ExpiredRentsNotificationBatch,
} from '../../../../dhl/it/models';
import { MappedLocker } from '../../../../bloqit/lockers/models';
import { LabelAssociation } from '../../../../inpost/models';

export async function setup(props: { dbURL: string; dbName: string }): Promise<{
    idMappingCollection: DocumentDatabaseClient<IdMapping>;
    expiredRentsCollection: DocumentDatabaseClient<ExpiredRent>;
    courierPickupSessionsCollection: DocumentDatabaseClient<CourierPickupSession>;
    expiredRentsNotificationBatchCollection: DocumentDatabaseClient<ExpiredRentsNotificationBatch>;
    mappedLockersCollection: DocumentDatabaseClient<MappedLocker>;
    labelAssociationCollection: DocumentDatabaseClient<LabelAssociation>;
}> {
    const conn = await mongoose.connect(`${props.dbURL}/${props.dbName}`);

    conn.model<IdMapping>('IdMapping', schemas.idMappingSchema);
    const idMappingCollection = new IdMappingDatabaseClient({
        collection: conn.model<IdMapping>('IdMapping'),
    });

    conn.model<CourierPickupSession>('courierPickupSession', schemas.courierPickupSessionSchema);
    const courierPickupSessionsCollection = new CourierPickupSessionsDatabaseClient({
        collection: conn.model<CourierPickupSession>('courierPickupSession'),
    });

    conn.model<ExpiredRent>('expiredRent', schemas.expiredRentSchema);
    const expiredRentsCollection = new ExpiredRentsDatabaseClient({
        collection: conn.model<ExpiredRent>('expiredRent'),
    });

    conn.model<ExpiredRentsNotificationBatch>(
        'expiredRentsNotificationBatch',
        schemas.expiredRentsNotificationBatchSchema,
    );

    const expiredRentsNotificationBatchCollection = new ExpiredRentsNotificationBatchDatabaseClient(
        { collection: conn.model<ExpiredRentsNotificationBatch>('expiredRentsNotificationBatch') },
    );

    const mappedLockersModel = conn.model<MappedLocker>('mappedLocker', schemas.mappedLockerSchema);

    const mappedLockersCollection = new MappedLockersDatabaseClient({
        collection: mappedLockersModel,
    });

    const labelAssociationModel = conn.model<LabelAssociation>(
        'labelAssociation',
        schemas.labelAssociationSchema,
    );
    const labelAssociationCollection = new LabelAssociationDatabaseClient({
        collection: labelAssociationModel,
    });

    return {
        idMappingCollection,
        courierPickupSessionsCollection,
        expiredRentsCollection,
        expiredRentsNotificationBatchCollection,
        mappedLockersCollection,
        labelAssociationCollection,
    };
}
