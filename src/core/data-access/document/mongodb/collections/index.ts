import mongoose from 'mongoose';
import { type DocumentDatabaseClient } from '../..';
import { type IdMapping } from '../../../../identifier-mapping/repository';
import {
    type ExpiredRent,
    type CourierPickupSession,
    type ExpiredRentsNotificationBatch,
} from '../../../../../dhl/it/models';
import { type FilterQuery, type UpdateQuery } from 'mongoose';
import { MappedLocker } from '../../../../../bloqit/lockers/models';
import { LabelAssociation } from '../../../../../inpost/models';

export abstract class BaseDocumentDatabaseClient<T> implements DocumentDatabaseClient<T> {
    protected readonly model: mongoose.Model<T>;

    constructor(props: { collection: mongoose.Model<T> }) {
        this.model = props.collection;
    }

    async save(value: T): Promise<void> {
        await this.model.create(value);
    }

    async findBy(filter: any): Promise<T | undefined> {
        const result = await this.model.findOne<T>(filter as FilterQuery<T>);
        return result ?? undefined;
    }

    async updateOne(props: any): Promise<void> {
        const { filter, set } = props;
        const result = await this.model.updateOne(filter as FilterQuery<T>, set as UpdateQuery<T>);

        if (result.matchedCount === 0) {
            throw new Error(
                'Failed to process update operation. The requested record was not found.',
            );
        }
    }

    async findByIdAndUpdate(id: string, set: any): Promise<void> {
        await this.model.findByIdAndUpdate(new mongoose.Types.ObjectId(id), set as UpdateQuery<T>);
    }

    async removeOne(filter: any): Promise<void> {
        await this.model.deleteOne(filter as FilterQuery<IdMapping>);
    }

    async findByIdAndDelete(id: string): Promise<void> {
        await this.model.findByIdAndDelete(new mongoose.Types.ObjectId(id));
    }

    async list(criteria: any): Promise<T[]> {
        return await this.model.find(criteria as FilterQuery<T>);
    }
}

export class IdMappingDatabaseClient extends BaseDocumentDatabaseClient<IdMapping> {}

export class CourierPickupSessionsDatabaseClient extends BaseDocumentDatabaseClient<CourierPickupSession> {}

export class ExpiredRentsDatabaseClient extends BaseDocumentDatabaseClient<ExpiredRent> {}

export class ExpiredRentsNotificationBatchDatabaseClient extends BaseDocumentDatabaseClient<ExpiredRentsNotificationBatch> {
    async list(criteria: any): Promise<ExpiredRentsNotificationBatch[]> {
        const result = await this.model.find(
            criteria as FilterQuery<ExpiredRentsNotificationBatch>,
        );

        return result.map(raw => {
            const obj = raw.toObject();
            return { id: obj._id.toString(), rentIds: obj.rentIds, failureCount: obj.failureCount };
        });
    }
}

export class MappedLockersDatabaseClient extends BaseDocumentDatabaseClient<MappedLocker> {}

export class LabelAssociationDatabaseClient extends BaseDocumentDatabaseClient<LabelAssociation> {}
