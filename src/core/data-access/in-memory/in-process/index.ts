import { type InMemoryDatabaseClient } from '..';

export class InProcessMemoryDatabaseClient implements InMemoryDatabaseClient {
    private database: object;

    constructor() {
        this.database = {};
    }

    async get<T = string>(key: string): Promise<T> {
        return this.database[key];
    }

    async set<T = string>(key: string, value: T): Promise<void> {
        this.database[key] = value;
    }

    async remove(key: string): Promise<void> {
        this.database[key] = null;
    }
}
