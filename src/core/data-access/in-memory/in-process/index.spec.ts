import { InProcessMemoryDatabaseClient } from '.';

interface MyType {
    value: string;
}

describe('InProcessMemoryDatabaseClient', () => {
    it('should allow to perform set and get operations on a key using strings as value', async () => {
        const client = new InProcessMemoryDatabaseClient();

        await client.set('key', 'value');
        const value = await client.get('key');

        expect(value).toEqual('value');
    });

    it('should allow to perform set and get operations on a key using a complex data structure', async () => {
        const client = new InProcessMemoryDatabaseClient();

        const obj: MyType = { value: 'value' };
        await client.set<MyType>('key', obj);
        const value = await client.get<MyType>('key');

        expect(value).toEqual({ value: 'value' });
    });
});
