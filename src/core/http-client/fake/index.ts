import { type HTT<PERSON>lient } from '..';

export class FakeHTTP<PERSON>lient implements HTTPClient {
    async post<T = any>(_args: {
        url: string;
        data: any;
        headers?: any;
        httpsAgentOptions?: { rejectUnauthorized: boolean };
        validateStatus?: (status: number) => boolean;
    }): Promise<{ status: number; data: T }> {
        throw new Error('Method not implemented. Please override it for your test purposes.');
    }

    async request<T>(_props: {
        method: string;
        url: string;
        headers: any;
        httpsAgentOptions?: { rejectUnauthorized: boolean };
        data: any;
    }): Promise<T> {
        throw new Error('Method not implemented. Please override it for your test purposes.');
    }
}
