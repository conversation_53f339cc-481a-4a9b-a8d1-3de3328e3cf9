export interface HTTPClient {
    request: <T>(props: {
        method: string;
        url: string;
        headers: any;
        httpsAgentOptions?: { rejectUnauthorized: boolean };
        data: any;
        validateStatus?: (status: number) => boolean;
    }) => Promise<T>;

    post: <T = any>(args: {
        url: string;
        data: any;
        headers?: any;
        httpsAgentOptions?: { rejectUnauthorized: boolean };
        validateStatus?: (status: number) => boolean;
    }) => Promise<{ status: number; data: T }>;
}
