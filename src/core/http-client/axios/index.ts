import https from 'https';
import { type AxiosInstance } from 'axios';
import { type HTTPClient } from '..';

export const StatusValidationStrategy = {
    TWO_XX_RANGE_ONLY: (status: number) => status >= 200 && status < 300,
    ACCEPT_ALL: (_: number) => true,
};

export class AxiosHTTPClient implements HTTPClient {
    private readonly axiosClient: AxiosInstance;
    private readonly validateStatusFn?: (status: number) => boolean;

    constructor(deps: {
        axiosClient: AxiosInstance;
        validateStatusFn?: (status: number) => boolean;
    }) {
        this.axiosClient = deps.axiosClient;
        this.validateStatusFn = deps.validateStatusFn;
        this.request = this.request.bind(this);
    }

    async request<T>(props: {
        method: string;
        url: string;
        headers: any;
        httpsAgentOptions?: { rejectUnauthorized: boolean };
        validateStatus?: (status: number) => boolean;
        data: any;
    }): Promise<T> {
        return await this.axiosClient.request({
            method: props.method,
            url: props.url,
            headers: props.headers,
            httpsAgent: new https.Agent(props.httpsAgentOptions),
            data: props.data,
            validateStatus: props.validateStatus ?? this.validateStatusFn,
        });
    }

    async post<T = any>(args: {
        url: string;
        data: any;
        headers?: any;
        httpsAgentOptions?: { rejectUnauthorized: boolean };
        validateStatus?: (status: number) => boolean;
    }): Promise<{ status: number; data: T }> {
        const { url, data, headers, validateStatus, httpsAgentOptions } = args;
        const commonHeaders = { 'Content-Type': 'application/json', Connection: 'keep-alive' };

        return await this.request({
            url,
            data,
            method: 'post',
            httpsAgentOptions: { rejectUnauthorized: false, ...httpsAgentOptions },
            headers: { ...commonHeaders, ...headers },
            validateStatus: validateStatus ?? this.validateStatusFn,
        });
    }
}
