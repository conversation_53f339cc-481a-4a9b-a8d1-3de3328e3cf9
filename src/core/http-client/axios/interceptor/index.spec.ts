/* eslint-disable @typescript-eslint/unbound-method */
import { type InternalAxiosRequestConfig, type AxiosResponse } from 'axios';
import { AxiosInterceptor } from '.';
import { FakeLogger } from '../../../logger/fake';

describe('AxiosInterceptor', () => {
    const logger = new FakeLogger();

    beforeEach(() => jest.spyOn(logger, 'info').mockReturnValue(undefined));
    afterEach(() => jest.clearAllMocks());

    describe('interceptRequest', () => {
        it('should log requests', () => {
            const axiosInterceptor = new AxiosInterceptor({ logger });
            const config = {
                method: 'GET',
                url: 'https://bloq.it',
                headers: { 'Content-Type': 'application/json' },
                data: { foo: 'bar' },
            } as unknown as InternalAxiosRequestConfig<any>;

            axiosInterceptor.interceptRequest(config);
            expect(logger.info).toHaveBeenCalledTimes(1);
            expect(logger.info).toHaveBeenCalledWith({
                message: 'GET https://bloq.it',
                body: { foo: 'bar' },
                headers: { 'Content-Type': 'application/json' },
                url: 'https://bloq.it',
            });
        });
    });

    describe('interceptResponse', () => {
        it('should log requests', () => {
            const axiosInterceptor = new AxiosInterceptor({ logger });
            const config = {
                status: 200,
                headers: { 'Content-Type': 'application/json' },
                data: { foo: 'bar' },
                request: { method: 'GET', path: 'https://bloq.it' },
            } as unknown as AxiosResponse<any, any>;

            axiosInterceptor.interceptResponse(config);
            expect(logger.info).toHaveBeenCalledTimes(1);
            expect(logger.info).toHaveBeenCalledWith({
                message: 'GET https://bloq.it 200',
                body: { foo: 'bar' },
                headers: { 'Content-Type': 'application/json' },
                url: 'https://bloq.it',
            });
        });
    });

    describe('interceptError', () => {
        it('should log errors', async () => {
            const axiosInterceptor = new AxiosInterceptor({ logger });
            const request = {
                method: 'GET',
                path: 'https://bloq.it',
                data: { foo: 'bar' },
                headers: { 'Content-Type': 'application/json' },
            };
            const response = {
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                data: { foo: 'bar' },
            };

            const spyOnError = jest.spyOn(logger, 'error').mockReturnValue(undefined);

            try {
                await axiosInterceptor.interceptError({ request, response });
            } catch (_ex) {
                expect(spyOnError).toHaveBeenCalledTimes(1);
                expect(spyOnError).toHaveBeenCalledWith({
                    message: '[external_request_error]: GET https://bloq.it 400',
                    req: {
                        body: { foo: 'bar' },
                        headers: { 'Content-Type': 'application/json' },
                        url: 'https://bloq.it',
                    },
                    res: {
                        headers: { 'Content-Type': 'application/json' },
                        body: { foo: 'bar' },
                    },
                });
            }
        });
    });
});
