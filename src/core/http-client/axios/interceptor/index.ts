import { type InternalAxiosRequestConfig, type AxiosResponse } from 'axios';
import { type Logger } from '../../../logger';

export class AxiosInterceptor {
    private readonly logger: Logger;

    constructor(deps: { logger: Logger }) {
        this.logger = deps.logger;
        this.interceptRequest = this.interceptRequest.bind(this);
        this.interceptResponse = this.interceptResponse.bind(this);
        this.interceptError = this.interceptError.bind(this);
    }

    interceptRequest(config: InternalAxiosRequestConfig<any>): InternalAxiosRequestConfig<any> {
        const { method, url, headers, data } = config;

        if (method === undefined || url === undefined) return config;

        this.logger.info({ message: `${method.toUpperCase()} ${url}`, body: data, headers, url });

        return config;
    }

    interceptResponse(config: AxiosResponse<any, any>): AxiosResponse<any, any> {
        const { request, headers, data, status } = config;

        const reqMethod: string = request?.method?.toString().toUpperCase();
        const reqPath: string = request?.path?.toString();

        this.logger.info({
            message: `${reqMethod} ${reqPath} ${status}`,
            url: reqPath,
            body: data,
            headers,
        });

        return config;
    }

    async interceptError(err: any): Promise<never> {
        const { request, response } = err;

        const reqInfo = this.extractRequestInfo(request);
        const { method: reqMethod, path: reqPath, data: reqBody } = reqInfo;

        this.logger.error({
            message: `[external_request_error]: ${reqMethod} ${reqPath} ${response?.status}`,
            req: { body: reqBody, headers: request?.headers, url: request?.path },
            res: { headers: response?.headers, body: response?.data },
        });

        return await Promise.reject(err);
    }

    private extractRequestInfo(request?: any): { method: string; path: string; data: any } {
        const reqMethod: string = request?.method?.toString().toUpperCase();
        const reqPath: string = request?.path?.toString();
        const reqBody = this.parseRequestBody(request?.data);

        return { method: reqMethod, path: reqPath, data: reqBody };
    }

    private parseRequestBody(body?: any): any {
        /*
            This is a workaround to handle Axios' request body being a string. We need it to be an
            object so we can filter it propertly at Datadog. If the body is not parseable, though,
            we just keep it as is
        */

        if (body === undefined) return body;
        try {
            return JSON.parse(body as string);
        } catch (ex) {
            return body;
        }
    }
}
