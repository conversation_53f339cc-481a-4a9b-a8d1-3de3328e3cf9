import type { HTTPRequest, HTTPResponse } from '../../../http/models/extensions/express';
import type { PartnerRegistry } from '../../../core/partner-registry';
import type { LockerChangeState, LockerMaintenanceStatusUpdateEvent } from '../models';
import { OK, INTERNAL_SERVER_ERROR, NO_CONTENT } from '../../../http/models/codes';
import { type Logger } from '../../../core/logger';

export interface LockerChangeStateRequest extends HTTPRequest {
    body: LockerChangeState;
}

export interface LockerMaintenanceStatusUpdateEventHTTPRequest extends HTTPRequest {
    body: LockerMaintenanceStatusUpdateEvent;
}

export class LockersController {
    private readonly partnerRegistry: PartnerRegistry;
    private readonly logger: Logger;

    constructor(deps: { partnerRegistry: PartnerRegistry; logger: Logger }) {
        this.logger = deps.logger;
        this.partnerRegistry = deps.partnerRegistry;
        this.changeLockerState = this.changeLockerState.bind(this);
        this.maintenanceStatusUpdate = this.maintenanceStatusUpdate.bind(this);
    }

    async changeLockerState(
        req: LockerChangeStateRequest,
        res: HTTPResponse,
    ): Promise<HTTPResponse> {
        try {
            const partnerId = req.partnerId;
            if (partnerId === undefined) throw new Error('Missing partnerId');

            const partnerHandler = this.partnerRegistry.get(partnerId);
            const response = await partnerHandler?.setConfig(req.body);
            return res.status(response?.success === true ? OK : INTERNAL_SERVER_ERROR).send();
        } catch (ex) {
            const err = ex as Error;
            this.logger.error({ message: err.message });
            return res.status(INTERNAL_SERVER_ERROR).end();
        }
    }

    async maintenanceStatusUpdate(
        req: LockerMaintenanceStatusUpdateEventHTTPRequest,
        res: HTTPResponse,
    ): Promise<HTTPResponse> {
        try {
            const partnerId = req.partnerId;
            if (partnerId === undefined) throw new Error('Missing partnerId');

            const partnerHandler = this.partnerRegistry.get(partnerId);
            await partnerHandler?.maintenanceStatusUpdate(req.body);
            return res.status(NO_CONTENT).send();
        } catch (ex) {
            const err = ex as Error;
            this.logger.error({ message: err.message });
            return res.status(INTERNAL_SERVER_ERROR).end();
        }
    }
}
