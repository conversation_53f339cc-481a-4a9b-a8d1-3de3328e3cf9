import { BloqObjectResponse } from '../../bloqs/models';
import { LockerType } from '../../lockers/models';

export enum Roles {
    CUSTOMER = 'Customer',
    COURIER = 'Courier',
}

export enum ActionInitiatedBySources {
    BLOQT_IT_ADMIN = 'bloq_it_admin',
    LOCKER = 'locker',
    PUBLIC_API = 'public_api',
    EXPIRATION = 'expiration',
    CRONJOB = 'cronjob',
}

export interface Rent {
    /** @property {string} externalID Matches the parcelidentifier on DHL's side */
    externalID: string;

    /** @property {object} pickUpCodes Object containing all pick up codes related to the rent */
    pickUpCodes: object | undefined;

    /** @property {string} dropOffCode Matches the parcelidentifier on DHL's side */
    dropOffCode: string;

    /** @property {object} customer Information related to the customer attached to the rent */
    customer: Customer | undefined;

    dimensions?: {
        length: number;
        width: number;
        height: number;
    };

    /** @property {PrePickupAction[]} prePickupActions Array of action to be made before opening the door on a pickup */
    prePickupActions: PrePickupAction[] | undefined;

    /** @property {PostPickupAction[]} postPickupAction Array of action to be made after opening the door on a pickup */
    postPickupAction: PostPickupAction[] | undefined;

    /** @property {string} setId Matches the set id on DHL's side */
    setID: string | undefined;

    /** @property {number} priority Defines the priority of pickup */
    priority: number | undefined;

    /** @property {Date} expiryDate Defines the date the rent should expire */
    expiryDate?: Date;

    /** @property {Record<string, unknown>} metadata Stores some information about the rent only relevant to the partner */
    metadata?: Record<string, unknown>;

    /** @property {string} carrier Carrier for this rent */
    carrier?: string;

    /** @property {string} state State of the rent */
    state?: string;
}

export interface RentDelivery extends Rent {
    /** @property {string} desiredLockerType Specify the type of locker to use for this rent  */
    desiredLockerType?: LockerType;
}

export interface Customer {
    email?: string;
    phone?: string;
    name?: string;
}

export interface EventCustomer {
    email: string;
    phone?: string;
    name?: string;
}

export interface ActionInitiatedBy {
    role?: Roles;
    id: string;
    session?: string;
    source?: ActionInitiatedBySources;
}
export interface RentEvent {
    _id?: string;
    customer: EventCustomer;
    courier?: string;
    rent: string;
    bloq: string;
    locker: string;
    message?: string;
    timestamp: Date;
    functionalityContext?: FunctionalityContext;
    uiFlowContext?: string;
    actionInitiatedBy: ActionInitiatedBy;
    metadata: Record<string, any>;
    title?: string;
    description?: string;
}

export enum OperationType {
    FIRST_MILE = 'firstMile',
    LAST_MILE = 'lastMile',
}

export enum PrePickupActionType {
    VERIFY_PICKUP = 'verify_pickup',
    ASSOCIATE_LABEL = 'associate_label',
}

export interface PrePickupAction {
    order: number;
    action: PrePickupActionType;
}

export enum PostPickupActionType {
    SCAN_CODE = 'scan_code',
}

export interface PostPickupAction {
    order: number;
    action: PostPickupActionType;
}

export enum FunctionalityContext {
    EmergencyTakeoutFlow = 'EmergencyTakeoutFlow',
    CleanCompartmentsFlow = 'CleanCompartmentsFlow',
    CourierDropOffFlow = 'CourierDropOffFlow',
    CourierPickUpExpiredParcelsFlow = 'CourierPickUpExpiredParcelsFlow',
    CourierPickUpParcelsFlow = 'CourierPickUpParcelsFlow',
    CourierPickUpLabellessParcelsFlow = 'CourierPickUpLabellessParcelsFlow',
    CourierInspectionFlow = 'CourierInspectionFlow',
    CustomerDropOffLabellessParcelsFlow = 'CustomerDropOffLabellessParcelsFlow',
    CustomerDropOffParcelsFlow = 'CustomerDropOffParcelsFlow',
    CustomerPickUpParcelsFlow = 'CustomerPickUpParcelsFlow',
}

export enum UIFlowContext {
    EMERGENCY_TAKEOUT_FLOW = 'EmergencyTakeoutFlow',
    CLEAN_COMPARTMENTS_FLOW = 'CleanCompartmentsFlow',
    COURIER_DROPOFF_FLOW = 'CourierDropOffFlow',
    COURIER_PICKUP_EXPIRED_PARCELS_FLOW = 'CourierPickUpExpiredParcelsFlow',
    COURIER_PICKUP_PARCELS_FLOW = 'CourierPickUpParcelsFlow',
    COURIER_PICKUP_LABELLESS_PARCELS_FLOW = 'CourierPickUpLabellessParcelsFlow',
    COURIER_INSPECTION_FLOW = 'CourierInspectionFlow',
    CUSTOMER_DROPOFF_LABELLESS_PARCELS_FLOW = 'CustomerDropOffLabellessParcelsFlow',
    CUSTOMER_DROPOFF_PARCELS_FLOW = 'CustomerDropOffParcelsFlow',
    CUSTOMER_PICKUP_PARCELS_FLOW = 'CustomerPickUpParcelsFlow',
}

export interface RentObjectResponse {
    _id: string;
    state: string;
    externalID: string;
    bloq: BloqObjectResponse;
    details: {
        pickUpCodes: {
            pin: string;
        };
        dropOffCode: string;
    };
    locker?: {
        _id: string;
        lockerTitle: string;
        type: LockerType;
        bloq?: {
            _id: string;
        };
    };
}

export interface CreateRentResponse {
    _id: string;
    type: string;
    state: string;
    states: {
        state: string;
        timestamp: Date;
    }[];
    customer: string;
    locker: string;
    startDate: Date;
    expiryDate: Date;
    details: {
        pickUpCodes: {
            pin: string;
        };
        dropOffCode: string;
    };
    pricing: {
        totalPrice: number;
        openingPrice: number;
        perMinutePrice: number;
        paid: boolean;
        debt: number;
        amountPaid: number;
        discount: number;
        durationMinutes: number;
    };
    notificationOptions: string[];
}

export interface UpdateRentResponse {
    docs: Rent[];
    limit: number;
    page: number;
    totalDocs: number;
}

export enum RentState {
    CREATED = 'created',
    STARTING = 'starting',
    IN_PROGRESS = 'in_progress',
    LOCKER_OPENED = 'locker_opened',
    COLLECT_ITEM = 'collect_item',
    PRICE_CALCULATION = 'price_calculation',
    PAYMENT_ATTEMPT = 'payment_attempt',
    EXPIRED = 'expired',
    FINISHED = 'finished',
    RESERVED = 'reserved',
    CANCELLED = 'cancelled',
    IN_TRANSIT = 'in_transit',
    READY_FOR_PICKUP = 'ready_for_pickup',
}
