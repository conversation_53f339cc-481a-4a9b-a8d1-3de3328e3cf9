/* eslint-disable @typescript-eslint/unbound-method */
import { type Response } from 'express';

import { RentsController, type SearchRentRequest } from '.';
import { FakePartnerRegistry } from '../../../core/partner-registry/fake';
import FakeExpressRequestBuilder from '../../../../test/mocks/http/express/request';
import FakeExpressResponseBuilder from '../../../../test/mocks/http/express/response';
import { simplifyControllerResponse } from '../../../../test/utils';
import { PartnerResponseError } from '../../../core/utils/error';
import { NO_CONTENT, INTERNAL_SERVER_ERROR, OK } from '../../../http/models/codes';
import { FakeLogger } from '../../../core/logger/fake';
import { FakeIdMappingRepository } from '../../../core/identifier-mapping/repository/fake';
import { IdMappingType } from '../../../core/identifier-mapping/repository';
import { FakePartnerHandler } from '../../../core/partner-registry/fake/handler';
import { type Rent } from '../models/rent';
import { randomUUID } from 'node:crypto';

describe('RentsController', () => {
    const error = new Error('Underlying partner error');
    const partnerFriendlyId = 'dhl-cz';
    const rent: Rent = { id: 'rent-123' } as unknown as Rent;

    const partnerRegistry = new FakePartnerRegistry();
    const idMappingRepository = new FakeIdMappingRepository();
    const logger = new FakeLogger();
    const partnerHandler = new FakePartnerHandler();

    let res: Response;

    beforeEach(() => {
        res = new FakeExpressResponseBuilder().build();
        jest.spyOn(logger, 'error').mockReturnValueOnce(undefined);
    });

    afterEach(() => jest.clearAllMocks());

    describe('searchRent', () => {
        it('should fetch a rent requested by the courier by resolving the call to a specific partner handler', async () => {
            jest.spyOn(partnerRegistry, 'get').mockReturnValue(partnerHandler);
            jest.spyOn(partnerHandler, 'getRentById').mockResolvedValue([rent]);

            const req = new FakeExpressRequestBuilder()
                .withParams({ id: 'rent-123' })
                .withQuery({ partnerBloqId: 'partner-bloq-id-1', requestedByCourier: true })
                .build();

            req.partnerId = 'partner-123';

            const ctrl = new RentsController({ partnerRegistry, idMappingRepository, logger });

            const rawResult = await ctrl.searchRent(req as unknown as SearchRentRequest, res);
            const result = simplifyControllerResponse(rawResult);

            expect(result.body).toEqual({ rents: [rent], meta: { partnerId: 'partner-123' } });
        });

        it('should fetch a rent requested by the user by resolving the call to a specific partner handler', async () => {
            jest.spyOn(partnerRegistry, 'get').mockReturnValue(partnerHandler);
            jest.spyOn(partnerHandler, 'getRentById').mockResolvedValue([rent]);

            const req = new FakeExpressRequestBuilder()
                .withParams({ id: 'rent-123' })
                .withQuery({ partnerBloqId: 'partner-bloq-id-1' })
                .build();

            req.partnerId = 'partner-123';

            const ctrl = new RentsController({ partnerRegistry, idMappingRepository, logger });

            const rawResult = await ctrl.searchRent(req as unknown as SearchRentRequest, res);
            const result = simplifyControllerResponse(rawResult);

            expect(result.body).toEqual({ rents: [rent], meta: { partnerId: 'partner-123' } });
        });

        it('should handle a error and return a proper response by resolving the call to a specific partner handler', async () => {
            const error = new PartnerResponseError({ code: 'code-123', message: 'message-123' });
            const { message, code, stack } = error;
            jest.spyOn(partnerRegistry, 'get').mockReturnValue(partnerHandler);
            jest.spyOn(partnerHandler, 'getRentById').mockRejectedValue(error);

            const req = new FakeExpressRequestBuilder()
                .withParams({ id: 'rent-123' })
                .withQuery({ partnerBloqId: 'partner-bloq-id-1' })
                .build();

            req.partnerId = 'partner-123';

            const ctrl = new RentsController({ partnerRegistry, idMappingRepository, logger });

            const rawResult = await ctrl.searchRent(req as unknown as SearchRentRequest, res);
            const result = simplifyControllerResponse(rawResult);

            expect(result.status).toEqual(INTERNAL_SERVER_ERROR);
            expect(result.body).toEqual({
                error: { message, code, stack },
                meta: { partnerId: 'partner-123' },
            });
        });
    });

    describe('canBePickedUp', () => {
        it('should check whether or not a rent can be picked up', async () => {
            jest.spyOn(partnerRegistry, 'get').mockReturnValue(partnerHandler);
            jest.spyOn(partnerHandler, 'canBePickedUp').mockResolvedValue(true);

            const req = new FakeExpressRequestBuilder()
                .withParams({ id: 'rent-123' })
                .withQuery({ partnerBloqId: 'partner-bloq-id-1', requestedByCourier: true })
                .build();

            req.partnerId = 'partner-123';

            const ctrl = new RentsController({ partnerRegistry, idMappingRepository, logger });

            const rawResult = await ctrl.canBePickedUp(req as unknown as SearchRentRequest, res);
            const result = simplifyControllerResponse(rawResult);

            expect(result.status).toEqual(OK);
            expect(result.body).toEqual({
                isPickupAllowed: true,
                meta: { partnerId: 'partner-123' },
            });
        });

        it('should handle underlying partner errors', async () => {
            jest.spyOn(partnerRegistry, 'get').mockReturnValue(partnerHandler);
            jest.spyOn(partnerHandler, 'canBePickedUp').mockRejectedValueOnce(error);

            const req = new FakeExpressRequestBuilder()
                .withParams({ id: 'rent-123' })
                .withQuery({ partnerBloqId: 'partner-bloq-id-1', requestedByCourier: true })
                .build();

            req.partnerId = 'partner-123';

            const ctrl = new RentsController({ partnerRegistry, idMappingRepository, logger });

            const rawResult = await ctrl.canBePickedUp(req as unknown as SearchRentRequest, res);
            const result = simplifyControllerResponse(rawResult);

            const formattedError = { message: error.message, stack: error.stack };

            expect(result.status).toEqual(INTERNAL_SERVER_ERROR);
            expect(result.body).toEqual({
                meta: { partnerId: 'partner-123' },
                error: formattedError,
            });

            expect(logger.error).toHaveBeenCalledWith({
                message: 'Failed to check pick up availability',
                error: formattedError,
            });
        });
    });

    describe('dropOffConfirmationEvent', () => {
        it('send drop off confirmation event successfully', async () => {
            jest.spyOn(partnerRegistry, 'get').mockReturnValue(partnerHandler);
            jest.spyOn(partnerHandler, 'notifyDropOffConfirmation').mockResolvedValueOnce();

            const req = new FakeExpressRequestBuilder().build();
            req.partnerId = 'partner-123';

            const ctrl = new RentsController({ partnerRegistry, idMappingRepository, logger });

            const result = await ctrl.dropOffConfirmationEvent(
                req as unknown as SearchRentRequest,
                res,
            );
            expect(result.status).toEqual(NO_CONTENT);
        });

        it('send drop off confirmation event with error', async () => {
            jest.spyOn(partnerRegistry, 'get').mockReturnValue(partnerHandler);
            jest.spyOn(partnerHandler, 'notifyDropOffConfirmation').mockRejectedValue(error);

            const req = new FakeExpressRequestBuilder().build();
            req.partnerId = 'partner-123';

            const ctrl = new RentsController({ partnerRegistry, idMappingRepository, logger });

            const rawResult = await ctrl.dropOffConfirmationEvent(
                req as unknown as SearchRentRequest,
                res,
            );

            const result = simplifyControllerResponse(rawResult);

            expect(result.status).toEqual(INTERNAL_SERVER_ERROR);
            expect(logger.error).toHaveBeenCalledWith({
                message: 'Failed to notify drop off confirmation',
                error: { message: error.message, stack: error.stack },
            });
        });
    });

    describe('collectItemEvent', () => {
        it('send collect item event successfully', async () => {
            jest.spyOn(partnerRegistry, 'get').mockReturnValue(partnerHandler);
            jest.spyOn(partnerHandler, 'notifyCollectItem').mockResolvedValueOnce();

            const req = new FakeExpressRequestBuilder().build();
            req.partnerId = 'partner-123';

            const ctrl = new RentsController({ partnerRegistry, idMappingRepository, logger });

            const result = await ctrl.collectItemEvent(req as unknown as SearchRentRequest, res);
            expect(result.status).toEqual(NO_CONTENT);
        });

        it('send collect item event with error', async () => {
            jest.spyOn(partnerRegistry, 'get').mockReturnValue(partnerHandler);
            jest.spyOn(partnerHandler, 'notifyCollectItem').mockRejectedValueOnce(error);

            const req = new FakeExpressRequestBuilder().build();
            req.partnerId = 'partner-123';

            const ctrl = new RentsController({ partnerRegistry, idMappingRepository, logger });

            const rawResponse = await ctrl.collectItemEvent(
                req as unknown as SearchRentRequest,
                res,
            );

            const response = simplifyControllerResponse(rawResponse);
            expect(response.status).toEqual(INTERNAL_SERVER_ERROR);

            expect(logger.error).toHaveBeenCalledWith({
                message: 'Failed to notify collect item',
                errMsg: error.message,
                stack: error.stack,
                originalMessage: undefined,
            });
        });
    });

    describe('rentExpiredEvent', () => {
        it('send expired rent event successfully', async () => {
            jest.spyOn(partnerRegistry, 'get').mockReturnValue(partnerHandler);
            jest.spyOn(partnerHandler, 'notifyExpiredRent').mockResolvedValueOnce();

            const req = new FakeExpressRequestBuilder().build();
            req.partnerId = 'partner-123';

            const ctrl = new RentsController({ partnerRegistry, idMappingRepository, logger });

            const result = await ctrl.rentExpiredEvent(req as unknown as SearchRentRequest, res);
            expect(result.status).toEqual(NO_CONTENT);
        });

        it('send expired rent event with error', async () => {
            jest.spyOn(partnerRegistry, 'get').mockReturnValue(partnerHandler);
            jest.spyOn(partnerHandler, 'notifyExpiredRent').mockRejectedValueOnce(error);

            const formattedError = { message: error.message, stack: error.stack };

            const req = new FakeExpressRequestBuilder().build();
            req.partnerId = 'partner-123';

            const ctrl = new RentsController({ partnerRegistry, idMappingRepository, logger });

            const rawResponse = await ctrl.rentExpiredEvent(
                req as unknown as SearchRentRequest,
                res,
            );

            const response = simplifyControllerResponse(rawResponse);
            expect(response.status).toEqual(INTERNAL_SERVER_ERROR);

            expect(logger.error).toHaveBeenCalledWith({
                message: 'Failed to notify expired rent',
                error: formattedError,
            });
        });
    });

    describe('batchNotifyExpiredRents', () => {
        it('should notify the partner about a batch of expired rents', async () => {
            jest.spyOn(partnerRegistry, 'get').mockReturnValue(partnerHandler);
            jest.spyOn(partnerHandler, 'notifyExpiredRents').mockResolvedValueOnce();

            const batchSize = 1;
            const req = new FakeExpressRequestBuilder().withBody({ batchSize }).build();
            req.partnerId = 'partner-123';

            const ctrl = new RentsController({ partnerRegistry, idMappingRepository, logger });

            const result = await ctrl.batchNotifyExpiredRents(
                req as unknown as SearchRentRequest,
                res,
            );

            expect(result.status).toEqual(NO_CONTENT);
            expect(partnerHandler.notifyExpiredRents).toHaveBeenCalledTimes(1);
            expect(partnerHandler.notifyExpiredRents).toHaveBeenCalledWith({ batchSize: 1 });
        });

        it('should not perform the request if dry run is enabled', async () => {
            jest.spyOn(logger, 'info').mockImplementationOnce(() => {});
            jest.spyOn(partnerRegistry, 'get').mockReturnValue(partnerHandler);

            const rents = [
                {
                    rentExternalId: randomUUID(),
                    bloqExternalId: randomUUID(),
                    productType: 'product-type-1',
                },
                {
                    rentExternalId: randomUUID(),
                    bloqExternalId: randomUUID(),
                    productType: 'product-type-2',
                },
            ];

            const req = new FakeExpressRequestBuilder().withBody({ rents }).build();
            req.partnerId = 'partner-123';

            const ctrl = new RentsController({
                partnerRegistry,
                idMappingRepository,
                logger,
                batchNotificationsOfExpiredRentsDryRunEnabled: true,
            });

            const result = await ctrl.batchNotifyExpiredRents(
                req as unknown as SearchRentRequest,
                res,
            );

            expect(result.status).toEqual(NO_CONTENT);
            expect(partnerHandler.notifyExpiredRents).toHaveBeenCalledTimes(0);

            expect(logger.info).toHaveBeenCalledTimes(1);
            expect(logger.info).toHaveBeenCalledWith({
                message: 'Received request to send a batch notification related to expired rents',
                payload: req.body,
            });
        });
    });

    describe('doorStuckEvent', () => {
        it('send door stuck event successfully', async () => {
            jest.spyOn(partnerRegistry, 'get').mockReturnValue(partnerHandler);
            jest.spyOn(partnerHandler, 'notifyStuckRent').mockResolvedValueOnce();

            const req = new FakeExpressRequestBuilder().build();
            req.partnerId = 'partner-123';

            const ctrl = new RentsController({ partnerRegistry, idMappingRepository, logger });

            const result = await ctrl.doorStuckEvent(req as unknown as SearchRentRequest, res);
            expect(result.status).toEqual(NO_CONTENT);
        });

        it('send door stuck event with error', async () => {
            jest.spyOn(partnerRegistry, 'get').mockReturnValue(partnerHandler);
            jest.spyOn(partnerHandler, 'notifyStuckRent').mockRejectedValueOnce(error);

            const formattedError = { message: error.message, stack: error.stack };

            const req = new FakeExpressRequestBuilder().build();
            req.partnerId = 'partner-123';

            const ctrl = new RentsController({ partnerRegistry, idMappingRepository, logger });

            const rawResponse = await ctrl.doorStuckEvent(req as unknown as SearchRentRequest, res);
            const response = simplifyControllerResponse(rawResponse);
            expect(response.status).toEqual(INTERNAL_SERVER_ERROR);

            expect(logger.error).toHaveBeenCalledWith({
                message: 'Failed to notify door stuck',
                error: formattedError,
            });
        });
    });

    describe('doorDirtyEvent', () => {
        it('send door dirty event successfully', async () => {
            jest.spyOn(partnerRegistry, 'get').mockReturnValue(partnerHandler);
            jest.spyOn(partnerHandler, 'notifyDirtyDoor').mockResolvedValueOnce();

            const req = new FakeExpressRequestBuilder().build();
            req.partnerId = 'partner-123';

            const ctrl = new RentsController({ partnerRegistry, idMappingRepository, logger });

            const result = await ctrl.doorDirtyEvent(req as unknown as SearchRentRequest, res);
            expect(result.status).toEqual(NO_CONTENT);
        });

        it('send door dirty event with error', async () => {
            jest.spyOn(partnerRegistry, 'get').mockReturnValue(partnerHandler);
            jest.spyOn(partnerHandler, 'notifyDirtyDoor').mockRejectedValueOnce(error);

            const formattedError = { message: error.message, stack: error.stack };

            const req = new FakeExpressRequestBuilder().build();
            req.partnerId = 'partner-123';

            const ctrl = new RentsController({ partnerRegistry, idMappingRepository, logger });

            const rawResponse = await ctrl.doorDirtyEvent(req as unknown as SearchRentRequest, res);
            const response = simplifyControllerResponse(rawResponse);
            expect(response.status).toEqual(INTERNAL_SERVER_ERROR);

            expect(logger.error).toHaveBeenCalledWith({
                message: 'Failed to notify dirty door',
                error: formattedError,
            });
        });
    });

    describe('violenceEvent', () => {
        it('send ping event successfully', async () => {
            jest.spyOn(partnerRegistry, 'get').mockReturnValue(partnerHandler);
            jest.spyOn(partnerHandler, 'notifyViolence').mockResolvedValueOnce(undefined);

            const req = new FakeExpressRequestBuilder().build();
            req.partnerId = 'partner-123';

            const ctrl = new RentsController({ partnerRegistry, idMappingRepository, logger });

            const result = await ctrl.violenceEvent(req as unknown as SearchRentRequest, res);
            expect(result.status).toEqual(NO_CONTENT);
        });

        it('should log the error approprietly', async () => {
            const errResponse = { data: { message: 'underlying error message' } };
            const enhancedError = {
                message: error.message,
                stack: error.stack,
                response: errResponse,
            };

            jest.spyOn(partnerRegistry, 'get').mockReturnValue(partnerHandler);
            jest.spyOn(partnerHandler, 'notifyViolence').mockRejectedValueOnce(enhancedError);

            const req = new FakeExpressRequestBuilder().build();
            req.partnerId = 'partner-123';

            const ctrl = new RentsController({ partnerRegistry, idMappingRepository, logger });

            const rawResponse = await ctrl.violenceEvent(req as unknown as SearchRentRequest, res);

            const response = simplifyControllerResponse(rawResponse);
            expect(response.status).toEqual(INTERNAL_SERVER_ERROR);

            expect(logger.error).toHaveBeenCalledWith({
                message: 'Failed to notify violence event',
                error: {
                    message: error.message,
                    stack: error.stack,
                    originalResponse: errResponse.data,
                },
            });
        });
    });

    describe('processRentCreatedEvent', () => {
        const bloqitId = 'bloqit-rent-id';
        const partnerId = 'external-partner-rent-id';

        afterEach(() => jest.restoreAllMocks());

        it('should store an id mapping', async () => {
            jest.spyOn(idMappingRepository, 'storeRentIdMapping').mockResolvedValueOnce(undefined);

            const ctrl = new RentsController({ idMappingRepository, partnerRegistry, logger });

            const reqBody = {
                codeName: 'rent.created',
                rent: bloqitId,
                metadata: { externalID: partnerId },
            };

            const req = new FakeExpressRequestBuilder().withBody(reqBody).build();
            req.partnerId = partnerFriendlyId;

            const rawResponse = await ctrl.processRentCreatedEvent(req, res);
            const response = simplifyControllerResponse(rawResponse);

            expect(response.status).toBe(NO_CONTENT);
            expect(idMappingRepository.storeRentIdMapping).toHaveBeenCalledTimes(1);
            expect(idMappingRepository.storeRentIdMapping).toHaveBeenCalledWith({
                bloqitId: reqBody.rent,
                partnerId: reqBody.metadata.externalID,
                partnerFriendlyId,
            });
        });

        it('should handle unexpected errors raised by the id mapping repository', async () => {
            const error = new Error('IdMapping repository - unexpected error');

            jest.spyOn(idMappingRepository, 'storeRentIdMapping').mockRejectedValueOnce(error);

            const ctrl = new RentsController({ idMappingRepository, partnerRegistry, logger });

            const reqBody = {
                codeName: 'rent.created',
                rent: bloqitId,
                metadata: { externalID: partnerId },
            };

            const req = new FakeExpressRequestBuilder().withBody(reqBody).build();
            req.partnerId = partnerFriendlyId;

            const rawResponse = await ctrl.processRentCreatedEvent(req, res);
            const response = simplifyControllerResponse(rawResponse);

            expect(response.status).toBe(INTERNAL_SERVER_ERROR);
            expect(idMappingRepository.storeRentIdMapping).toHaveBeenCalledTimes(1);
            expect(idMappingRepository.storeRentIdMapping).toHaveBeenCalledWith({
                bloqitId: reqBody.rent,
                partnerId: reqBody.metadata.externalID,
                partnerFriendlyId,
            });
            expect(logger.error).toHaveBeenCalledWith({ message: error.message });
        });
    });

    describe('processRentFinishedEvent', () => {
        const bloqitId = 'bloqit-rent-id';

        afterEach(() => jest.restoreAllMocks());

        it('should drop an id mapping when rent is finished', async () => {
            jest.spyOn(partnerRegistry, 'get').mockReturnValue(partnerHandler);
            jest.spyOn(partnerHandler, 'handleRentFinishedEvent').mockResolvedValueOnce();
            jest.spyOn(idMappingRepository, 'drop').mockResolvedValueOnce();

            const ctrl = new RentsController({ idMappingRepository, partnerRegistry, logger });

            const reqBody = {
                rent: bloqitId,
                codeName: 'rent.finished',
                description: 'Rent has transited to the FINISHED state',
                title: 'Rent Finished',
            };

            const req = new FakeExpressRequestBuilder().withBody(reqBody).build();
            req.partnerId = partnerFriendlyId;

            const rawResponse = await ctrl.processRentFinishedEvent(req, res);
            const response = simplifyControllerResponse(rawResponse);

            expect(response.status).toBe(NO_CONTENT);

            expect(partnerHandler.handleRentFinishedEvent).toHaveBeenCalledTimes(1);
            expect(partnerHandler.handleRentFinishedEvent).toHaveBeenCalledWith(reqBody);

            expect(idMappingRepository.drop).toHaveBeenCalledTimes(1);
            expect(idMappingRepository.drop).toHaveBeenCalledWith({
                idType: IdMappingType.RENT,
                matching: { bloqitId: reqBody.rent, partnerFriendlyId },
            });
        });

        it('should handle unexpected errors raised by the id mapping repository', async () => {
            const error = new Error('IdMapping repository - unexpected error');

            jest.spyOn(idMappingRepository, 'drop').mockRejectedValueOnce(error);

            const ctrl = new RentsController({ idMappingRepository, partnerRegistry, logger });

            const reqBody = {
                rent: bloqitId,
                codeName: 'rent.finished',
                description: 'Rent has transited to the FINISHED state',
                title: 'Rent Finished',
            };

            const req = new FakeExpressRequestBuilder().withBody(reqBody).build();
            req.partnerId = partnerFriendlyId;

            const rawResponse = await ctrl.processRentFinishedEvent(req, res);
            const response = simplifyControllerResponse(rawResponse);

            expect(response.status).toBe(INTERNAL_SERVER_ERROR);
            expect(logger.error).toHaveBeenCalledWith({ message: error.message });

            expect(idMappingRepository.drop).toHaveBeenCalledTimes(1);
            expect(idMappingRepository.drop).toHaveBeenCalledWith({
                idType: IdMappingType.RENT,
                matching: { bloqitId: reqBody.rent, partnerFriendlyId },
            });
        });
    });

    describe('associateLabel', () => {
        it('should associate a label to the shipment', async () => {
            jest.spyOn(partnerRegistry, 'get').mockReturnValue(partnerHandler);
            jest.spyOn(partnerHandler, 'associateLabel').mockResolvedValueOnce(true);

            const req = new FakeExpressRequestBuilder()
                .withParams({ id: 'rent-123' })
                .withQuery({
                    partnerBloqId: 'partner-bloq-id-1',
                    courier: 'courier-123',
                    locale: 'en',
                    code: 'code-123',
                })
                .build();

            req.partnerId = 'partner-123';

            const ctrl = new RentsController({ partnerRegistry, idMappingRepository, logger });

            const rawResult = await ctrl.associateLabel(req as unknown as SearchRentRequest, res);
            const result = simplifyControllerResponse(rawResult);

            expect(result.body).toEqual({ result: true, meta: { partnerId: 'partner-123' } });
        });

        it('should handle a error and return a proper response by resolving the call to a specific partner handler', async () => {
            const error = new PartnerResponseError({ code: 'code-123', message: 'message-123' });
            const formattedError = { message: error.message, stack: error.stack };

            jest.spyOn(partnerRegistry, 'get').mockReturnValue(partnerHandler);
            jest.spyOn(partnerHandler, 'associateLabel').mockRejectedValueOnce(error);

            const req = new FakeExpressRequestBuilder()
                .withParams({ id: 'rent-123' })
                .withQuery({
                    partnerBloqId: 'partner-bloq-id-1',
                    courier: 'courier-123',
                    locale: 'en',
                    code: 'code-123',
                })
                .build();

            req.partnerId = 'partner-123';

            const ctrl = new RentsController({ partnerRegistry, idMappingRepository, logger });

            const rawResult = await ctrl.associateLabel(req as unknown as SearchRentRequest, res);
            const result = simplifyControllerResponse(rawResult);

            expect(result.status).toEqual(INTERNAL_SERVER_ERROR);
            expect(result.body).toEqual({
                error: formattedError,
                meta: { partnerId: 'partner-123' },
            });

            expect(logger.error).toHaveBeenCalledWith({
                message: 'Failed to associate label',
                error: formattedError,
            });
        });
    });

    describe('processRentFlowInterruptedEvent', () => {
        const bloqitId = 'bloqit-rent-id';
        const partnerId = 'external-partner-rent-id';

        it('should process a rent flow interrupted event', async () => {
            jest.spyOn(partnerRegistry, 'get').mockReturnValue(partnerHandler);
            jest.spyOn(partnerHandler, 'handleRentFlowInterruptedEvent').mockResolvedValueOnce();

            const ctrl = new RentsController({ idMappingRepository, partnerRegistry, logger });

            const reqBody = {
                codeName: 'rent.flowinterrupted',
                rent: bloqitId,
                metadata: { externalID: partnerId },
            };

            const req = new FakeExpressRequestBuilder().withBody(reqBody).build();
            req.partnerId = partnerFriendlyId;

            const rawResponse = await ctrl.processRentFlowInterruptedEvent(req, res);
            const response = simplifyControllerResponse(rawResponse);

            expect(response.status).toBe(NO_CONTENT);
        });

        it('should handle unexpected errors', async () => {
            jest.spyOn(partnerRegistry, 'get').mockReturnValue(partnerHandler);
            jest.spyOn(partnerHandler, 'handleRentFlowInterruptedEvent').mockRejectedValueOnce(
                error,
            );

            const ctrl = new RentsController({ idMappingRepository, partnerRegistry, logger });

            const reqBody = {
                codeName: 'rent.flowinterrupted',
                rent: bloqitId,
                metadata: { externalID: partnerId },
            };

            const req = new FakeExpressRequestBuilder().withBody(reqBody).build();
            req.partnerId = partnerFriendlyId;

            const rawResponse = await ctrl.processRentFlowInterruptedEvent(req, res);
            const response = simplifyControllerResponse(rawResponse);

            expect(response.status).toBe(INTERNAL_SERVER_ERROR);
        });
    });
});
