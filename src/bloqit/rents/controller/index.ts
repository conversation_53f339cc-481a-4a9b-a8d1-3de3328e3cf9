import type { HTTPRequest, HTTPResponse } from '../../../http/models/extensions/express';
import type { PartnerRegistry } from '../../../core/partner-registry';
import type { RentEvent, OperationType } from '../models/rent';
import { NO_CONTENT, INTERNAL_SERVER_ERROR } from '../../../http/models/codes';
import {
    IdMappingType,
    type IdMappingRepository,
} from '../../../core/identifier-mapping/repository';
import { type Logger } from '../../../core/logger';

export interface SearchRentRequest extends HTTPRequest {
    params: { id: string };
}

export interface RentCreatedEventHTTPRequest extends HTTPRequest {
    body: RentEvent;
}

export class RentsController {
    private readonly partnerRegistry: PartnerRegistry;
    private readonly idMappingRepository: IdMappingRepository;
    private readonly logger: Logger;
    private readonly BATCH_NOTIFICATION_OF_EXPIRED_RENTS_DRY_RUN_ENABLED: boolean;

    constructor(deps: {
        partnerRegistry: PartnerRegistry;
        idMappingRepository: IdMappingRepository;
        logger: Logger;
        batchNotificationsOfExpiredRentsDryRunEnabled?: boolean;
    }) {
        this.partnerRegistry = deps.partnerRegistry;
        this.idMappingRepository = deps.idMappingRepository;
        this.logger = deps.logger;
        this.BATCH_NOTIFICATION_OF_EXPIRED_RENTS_DRY_RUN_ENABLED =
            deps.batchNotificationsOfExpiredRentsDryRunEnabled ?? false;

        this.searchRent = this.searchRent.bind(this);
        this.canBePickedUp = this.canBePickedUp.bind(this);
        this.dropOffConfirmationEvent = this.dropOffConfirmationEvent.bind(this);
        this.dropOffCancellationEvent = this.dropOffCancellationEvent.bind(this);
        this.collectItemEvent = this.collectItemEvent.bind(this);
        this.rentExpiredEvent = this.rentExpiredEvent.bind(this);
        this.doorStuckEvent = this.doorStuckEvent.bind(this);
        this.violenceEvent = this.violenceEvent.bind(this);
        this.processRentCreatedEvent = this.processRentCreatedEvent.bind(this);
        this.processRentStartingEvent = this.processRentStartingEvent.bind(this);
        this.processRentFinishedEvent = this.processRentFinishedEvent.bind(this);
        this.processRentFlowInterruptedEvent = this.processRentFlowInterruptedEvent.bind(this);
        this.associateLabel = this.associateLabel.bind(this);
        this.batchNotifyExpiredRents = this.batchNotifyExpiredRents.bind(this);
        this.doorDirtyEvent = this.doorDirtyEvent.bind(this);
    }

    async searchRent(req: SearchRentRequest, res: HTTPResponse): Promise<HTTPResponse> {
        const partnerId = req.partnerId;
        if (partnerId === undefined) throw new Error('Missing partnerId');

        const partnerHandler = this.partnerRegistry.get(partnerId);

        try {
            const rents = await partnerHandler?.getRentById({
                partnerRentId: req.params.id,
                bloqId: req.query.bloqId?.toString() ?? '',
                locale: req.query.locale?.toString() ?? '',
                courier: req.query.courier?.toString(),
                nodeId: req.query.externalId?.toString() ?? '',
                country: req.query.country?.toString() ?? '',
            });

            return res.json({ rents, meta: { partnerId } });
        } catch (error) {
            const { message, code, stack } = error as Error & { code?: string };
            this.logger.error({ message: `Failed to search rent: ${message}`, stack });
            return res
                .status(INTERNAL_SERVER_ERROR)
                .json({ error: { message, code, stack }, meta: { partnerId } });
        }
    }

    async canBePickedUp(req: SearchRentRequest, res: HTTPResponse): Promise<HTTPResponse> {
        const partnerId = req.partnerId;
        if (partnerId === undefined) throw new Error('Missing partnerId');

        const partnerHandler = this.partnerRegistry.get(partnerId);

        try {
            const isPickupAllowed: boolean =
                (await partnerHandler?.canBePickedUp({
                    partnerRentId: req.params.id,
                    bloqId: req.query.bloqId?.toString() ?? '',
                    operationType: req.query.operationType as OperationType,
                })) ?? false;

            return res.json({ isPickupAllowed, meta: { partnerId } });
        } catch (ex) {
            const error = this.parseError(ex);
            this.logger.error({ message: 'Failed to check pick up availability', error });
            return res.status(INTERNAL_SERVER_ERROR).json({ error, meta: { partnerId } });
        }
    }

    async dropOffConfirmationEvent(req: HTTPRequest, res: HTTPResponse): Promise<HTTPResponse> {
        const partnerId = req.partnerId;
        if (partnerId === undefined) throw new Error('Missing partnerId');

        const partnerHandler = this.partnerRegistry.get(partnerId);
        try {
            await partnerHandler?.notifyDropOffConfirmation(req.body as RentEvent);
            return res.status(NO_CONTENT).send();
        } catch (ex) {
            const error = this.parseError(ex);
            this.logger.error({ message: 'Failed to notify drop off confirmation', error });
            return res.status(INTERNAL_SERVER_ERROR).send();
        }
    }

    async dropOffCancellationEvent(req: HTTPRequest, res: HTTPResponse): Promise<HTTPResponse> {
        const partnerId = req.partnerId;
        if (partnerId === undefined) throw new Error('Missing partnerId');

        const partnerHandler = this.partnerRegistry.get(partnerId);
        try {
            await partnerHandler?.notifyDropOffCancellation(req.body as RentEvent);
            return res.status(NO_CONTENT).send();
        } catch (ex) {
            const error = this.parseError(ex);
            this.logger.error({ message: 'Failed to notify drop off cancellation', error });
            return res.status(INTERNAL_SERVER_ERROR).send();
        }
    }

    async collectItemEvent(req: HTTPRequest, res: HTTPResponse): Promise<HTTPResponse> {
        const partnerId = req.partnerId;
        if (partnerId === undefined) throw new Error('Missing partnerId');

        const partnerHandler = this.partnerRegistry.get(partnerId);
        try {
            await partnerHandler?.notifyCollectItem(req.body as RentEvent);
            return res.status(NO_CONTENT).send();
        } catch (ex) {
            const { message, stack, originalResponse } = this.parseError(ex);
            this.logger.error({
                message: 'Failed to notify collect item',
                originalResponse,
                errMsg: message,
                stack,
            });
            return res.status(INTERNAL_SERVER_ERROR).send();
        }
    }

    async rentExpiredEvent(req: HTTPRequest, res: HTTPResponse): Promise<HTTPResponse> {
        const partnerId = req.partnerId;
        if (partnerId === undefined) throw new Error('Missing partnerId');

        const partnerHandler = this.partnerRegistry.get(partnerId);
        try {
            await partnerHandler?.notifyExpiredRent(req.body as RentEvent);
            return res.status(NO_CONTENT).send();
        } catch (ex) {
            const error = this.parseError(ex);
            this.logger.error({ message: 'Failed to notify expired rent', error });
            return res.status(INTERNAL_SERVER_ERROR).send();
        }
    }

    async batchNotifyExpiredRents(req: HTTPRequest, res: HTTPResponse): Promise<HTTPResponse> {
        const partnerId = req.partnerId;
        if (partnerId === undefined) throw new Error('Missing partnerId');

        const partnerHandler = this.partnerRegistry.get(partnerId);
        try {
            const batchSize = req.body.batchSize;

            if (this.BATCH_NOTIFICATION_OF_EXPIRED_RENTS_DRY_RUN_ENABLED) {
                this.logger.info({
                    message:
                        'Received request to send a batch notification related to expired rents',
                    payload: req.body,
                });
            } else {
                await partnerHandler?.notifyExpiredRents({ batchSize });
            }

            return res.status(NO_CONTENT).send();
        } catch (ex) {
            const error = this.parseError(ex);
            const data = { message: 'Failed to notify expired rent', error };
            this.logger.error(data);
            return res.status(INTERNAL_SERVER_ERROR).send();
        }
    }

    async doorStuckEvent(req: HTTPRequest, res: HTTPResponse): Promise<HTTPResponse> {
        const partnerId = req.partnerId;
        if (partnerId === undefined) throw new Error('Missing partnerId');

        const partnerHandler = this.partnerRegistry.get(partnerId);
        try {
            await partnerHandler?.notifyStuckRent(req.body as RentEvent);
            return res.status(NO_CONTENT).send();
        } catch (ex) {
            const error = this.parseError(ex);
            this.logger.error({ message: 'Failed to notify door stuck', error });
            return res.status(INTERNAL_SERVER_ERROR).send();
        }
    }

    async doorDirtyEvent(req: HTTPRequest, res: HTTPResponse): Promise<HTTPResponse> {
        const partnerId = req.partnerId;
        if (partnerId === undefined) throw new Error('Missing partnerId');

        const partnerHandler = this.partnerRegistry.get(partnerId);
        try {
            await partnerHandler?.notifyDirtyDoor(req.body as RentEvent);
            return res.status(NO_CONTENT).send();
        } catch (ex) {
            const error = this.parseError(ex);
            this.logger.error({ message: 'Failed to notify dirty door', error });
            return res.status(INTERNAL_SERVER_ERROR).send();
        }
    }

    async violenceEvent(req: HTTPRequest, res: HTTPResponse): Promise<HTTPResponse> {
        const partnerId = req.partnerId;
        if (partnerId === undefined) throw new Error('Missing partnerId');

        const partnerHandler = this.partnerRegistry.get(partnerId);
        try {
            await partnerHandler?.notifyViolence(req.body as RentEvent);
            return res.status(NO_CONTENT).send();
        } catch (ex) {
            const error = this.parseError(ex);
            this.logger.error({ message: 'Failed to notify violence event', error });
            return res.status(INTERNAL_SERVER_ERROR).send();
        }
    }

    async processRentCreatedEvent(
        req: RentCreatedEventHTTPRequest,
        res: HTTPResponse,
    ): Promise<HTTPResponse> {
        try {
            const { body: data, partnerId } = req;

            if (partnerId === undefined) throw new Error('Missing partnerId');

            await this.idMappingRepository.storeRentIdMapping({
                bloqitId: data.rent,
                partnerId: data.metadata.externalID,
                partnerFriendlyId: partnerId,
            });

            return res.status(NO_CONTENT).end();
        } catch (ex) {
            const err = ex as Error;
            this.logger.error({ message: err.message });
            return res.status(INTERNAL_SERVER_ERROR).end();
        }
    }

    async processRentStartingEvent(
        req: RentCreatedEventHTTPRequest,
        res: HTTPResponse,
    ): Promise<HTTPResponse> {
        try {
            const { body: data, partnerId } = req;

            if (partnerId === undefined) throw new Error('Missing partnerId');

            const partnerHandler = this.partnerRegistry.get(partnerId);
            await partnerHandler?.handleRentStartingEvent(data);

            return res.status(NO_CONTENT).end();
        } catch (ex) {
            const err = ex as Error;
            this.logger.error({ message: err.message });
            return res.status(INTERNAL_SERVER_ERROR).end();
        }
    }

    async processRentFinishedEvent(
        req: RentCreatedEventHTTPRequest,
        res: HTTPResponse,
    ): Promise<HTTPResponse> {
        try {
            const { body: data, partnerId } = req;

            if (partnerId === undefined) throw new Error('Missing partnerId');

            await this.idMappingRepository.drop({
                idType: IdMappingType.RENT,
                matching: { bloqitId: data.rent, partnerFriendlyId: partnerId },
            });

            const partnerHandler = this.partnerRegistry.get(partnerId);
            await partnerHandler?.handleRentFinishedEvent(data);

            return res.status(NO_CONTENT).end();
        } catch (ex) {
            const err = ex as Error;
            this.logger.error({ message: err.message });
            return res.status(INTERNAL_SERVER_ERROR).end();
        }
    }

    async processRentFlowInterruptedEvent(
        req: RentCreatedEventHTTPRequest,
        res: HTTPResponse,
    ): Promise<HTTPResponse> {
        try {
            const { body: data, partnerId } = req;

            if (partnerId === undefined) throw new Error('Missing partnerId');

            const partnerHandler = this.partnerRegistry.get(partnerId);
            await partnerHandler?.handleRentFlowInterruptedEvent(data);

            return res.status(NO_CONTENT).end();
        } catch (ex) {
            const err = ex as Error;
            this.logger.error({ message: err.message });
            return res.status(INTERNAL_SERVER_ERROR).end();
        }
    }

    async associateLabel(req: HTTPRequest, res: HTTPResponse): Promise<HTTPResponse> {
        const partnerId = req.partnerId;
        if (partnerId === undefined) throw new Error('Missing partnerId');

        const partnerHandler = this.partnerRegistry.get(partnerId);
        try {
            const result = await partnerHandler?.associateLabel({
                partnerRentId: req.params.id,
                bloqId: req.query.bloqId?.toString() ?? '',
                operationType: req.query.operationType as OperationType,
                courier: req.query.courier?.toString() ?? '',
                locale: req.query.locale?.toString() ?? '',
                code: req.body.code?.toString() ?? '',
                bloqExternalId: req.body.bloqExternalID?.toString() ?? '',
                country: req.body.country?.toString() ?? '',
            });
            return res.json({ result, meta: { partnerId } });
        } catch (ex) {
            const error = this.parseError(ex);
            this.logger.error({ message: 'Failed to associate label', error });
            return res.status(INTERNAL_SERVER_ERROR).json({ error, meta: { partnerId } });
        }
    }

    private parseError(ex: any): { message: string; stack?: string; originalResponse?: any } {
        const { message, stack, response } = ex;
        const error = { message, stack, originalResponse: response?.data };
        return error;
    }
}
