/* eslint-disable @typescript-eslint/unbound-method */
import type { RequestHandler, Router } from 'express';
import { RentsController } from '../rents/controller';
import { CouriersController } from '../couriers/controller';
import type { PartnerRegistry } from '../../core/partner-registry';
import { BloqsController } from '../bloqs/controller';
import { LockersController } from '../lockers/controller';
import { type IdMappingRepository } from '../../core/identifier-mapping/repository';
import { type Logger } from '../../core/logger';

export const setup = (deps: {
    router: Router;
    partnerRegistry: PartnerRegistry;
    idMappingRepository: IdMappingRepository;
    logger: Logger;
}): Router => {
    const { router, partnerRegistry, idMappingRepository, logger } = deps;

    const rentsCtrl = new RentsController({
        partnerRegistry,
        idMappingRepository,
        logger,
        batchNotificationsOfExpiredRentsDryRunEnabled: false,
    });

    router.route('/rents/:id').get(rentsCtrl.searchRent as unknown as RequestHandler);
    router
        .route('/rents/:id/pickup/check')
        .get(rentsCtrl.canBePickedUp as unknown as RequestHandler);
    router.route('/rents/:id/label').post(rentsCtrl.associateLabel as unknown as RequestHandler);
    router
        .route('/rents/events/drop-off-confirmation')
        .post(rentsCtrl.dropOffConfirmationEvent as unknown as RequestHandler);
    router
        .route('/rents/events/drop-off-cancellation')
        .post(rentsCtrl.dropOffCancellationEvent as unknown as RequestHandler);
    router
        .route('/rents/events/collect-item')
        .post(rentsCtrl.collectItemEvent as unknown as RequestHandler);
    router
        .route('/rents/events/expired')
        .post(rentsCtrl.rentExpiredEvent as unknown as RequestHandler);
    router
        .route('/rents/events/expired/batch')
        .post(rentsCtrl.batchNotifyExpiredRents as unknown as RequestHandler);
    router
        .route('/rents/events/door-stuck')
        .post(rentsCtrl.doorStuckEvent as unknown as RequestHandler);
    router
        .route('/rents/events/door-dirty')
        .post(rentsCtrl.doorDirtyEvent as unknown as RequestHandler);
    router
        .route('/rents/events/violence')
        .post(rentsCtrl.violenceEvent as unknown as RequestHandler);
    router
        .route('/rents/events/created')
        .post(rentsCtrl.processRentCreatedEvent as unknown as RequestHandler);
    router
        .route('/rents/events/starting')
        .post(rentsCtrl.processRentStartingEvent as unknown as RequestHandler);
    router
        .route('/rents/events/finished')
        .post(rentsCtrl.processRentFinishedEvent as unknown as RequestHandler);
    router
        .route('/rents/events/flow-interrupted')
        .post(rentsCtrl.processRentFlowInterruptedEvent as unknown as RequestHandler);

    const bloqsCtrl = new BloqsController({ partnerRegistry, idMappingRepository, logger });
    router
        .route('/bloqs/change-state')
        .post(bloqsCtrl.changeBloqState as unknown as RequestHandler);
    router.route('/bloqs/events/ping').post(bloqsCtrl.pingEvent as unknown as RequestHandler);
    router
        .route('/bloqs/events/created')
        .post(bloqsCtrl.processBloqCreatedEvent as unknown as RequestHandler);
    router
        .route('/bloqs/events/updated')
        .post(bloqsCtrl.processBloqUpdatedEvent as unknown as RequestHandler);
    router
        .route('/bloqs/events/courier-logout')
        .post(bloqsCtrl.proccessCourierLogoutEvent as unknown as RequestHandler);

    router
        .route('/bloqs/:bloqId/couriers-access-codes')
        .get(bloqsCtrl.getCouriersAccessCodes as unknown as RequestHandler);

    const lockersCtrl = new LockersController({ partnerRegistry, logger });
    router
        .route('/lockers/change-state')
        .post(lockersCtrl.changeLockerState as unknown as RequestHandler);

    router
        .route('/lockers/maintenance-status-update')
        .post(lockersCtrl.maintenanceStatusUpdate as unknown as RequestHandler);

    const couriersCtrl = new CouriersController({ partnerRegistry });
    router
        .route('/couriers/authenticate')
        .post(couriersCtrl.authenticateCourier as unknown as RequestHandler);

    return router;
};
