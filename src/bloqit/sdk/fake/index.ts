import { type BloqitClient } from '..';
import { type RentDelivery, type Rent, UpdateRentResponse } from '../../rents/models/rent';
import { type LockerObjectResponse } from '../../lockers/models';
import type { BloqLocker, BloqObjectResponse } from '../../bloqs/models';

export class FakeBloqitClient implements BloqitClient {
    async getRentById(_props: {
        id: string;
        ctx: { partnerId: string };
    }): Promise<Rent | undefined> {
        throw new Error('Not implemented. Please override it for your test purposes');
    }

    async listAvailableDoorsFor(_bloqId: string): Promise<{ count: number; doors: string[] }> {
        throw new Error('Not implemented. Please override it for your test purposes');
    }

    async activateBloq(_props: { bloqId: string }): Promise<void> {
        throw new Error('Not implemented. Please override it for your test purposes');
    }

    async deactivateBloq(_props: { bloqId: string }): Promise<void> {
        throw new Error('Not implemented. Please override it for your test purposes');
    }

    async getOccupancy(_props: { bloqId: string }): Promise<LockerObjectResponse> {
        throw new Error('Not implemented. Please override it for your test purposes');
    }

    async updateBloqMetadata(_props: {
        bloqId: string;
        metadata: Record<string, string>;
    }): Promise<void> {
        throw new Error('Not implemented. Please override it for your test purposes');
    }

    async retrieveBloqById(_props: { bloqId: string }): Promise<BloqObjectResponse> {
        throw new Error('Not implemented. Please override it for your test purposes');
    }

    async retrieveBloqByExternalId(_props: { externalId: string }): Promise<BloqObjectResponse> {
        throw new Error('Not implemented. Please override it for your test purposes');
    }

    async getRent(_props: { rentId: string }): Promise<any> {
        throw new Error('Not implemented. Please override it for your test purposes');
    }

    async openDoors(_props: { bloqId: string; lockers: string[] }): Promise<any> {
        throw new Error('Not implemented. Please override it for your test purposes');
    }

    async createRent(_props: {
        rent: RentDelivery;
        lockerId?: string;
        bloqId?: string;
    }): Promise<any> {
        throw new Error('Not implemented. Please override it for your test purposes');
    }

    async updateRent(_props: { rentId: string; rent: Partial<Rent> }): Promise<UpdateRentResponse> {
        throw new Error('Not implemented. Please override it for your test purposes');
    }

    async collectItem(_props: { rentId: string }): Promise<any> {
        throw new Error('Not implemented. Please override it for your test purposes');
    }

    async retrieveRentByExternalId(_props: { externalId: string }): Promise<any> {
        throw new Error('Not implemented. Please override it for your test purposes');
    }

    async cancelRent(_props: { rentId: string }): Promise<any> {
        throw new Error('Not implemented. Please override it for your test purposes');
    }

    async finishRent(_props: { rentId: string }): Promise<any> {
        throw new Error('Not implemented. Please override it for your test purposes');
    }

    async reportLockerProblem(_props: {
        bloqId: string;
        lockerId: string;
        reason: string;
        state: string;
    }): Promise<BloqLocker> {
        throw new Error('Not implemented. Please override it for your test purposes');
    }
}
