import type { BloqObjectResponse, BloqLocker } from '../bloqs/models';
import type { OpenLockersResponse, LockerObjectResponse } from '../lockers/models';
import {
    CreateRentResponse,
    Rent,
    RentObjectResponse,
    UpdateRentResponse,
} from '../rents/models/rent';

export interface BloqitClient {
    activateBloq: (props: { bloqId: string }) => Promise<void>;

    deactivateBloq: (props: { bloqId: string }) => Promise<void>;

    getOccupancy: (props: { bloqId: string }) => Promise<LockerObjectResponse>;

    updateBloqMetadata: (props: {
        bloqId: string;
        metadata: Record<string, string>;
    }) => Promise<void>;

    retrieveBloqById: (props: { bloqId: string }) => Promise<BloqObjectResponse>;

    retrieveBloqByExternalId: (props: { externalId: string }) => Promise<BloqObjectResponse>;

    getRent: (props: { rentId: string }) => Promise<RentObjectResponse>;

    openDoors: (props: { bloqId: string; lockers: string[] }) => Promise<OpenLockersResponse>;

    createRent: (props: {
        rent: Rent;
        lockerId?: string;
        bloqId?: string;
    }) => Promise<CreateRentResponse>;

    updateRent: (props: { rentId: string; rent: Partial<Rent> }) => Promise<UpdateRentResponse>;

    collectItem: (props: { rentId: string }) => Promise<RentObjectResponse>;

    retrieveRentByExternalId: (props: { externalId: string }) => Promise<RentObjectResponse>;

    cancelRent: (props: { rentId: string }) => Promise<RentObjectResponse>;

    finishRent: (props: { rentId: string }) => Promise<RentObjectResponse>;

    reportLockerProblem: (props: {
        bloqId: string;
        lockerId: string;
        reason: string;
        state: string;
    }) => Promise<BloqLocker>;
}
