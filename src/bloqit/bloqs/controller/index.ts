import type { HTTPRequest, HTTPResponse } from '../../../http/models/extensions/express';
import type { PartnerRegistry } from '../../../core/partner-registry';
import type { BloqChangeState, BloqEvent } from '../models';
import { OK, NO_CONTENT, INTERNAL_SERVER_ERROR } from '../../../http/models/codes';
import { type IdMappingRepository } from '../../../core/identifier-mapping/repository';
import { type Logger } from '../../../core/logger';

export interface BloqChangeStateRequest extends HTTPRequest {
    body: BloqChangeState;
}

export interface ProcessBloqCreatedEventRequest extends HTTPRequest {
    body: { bloq: string; metadata: { bloqExternalID: string } };
}

export class BloqsController {
    private readonly partnerRegistry: PartnerRegistry;
    private readonly idMappingRepository: IdMappingRepository;
    private readonly logger: Logger;

    constructor(deps: {
        partnerRegistry: PartnerRegistry;
        idMappingRepository: IdMappingRepository;
        logger: Logger;
    }) {
        this.partnerRegistry = deps.partnerRegistry;
        this.idMappingRepository = deps.idMappingRepository;
        this.logger = deps.logger;

        this.changeBloqState = this.changeBloqState.bind(this);
        this.pingEvent = this.pingEvent.bind(this);
        this.processBloqCreatedEvent = this.processBloqCreatedEvent.bind(this);
        this.processBloqUpdatedEvent = this.processBloqUpdatedEvent.bind(this);
        this.proccessCourierLogoutEvent = this.proccessCourierLogoutEvent.bind(this);
        this.getCouriersAccessCodes = this.getCouriersAccessCodes.bind(this);
    }

    async changeBloqState(req: BloqChangeStateRequest, res: HTTPResponse): Promise<HTTPResponse> {
        try {
            const partnerId = req.partnerId;
            if (partnerId === undefined) throw new Error('Missing partnerId');

            const partnerHandler = this.partnerRegistry.get(partnerId);
            const response = await partnerHandler?.setConfig(req.body);
            return res.status(response?.success === true ? OK : INTERNAL_SERVER_ERROR).send();
        } catch (ex) {
            const err = ex as Error;
            this.logger.error({ message: err.message });
            return res.status(INTERNAL_SERVER_ERROR).end();
        }
    }

    async pingEvent(req: HTTPRequest, res: HTTPResponse): Promise<HTTPResponse> {
        const partnerId = req.partnerId;
        if (partnerId === undefined) throw new Error('Missing partnerId');

        const partnerHandler = this.partnerRegistry.get(partnerId);
        try {
            await partnerHandler?.notifyPing(req.body as BloqChangeState);
            return res.status(200).send();
        } catch (e) {
            return res.status(400).send();
        }
    }

    async processBloqCreatedEvent(
        req: ProcessBloqCreatedEventRequest,
        res: HTTPResponse,
    ): Promise<HTTPResponse> {
        try {
            const { body: data, partnerId } = req;

            if (partnerId === undefined) throw new Error('Missing partnerId');

            await this.idMappingRepository.storeBloqIdMapping({
                sharedId: data.metadata.bloqExternalID,
                bloqitId: data.bloq,
                partnerFriendlyId: partnerId,
            });

            if (partnerId === process.env.INPOST_PARTNER_ID) {
                const partnerHandler = this.partnerRegistry.get(partnerId);
                try {
                    await partnerHandler?.handleBloqCreatedEvent(req.body as BloqEvent);
                } catch (error) {
                    return res.status(400).send();
                }
            }

            return res.status(NO_CONTENT).end();
        } catch (ex) {
            const err = ex as Error;
            this.logger.error({ message: err.message });
            return res.status(INTERNAL_SERVER_ERROR).end();
        }
    }

    async processBloqUpdatedEvent(
        req: ProcessBloqCreatedEventRequest,
        res: HTTPResponse,
    ): Promise<HTTPResponse> {
        try {
            const { body: data, partnerId } = req;

            if (partnerId === undefined) throw new Error('Missing partnerId');

            const partnerHandler = this.partnerRegistry.get(partnerId);
            await partnerHandler?.handleBloqUpdatedEvent(req.body as BloqEvent);

            return res.status(NO_CONTENT).end();
        } catch (ex) {
            const err = ex as Error;
            this.logger.error({ message: err.message });
            return res.status(INTERNAL_SERVER_ERROR).end();
        }
    }

    async proccessCourierLogoutEvent(req: HTTPRequest, res: HTTPResponse): Promise<HTTPResponse> {
        try {
            const partnerId = req.partnerId;
            if (partnerId === undefined) throw new Error('Missing partnerId');

            const partnerHandler = this.partnerRegistry.get(partnerId);
            await partnerHandler?.handleCourierLogout(req.body as BloqEvent);
            return res.status(NO_CONTENT).end();
        } catch (ex) {
            const err = ex as Error;
            this.logger.error({
                message: 'Failed to process courier logout',
                error: { message: err.message, stack: err.stack },
            });

            return res.status(INTERNAL_SERVER_ERROR).end();
        }
    }

    async getCouriersAccessCodes(req: HTTPRequest, res: HTTPResponse): Promise<HTTPResponse> {
        const partnerId = req.partnerId;
        if (partnerId === undefined) throw new Error('Missing partnerId');

        try {
            const { bloqId } = req.params;
            const partnerHandler = this.partnerRegistry.get(partnerId);
            const response = await partnerHandler?.getCouriersAccessCodes({
                bloqId,
                bloqExternalId: req.query.externalId?.toString(),
                carrier: req.query.carrier?.toString(),
                country: req.query.country?.toString(),
            });
            return res.status(OK).json(response);
        } catch (ex) {
            const err = ex as Error;
            this.logger.error({ errorMessage: err.message, errorStack: err.stack });
            return res.status(INTERNAL_SERVER_ERROR).end();
        }
    }
}
