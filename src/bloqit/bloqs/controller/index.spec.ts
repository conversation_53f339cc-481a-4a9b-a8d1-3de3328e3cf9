import { BloqsController } from '.';
import { FakePartnerRegistry } from '../../../core/partner-registry/fake';
import FakeExpressRequestBuilder from '../../../../test/mocks/http/express/request';
import FakeExpressResponseBuilder from '../../../../test/mocks/http/express/response';
import { FakeLogger } from '../../../core/logger/fake';
import { FakeIdMappingRepository } from '../../../core/identifier-mapping/repository/fake';
import { NO_CONTENT, INTERNAL_SERVER_ERROR } from '../../../http/models/codes';
import { simplifyControllerResponse } from '../../../../test/utils';
import { FakePartnerHandler } from '../../../core/partner-registry/fake/handler';
import { type HTTPRequest, type HTTPResponse } from '../../../http/models/extensions/express';

describe('BloqsController', () => {
    const partnerFriendlyId = 'dhl-cz';
    const idMappingRepository = new FakeIdMappingRepository();
    const logger = new FakeLogger();
    const partnerRegistry = new FakePartnerRegistry();
    const partnerHandler = new FakePartnerHandler();

    beforeEach(() => jest.spyOn(logger, 'error').mockReturnValue(undefined));
    afterEach(() => jest.restoreAllMocks());

    describe('pingEvent', () => {
        it('send ping event successfully', async () => {
            jest.spyOn(partnerRegistry, 'get').mockReturnValue(partnerHandler);
            jest.spyOn(partnerHandler, 'notifyPing').mockResolvedValueOnce();

            const res = new FakeExpressResponseBuilder().build();
            const req = new FakeExpressRequestBuilder().build();

            req.partnerId = 'partner-123';

            const ctrl = new BloqsController({ partnerRegistry, idMappingRepository, logger });

            const result = await ctrl.pingEvent(req, res);
            expect(result.status).toEqual(200);
        });

        it('send ping event with error', async () => {
            jest.spyOn(partnerRegistry, 'get').mockReturnValue(partnerHandler);
            jest.spyOn(partnerHandler, 'notifyPing').mockRejectedValueOnce(new Error());

            const res = new FakeExpressResponseBuilder().build();
            const req = new FakeExpressRequestBuilder().build();

            req.partnerId = 'partner-123';

            const ctrl = new BloqsController({ partnerRegistry, idMappingRepository, logger });

            const result = await ctrl.pingEvent(req, res);
            expect(result.status).toEqual(400);
        });
    });

    describe('processBloqCreatedEvent', () => {
        const bloqitId = 'bloqit-bloq-id';
        const sharedId = 'bloqit-shared-bloq-id';
        const partnerId = 'bloqit-partner-bloq-id';

        it('should store a bloq id mapping', async () => {
            jest.spyOn(idMappingRepository, 'storeBloqIdMapping').mockResolvedValueOnce(undefined);

            const ctrl = new BloqsController({ idMappingRepository, partnerRegistry, logger });

            const reqBody = { bloq: bloqitId, metadata: { bloqExternalID: partnerId } };

            const res = new FakeExpressResponseBuilder().build();
            const req = new FakeExpressRequestBuilder().withBody(reqBody).build();
            req.partnerId = partnerFriendlyId;

            jest.spyOn(partnerRegistry, 'get').mockReturnValue(partnerHandler);
            jest.spyOn(partnerHandler, 'handleBloqCreatedEvent').mockResolvedValueOnce();

            const rawResponse = await ctrl.processBloqCreatedEvent(req, res);
            const response = simplifyControllerResponse(rawResponse);

            expect(idMappingRepository.storeBloqIdMapping).toHaveBeenCalledTimes(1);
            expect(idMappingRepository.storeBloqIdMapping).toHaveBeenCalledWith({
                bloqitId: reqBody.bloq,
                sharedId: reqBody.metadata.bloqExternalID,
                partnerFriendlyId: req.partnerId,
            });
            expect(response.status).toBe(NO_CONTENT);
        });

        it('should handle unexpected errors raised by the id mapping repository', async () => {
            const error = new Error('IdMapping repository - unexpected error');

            jest.spyOn(idMappingRepository, 'storeBloqIdMapping').mockRejectedValueOnce(error);

            const ctrl = new BloqsController({ idMappingRepository, partnerRegistry, logger });

            const reqBody = { bloq: bloqitId, metadata: { bloqExternalID: sharedId } };
            const res = new FakeExpressResponseBuilder().build();
            const req = new FakeExpressRequestBuilder().withBody(reqBody).build();
            req.partnerId = partnerFriendlyId;

            jest.spyOn(partnerRegistry, 'get').mockReturnValue(partnerHandler);
            jest.spyOn(partnerHandler, 'handleBloqCreatedEvent').mockResolvedValueOnce();

            const rawResponse = await ctrl.processBloqCreatedEvent(req, res);
            const response = simplifyControllerResponse(rawResponse);

            expect(response.status).toBe(INTERNAL_SERVER_ERROR);

            expect(idMappingRepository.storeBloqIdMapping).toHaveBeenCalledTimes(1);
            expect(idMappingRepository.storeBloqIdMapping).toHaveBeenCalledWith({
                bloqitId: reqBody.bloq,
                sharedId: reqBody.metadata.bloqExternalID,
                partnerFriendlyId: req.partnerId,
            });

            expect(logger.error).toHaveBeenCalledWith({ message: error.message });
        });
    });

    describe('processBloqUpdatedEvent', () => {
        const bloqitId = 'bloqit-bloq-id';
        const sharedId = 'bloqit-shared-bloq-id';
        const partnerId = 'bloqit-partner-bloq-id';

        it('should forward a bloq update to a partner handler', async () => {
            jest.spyOn(idMappingRepository, 'storeBloqIdMapping').mockResolvedValueOnce(undefined);

            const ctrl = new BloqsController({ idMappingRepository, partnerRegistry, logger });

            const reqBody = { bloq: bloqitId, metadata: { bloqExternalID: partnerId } };

            const res = new FakeExpressResponseBuilder().build();
            const req = new FakeExpressRequestBuilder().withBody(reqBody).build();
            req.partnerId = partnerFriendlyId;

            jest.spyOn(partnerRegistry, 'get').mockReturnValue(partnerHandler);
            jest.spyOn(partnerHandler, 'handleBloqUpdatedEvent').mockResolvedValueOnce();

            const rawResponse = await ctrl.processBloqUpdatedEvent(req, res);
            const response = simplifyControllerResponse(rawResponse);

            expect(partnerHandler.handleBloqUpdatedEvent).toHaveBeenCalledTimes(1);
            expect(response.status).toBe(NO_CONTENT);
        });
    });

    describe('proccessCourierLogoutEvent', () => {
        const ctrl = new BloqsController({ partnerRegistry, idMappingRepository, logger });

        let req: HTTPRequest, res: HTTPResponse;
        beforeEach(() => {
            res = new FakeExpressResponseBuilder().build();
            req = new FakeExpressRequestBuilder().build();
            req.partnerId = 'partner-123';
        });

        it('should forward a courier logout call to a specific handler', async () => {
            jest.spyOn(partnerRegistry, 'get').mockReturnValue(partnerHandler);
            jest.spyOn(partnerHandler, 'handleCourierLogout').mockResolvedValueOnce();

            const result = await ctrl.proccessCourierLogoutEvent(req, res);

            expect(result.status).toEqual(204);
        });

        it('should handle unexpected errors', async () => {
            const err = new Error();
            jest.spyOn(partnerRegistry, 'get').mockReturnValue(partnerHandler);
            jest.spyOn(partnerHandler, 'handleCourierLogout').mockRejectedValueOnce(err);

            const result = await ctrl.proccessCourierLogoutEvent(req, res);

            expect(result.status).toEqual(500);
            expect(logger.error).toHaveBeenCalledTimes(1);
            expect(logger.error).toHaveBeenCalledWith({
                message: 'Failed to process courier logout',
                error: { message: err.message, stack: err.stack },
            });
        });
    });
});
