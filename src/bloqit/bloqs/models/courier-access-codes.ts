export interface CourierAccess {
    carrier?: string;
    code?: string;
    username?: string;
    password?: string;
    permissions?: BloqCourierPermissions[];
    hashAlgorithm?: HashAlgorithm;
}

export enum BloqCourierPermissions {
    EMERGENCY_ACCESS = 'EMERGENCY_ACCESS',
    INSPECT_BLOQ = 'INSPECT_BLOQ',
    CLEAN_BLOQ = 'CLEAN_BLOQ',
    MAINTENANCE_ACCESS = 'MAINTENANCE_ACCESS',
    DROP_OFF = 'DROP_OFF',
    PICK_UP = 'PICK_UP',
    ADMIN_ACCESS = 'ADMIN_ACCESS',
}

export interface CourierAccessCodesResponse {
    couriers: CourierAccess[];
}

export enum HashAlgorithm {
    MD5 = 'MD5',
    SHA_256 = 'SHA-256',
}
