import { LockerType, LockerUsageType } from '../../lockers/models';
import type { ActionInitiatedBy } from '../../rents/models/rent';

export enum CodeName {
    activate = 'bloq.bloq_active',
    deactivate = 'bloq.bloq_inactive',
}

export interface Metadata {
    bloqExternalID: string;
    lockerExternalID: undefined;
    externalID: string;
}

export interface BloqChangeState {
    _id: string;
    bloq: string;
    codeName: CodeName;
    metadata: Metadata;
    timestamp: Date;
}

export interface BloqChangeStateResponse {
    success: boolean;
}

export interface BloqEvent {
    bloq: string;
    timestamp: Date;
    metadata: { bloqExternalID: string };
    actionInitiatedBy: ActionInitiatedBy;
}

export interface BloqLocker {
    _id: string;
    rent: string | null;
    isOpen: boolean;
    lockerTitle: string;
    type: LockerType;
    active: boolean;
    createdAt: string;
    updatedAt: string;
    maintenanceStatus?: MaintenanceStatus;
    maintenanceStatusReason?: string;
    layout?: {
        column: number;
        row: number;
        openDirection?: string;
    };
    usageType?: LockerUsageType;
}

export enum MaintenanceStatus {
    NONE = 'none',
    TRIAGE = 'triage',
    MAINTENANCE = 'maintenance',
    VERIFICATION = 'verification',
}

export interface BloqObjectResponse {
    _id: string;
    active: boolean;
    externalId?: string;
    lockers: BloqLocker[];
    details?: {
        country: string;
    };
}
