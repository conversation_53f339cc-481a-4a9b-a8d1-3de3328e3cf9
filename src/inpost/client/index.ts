import {
    type FunctionalityErrorRequest,
    type GlobalResponseModel,
    type UserRolesDeltaRequest,
    type CouriersDeltaPageRequest,
    type MachineLayoutRequest,
    type MassiveCompartmentOpenAlertRequest,
    type ReportStatusRequest,
    type ReportCompartmentStates,
    type ChangeParcelStatusRequest,
    type PackExistsRequest,
    type GetParcelByQuickSendCodeRequest,
    type GlobalResponseModelWithParcel,
    type CheckPrelabeledParcelRequest,
    type GlobalResponseModelWithReverseReturnParcel,
    GlobalResponseModelCouriersChangedPayload,
    GlobalResponseModelUserRolesPayload,
    type QRCodeParcelRequest,
    QRCodeParcelResponse,
    QRCodeErrorResponse,
} from '../models';
import { type AccessToken, type InPostOAuthToken } from '../models/access-token';

export interface InPostApiClient {
    getAccessToken: () => Promise<InPostOAuthToken>;

    reportBoxMachineFunctionalityError: (
        accessToken: AccessToken,
        boxmachinename: string,
        countryCode: string,
        body: FunctionalityErrorRequest,
    ) => Promise<GlobalResponseModel>;

    getUserRolesDelta: (
        accessToken: AccessToken,
        boxmachinename: string,
        countryCode: string,
        body: UserRolesDeltaRequest,
    ) => Promise<GlobalResponseModelUserRolesPayload>;

    getCouriersDeletedDeltaPage: (
        accessToken: AccessToken,
        boxmachinename: string,
        countryCode: string,
        body: CouriersDeltaPageRequest,
    ) => Promise<GlobalResponseModel>;

    getCouriersChangedDeltaPage: (
        accessToken: AccessToken,
        boxmachinename: string,
        countryCode: string,
        body: CouriersDeltaPageRequest,
    ) => Promise<GlobalResponseModelCouriersChangedPayload>;

    setBoxMachineLayout: (
        accessToken: AccessToken,
        boxmachinename: string,
        countryCode: string,
        body: MachineLayoutRequest,
    ) => Promise<GlobalResponseModel>;

    sendMassiveCompartmentOpenAlert: (
        accessToken: AccessToken,
        countryCode: string,
        body: MassiveCompartmentOpenAlertRequest,
    ) => Promise<GlobalResponseModel>;

    reportStatus: (
        accessToken: AccessToken,
        countryCode: string,
        body: ReportStatusRequest,
    ) => Promise<GlobalResponseModel>;

    reportCompartmentStates: (
        accessToken: AccessToken,
        countryCode: string,
        boxmachinename: string,
        body: ReportCompartmentStates,
    ) => Promise<GlobalResponseModel>;

    notifyAlive: (accessToken: AccessToken, countryCode: string) => Promise<GlobalResponseModel>;

    changeParcelStatus: (
        accessToken: AccessToken,
        boxmachinename: string,
        countryCode: string,
        body: ChangeParcelStatusRequest,
    ) => Promise<GlobalResponseModel>;

    getUnknownParcelFaultMessage: (
        accessToken: AccessToken,
        countryCode: string,
        body: any,
    ) => Promise<GlobalResponseModel>;

    packExist: (
        accessToken: AccessToken,
        boxmachinename: string,
        countryCode: string,
        body: PackExistsRequest,
    ) => Promise<GlobalResponseModel>;

    getParcelByQuickSendCode: (
        accessToken: AccessToken,
        boxMachineName: string,
        countryCode: string,
        body: GetParcelByQuickSendCodeRequest,
    ) => Promise<GlobalResponseModelWithParcel>;

    getBoxMachineExtendedLayoutPage: (
        accessToken: AccessToken,
        boxMachineName: string,
        countryCode: string,
        body: any,
    ) => Promise<GlobalResponseModel>;

    createReverseReturnParcelFromBoxMachine: (
        accessToken: AccessToken,
        boxMachineName: string,
        countryCode: string,
        body: any,
    ) => Promise<GlobalResponseModelWithReverseReturnParcel>;

    checkPrelabeledParcel: (
        accessToken: AccessToken,
        boxMachineName: string,
        countryCode: string,
        body: CheckPrelabeledParcelRequest,
    ) => Promise<GlobalResponseModelWithParcel>;

    changeBoxSizeName: (
        accessToken: AccessToken,
        countryCode: string,
        body: any,
    ) => Promise<GlobalResponseModel>;

    courierUpdateAdHoc: (
        accessToken: AccessToken,
        login: string,
        boxMachineName: string,
        countryCode: string,
    ) => Promise<GlobalResponseModel>;

    getBoxMachineInfo: (accessToken: AccessToken) => Promise<GlobalResponseModel>;

    getBoxMachineConfigurationParameters: (
        accessToken: AccessToken,
    ) => Promise<GlobalResponseModel>;

    getParcelByQRCode: (
        accessToken: AccessToken,
        countryCode: string,
        body: QRCodeParcelRequest,
    ) => Promise<QRCodeParcelResponse>;
}
