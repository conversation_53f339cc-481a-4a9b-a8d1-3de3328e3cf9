import { InPostApiClient } from '..';

export class FakeInPostApiClient implements InPostApiClient {
    reportBoxMachineFunctionalityError = jest.fn();
    getUserRolesDelta = jest.fn();
    getCouriersDeletedDeltaPage = jest.fn();
    getCouriersChangedDeltaPage = jest.fn();
    setBoxMachineLayout = jest.fn();
    sendMassiveCompartmentOpenAlert = jest.fn();
    reportStatus = jest.fn();
    reportCompartmentStates = jest.fn();
    notifyAlive = jest.fn();
    changeParcelStatus = jest.fn();
    getUnknownParcelFaultMessage = jest.fn();
    packExist = jest.fn();
    getParcelByQuickSendCode = jest.fn();
    getBoxMachineExtendedLayoutPage = jest.fn();
    createReverseReturnParcelFromBoxMachine = jest.fn();
    checkPrelabeledParcel = jest.fn();
    changeBoxSizeName = jest.fn();
    courierUpdateAdHoc = jest.fn();
    getBoxMachineInfo = jest.fn();
    getBoxMachineConfigurationParameters = jest.fn();
    getAccessToken = jest.fn();
    getParcelByQRCode = jest.fn();
}
