import { InPostApiClientImpl } from '.';
import { ApplicationEnvironment } from '../../../core/env';
import { FakeHTTPClient } from '../../../core/http-client/fake';
import {
    BoxMachineParcelStatus,
    FunctionalityErrorRequest,
    FunctionalityErrorRequestErrorCode,
    GlobalResponseModelStatus,
    GlobalResponseModelWithParcel,
    GlobalResponseModelWithReverseReturnParcel,
    ReportCompartmentStates,
    ReverseReturnParcelResponse,
    SessionType,
} from '../../models';
import { AccessToken } from '../../models/access-token';
import { ActionSource, ParcelStatus } from '../../models/enums';

const env = {
    INPOST_OAUTH_HOST: 'https://authservice-test.inpost',
    INPOST_API_HOST: 'https://api-test-fake.inpost',
    INPOST_OAUTH_CLIENT_ID: 'dummy-client-id',
    INPOST_OAUTH_CLIENT_SECRET: 'dummy-client-secret',
} as unknown as ApplicationEnvironment;

describe('InPostClientImpl', () => {
    const dummyAccessToken: AccessToken = {
        token: 'dummy-access-token',
        validity: new Date(Date.now() + 24 * 60 * 60 * 1000),
    };

    describe('getAccessToken', () => {
        it('should retrieve an access token from InPost', async () => {
            const httpClient = new FakeHTTPClient();
            const requestSpy = jest.spyOn(httpClient, 'request').mockResolvedValueOnce({
                data: {
                    access_token: 'dummy-inpost-access-token',
                    expires_in: 123,
                    refresh_expires_in: 456,
                    token_type: 'dummy-token-type',
                    'not-before-policy': 789,
                    scope: 'dummy-scope',
                },
            });

            const client = new InPostApiClientImpl({ httpClient, env });

            const token = await client.getAccessToken();

            expect(requestSpy).toHaveBeenCalledTimes(1);
            expect(requestSpy).toHaveBeenCalledWith({
                method: 'post',
                url: `${env.INPOST_OAUTH_HOST}/auth/realms/accounts/protocol/openid-connect/token`,
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    Connection: 'keep-alive',
                },
                httpsAgentOptions: { rejectUnauthorized: false },
                data: {
                    client_id: env.INPOST_OAUTH_CLIENT_ID,
                    client_secret: env.INPOST_OAUTH_CLIENT_SECRET,
                    grant_type: 'client_credentials',
                },
            });

            expect(token.access_token).toEqual('dummy-inpost-access-token');
            expect(token.expires_in).toEqual(123);
            expect(token.refresh_expires_in).toEqual(456);
            expect(token.token_type).toEqual('dummy-token-type');
            expect(token['not-before-policy']).toEqual(789);
            expect(token.scope).toEqual('dummy-scope');
        });
    });

    describe('changeParcelStatus', () => {
        it('should perform a call to InPost to change the parcel status', async () => {
            const httpClient = new FakeHTTPClient();

            const requestSpy = jest.spyOn(httpClient, 'request').mockResolvedValueOnce({
                data: {
                    status: 'OK',
                },
            });

            const client = new InPostApiClientImpl({ httpClient, env });

            const requestData = {
                parcelCode: ['dummy-parcel-code'],
                status: ParcelStatus.MISSING_PARCEL,
                boxName: 'dummy-box-name',
                boxSize: 'dummy-box-size',
                timestamp: new Date().toString(),
                actionSource: ActionSource.PANEL,
            };

            const response = await client.changeParcelStatus(
                dummyAccessToken,
                'dummy-machine-name',
                'dummy-country-code',
                requestData,
            );

            expect(requestSpy).toHaveBeenCalledTimes(1);
            expect(requestSpy).toHaveBeenCalledWith({
                method: 'post',
                url: `${env.INPOST_API_HOST}/api/v1/boxmachineparcel/changeParcelStatus`,
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer dummy-access-token`,
                    boxmachinename: 'dummy-machine-name',
                    CountryCode: 'dummy-country-code',
                    Connection: 'keep-alive',
                },
                data: requestData,
            });

            expect(response).toEqual({
                status: 'OK',
            });
        });
    });

    describe('reportBoxMachineFunctionalityError', () => {
        it('should perform a call to InPost to report a functionality error in the locker', async () => {
            const httpClient = new FakeHTTPClient();

            const requestSpy = jest.spyOn(httpClient, 'request').mockResolvedValueOnce({
                data: {
                    sessionId: 'dummy-session-id',
                    statusMessage: 'dummy-status-message',
                    timestamp: new Date().toUTCString(),
                    status: 'OK',
                },
            });

            const client = new InPostApiClientImpl({ httpClient, env });

            const requestData: FunctionalityErrorRequest = {
                timestamp: new Date().toISOString(),
                parcelCode: 'dummy-parcel-code',
                errorCode: FunctionalityErrorRequestErrorCode.ParcelDoesNotExistInCentralSystem,
                errorDescription: 'Parcel is missing',
                sessionType: SessionType.Courier,
                functionalityContext: '',
                courierDocumentNr: '',
                compartment: '',
            };

            const response = await client.reportBoxMachineFunctionalityError(
                dummyAccessToken,
                'dummy-box-machine-name',
                'dummy-country-code',
                requestData,
            );

            expect(requestSpy).toHaveBeenCalledTimes(1);
            expect(requestSpy).toHaveBeenCalledWith({
                method: 'post',
                url: `${env.INPOST_API_HOST}/api/v1/functionalityerror/reportBoxMachineFunctionalityError`,
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer dummy-access-token`,
                    boxmachinename: 'dummy-box-machine-name',
                    CountryCode: 'dummy-country-code',
                    Connection: 'keep-alive',
                },
                data: requestData,
            });

            expect(response).toEqual({
                sessionId: 'dummy-session-id',
                statusMessage: 'dummy-status-message',
                timestamp: new Date().toUTCString(),
                status: 'OK',
            });
        });
    });

    describe('reportCompartmentStates', () => {
        it('should perform a call to InPost to report compartment states', async () => {
            const httpClient = new FakeHTTPClient();

            const requestSpy = jest.spyOn(httpClient, 'request').mockResolvedValueOnce({
                data: {
                    sessionId: 'dummy-session-id',
                    statusMessage: 'dummy-status-message',
                    timestamp: new Date().toUTCString(),
                    status: 'OK',
                },
            });

            const client = new InPostApiClientImpl({ httpClient, env });

            const requestData: ReportCompartmentStates = {
                timestamp: new Date().toISOString(),
                sessionType: SessionType.Courier,
                state: 'compartmentState',
                names: ['A1'],
            };

            const response = await client.reportCompartmentStates(
                dummyAccessToken,
                'dummy-country-code',
                'dummy-box-machine-name',
                requestData,
            );

            expect(requestSpy).toHaveBeenCalledTimes(1);
            expect(requestSpy).toHaveBeenCalledWith({
                method: 'post',
                url: `${env.INPOST_API_HOST}/api/v1/boxmachinestatus/reportCompartmentStates`,
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer dummy-access-token`,
                    CountryCode: 'dummy-country-code',
                    Connection: 'keep-alive',
                    boxmachinename: 'dummy-box-machine-name',
                },
                data: requestData,
            });

            expect(response).toEqual({
                sessionId: 'dummy-session-id',
                statusMessage: 'dummy-status-message',
                timestamp: new Date().toUTCString(),
                status: 'OK',
            });
        });
    });

    describe('courierUpdateAdhoc', () => {
        it('should perform a call to InPost to get courier info', async () => {
            const httpClient = new FakeHTTPClient();

            const requestSpy = jest.spyOn(httpClient, 'request').mockResolvedValueOnce({
                data: {
                    sessionId: 'dummy-session-id',
                    statusMessage: 'dummy-status-message',
                    timestamp: new Date().toUTCString(),
                    status: 'OK',
                },
            });

            const client = new InPostApiClientImpl({ httpClient, env });

            const response = await client.courierUpdateAdHoc(
                dummyAccessToken,
                'dummy-login',
                'dummy-box-machine-name',
                'dummy-country',
            );

            expect(requestSpy).toHaveBeenCalledTimes(1);
            expect(requestSpy).toHaveBeenCalledWith({
                method: 'get',
                url: `${env.INPOST_API_HOST}/api/v1/courier/courierUpdateAdHoc/dummy-login`,
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer dummy-access-token`,
                    boxmachinename: 'dummy-box-machine-name',
                    CountryCode: 'dummy-country',
                    Connection: 'keep-alive',
                },
                data: undefined,
            });

            expect(response).toEqual({
                sessionId: 'dummy-session-id',
                statusMessage: 'dummy-status-message',
                timestamp: new Date().toUTCString(),
                status: 'OK',
            });
        });
    });

    describe('getUserRolesDelta', () => {
        it('should perform a call to InPost to get user roles delta', async () => {
            const httpClient = new FakeHTTPClient();

            const requestSpy = jest.spyOn(httpClient, 'request').mockResolvedValueOnce({
                data: [
                    {
                        id: 761,
                        name: '306',
                        mask: 64,
                    },
                ],
            });

            const client = new InPostApiClientImpl({ httpClient, env });

            const response = await client.getUserRolesDelta(
                dummyAccessToken,
                'dummy-machine-name',
                'dummy-country-code',
                {},
            );

            expect(requestSpy).toHaveBeenCalledTimes(1);
            expect(requestSpy).toHaveBeenCalledWith({
                method: 'post',
                url: `${env.INPOST_API_HOST}/api/v1/boxmachinesynchronization/getUserRolesDelta`,
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer dummy-access-token`,
                    boxmachinename: 'dummy-machine-name',
                    CountryCode: 'dummy-country-code',
                    Connection: 'keep-alive',
                },
                data: {},
            });

            expect(response).toEqual([
                {
                    id: 761,
                    name: '306',
                    mask: 64,
                },
            ]);
        });
    });

    describe('getCouriersChangedDeltaPage', () => {
        it('should perform a call to InPost to get couriers changed delta page', async () => {
            const httpClient = new FakeHTTPClient();

            const requestSpy = jest.spyOn(httpClient, 'request').mockResolvedValueOnce({
                data: {
                    count: 1000,
                    result: [
                        {
                            id: 1,
                            documentNumber: '1234',
                            login: '2274D',
                            password: 'dummy-password',
                            userRole: 22,
                        },
                    ],
                    status: GlobalResponseModelStatus.OK_WITH_RESPONSE,
                },
            });

            const client = new InPostApiClientImpl({ httpClient, env });

            const response = await client.getCouriersChangedDeltaPage(
                dummyAccessToken,
                'dummy-machine-name',
                'dummy-country-code',
                {
                    paging: {
                        limit: 10,
                        offset: 0,
                    },
                },
            );

            expect(requestSpy).toHaveBeenCalledTimes(1);
            expect(requestSpy).toHaveBeenCalledWith({
                method: 'post',
                url: `${env.INPOST_API_HOST}/api/v1/boxmachinesynchronization/getCouriersChangedDeltaPage`,
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer dummy-access-token`,
                    boxmachinename: 'dummy-machine-name',
                    CountryCode: 'dummy-country-code',
                    Connection: 'keep-alive',
                },
                data: {
                    paging: {
                        limit: 10,
                        offset: 0,
                    },
                },
            });

            expect(response).toEqual({
                count: 1000,
                result: [
                    {
                        id: 1,
                        documentNumber: '1234',
                        login: '2274D',
                        password: 'dummy-password',
                        userRole: 22,
                    },
                ],
                status: GlobalResponseModelStatus.OK_WITH_RESPONSE,
            });
        });
    });

    describe('getParcelByQRCode', () => {
        it('should perform a call to InPost to get parcel by QR Code', async () => {
            const httpClient = new FakeHTTPClient();

            const requestSpy = jest.spyOn(httpClient, 'request').mockResolvedValueOnce({
                data: {
                    parcelCode: 'dummy-parcel-id',
                },
            });

            const client = new InPostApiClientImpl({ httpClient, env });

            const response = await client.getParcelByQRCode(
                dummyAccessToken,
                'dummy-country-code',
                {
                    boxMachineName: 'dummy-machine-name',
                    qrCode: 'dummy-qr-code',
                },
            );

            expect(requestSpy).toHaveBeenCalledTimes(1);
            expect(requestSpy).toHaveBeenCalledWith({
                method: 'post',
                url: `${env.INPOST_API_HOST}/api/v1/parcels/qrcode`,
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer dummy-access-token`,
                    boxmachinename: 'dummy-machine-name',
                    CountryCode: 'dummy-country-code',
                    Connection: 'keep-alive',
                },
                data: {
                    qrCode: 'dummy-qr-code',
                    boxMachineName: 'dummy-machine-name',
                },
            });
            expect(response).toEqual({
                parcelCode: 'dummy-parcel-id',
            });
        });
    });
});
