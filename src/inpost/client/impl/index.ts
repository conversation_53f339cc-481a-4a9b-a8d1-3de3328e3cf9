import { type Logger } from '../../../core/logger';
import { type InPostApiClient } from '..';
import { type ApplicationEnvironment } from '../../../core/env';
import { type HTTPClient } from '../../../core/http-client';
import {
    type CouriersDeltaPageRequest,
    type MachineLayoutRequest,
    type UserRolesDeltaRequest,
    type FunctionalityErrorRequest,
    type GlobalResponseModel,
    type MassiveCompartmentOpenAlertRequest,
    type ReportStatusRequest,
    type ReportCompartmentStates,
    type ChangeParcelStatusRequest,
    type PackExistsRequest,
    type GetParcelByQuickSendCodeRequest,
    GlobalResponseModelStatus,
    type CreateReverseReturnParcelFromBoxMachineRequest,
    type CheckPrelabeledParcelRequest,
    type GlobalResponseModelWithParcel,
    GlobalResponseModelCouriersChangedPayload,
    GlobalResponseModelUserRolesPayload,
    QRCodeParcelRequest,
    QRCodeParcelResponse,
    QRCodeErrorResponse,
} from '../../models';
import { type AccessToken, type InPostOAuthToken } from '../../models/access-token';
import { PartnerResponseError } from '../../../core/utils/error';
import { CountryCode } from '../../models/enums';

export class InPostApiClientImpl implements InPostApiClient {
    private readonly httpClient: HTTPClient;
    protected readonly env: ApplicationEnvironment;

    private readonly defaultCountryCode = CountryCode.GB;

    constructor(deps: { httpClient: HTTPClient; env: ApplicationEnvironment }) {
        this.httpClient = deps.httpClient;
        this.env = deps.env;

        this.getAccessToken = this.getAccessToken.bind(this);
        this.reportBoxMachineFunctionalityError =
            this.reportBoxMachineFunctionalityError.bind(this);
        this.getUserRolesDelta = this.getUserRolesDelta.bind(this);
        this.getCouriersDeletedDeltaPage = this.getCouriersDeletedDeltaPage.bind(this);
        this.getCouriersChangedDeltaPage = this.getCouriersChangedDeltaPage.bind(this);
        this.setBoxMachineLayout = this.setBoxMachineLayout.bind(this);
        this.sendMassiveCompartmentOpenAlert = this.sendMassiveCompartmentOpenAlert.bind(this);
        this.reportStatus = this.reportStatus.bind(this);
        this.reportCompartmentStates = this.reportCompartmentStates.bind(this);
        this.notifyAlive = this.notifyAlive.bind(this);
        this.changeParcelStatus = this.changeParcelStatus.bind(this);
        this.getUnknownParcelFaultMessage = this.getUnknownParcelFaultMessage.bind(this);
        this.packExist = this.packExist.bind(this);
        this.getParcelByQuickSendCode = this.getParcelByQuickSendCode.bind(this);
        this.getBoxMachineExtendedLayoutPage = this.getBoxMachineExtendedLayoutPage.bind(this);
        this.createReverseReturnParcelFromBoxMachine =
            this.createReverseReturnParcelFromBoxMachine.bind(this);
        this.checkPrelabeledParcel = this.checkPrelabeledParcel.bind(this);
        this.changeBoxSizeName = this.changeBoxSizeName.bind(this);
        this.courierUpdateAdHoc = this.courierUpdateAdHoc.bind(this);
        this.getBoxMachineInfo = this.getBoxMachineInfo.bind(this);
        this.getBoxMachineConfigurationParameters =
            this.getBoxMachineConfigurationParameters.bind(this);
        this.getParcelByQRCode = this.getParcelByQRCode.bind(this);

        this.getDefaultCountryCodeIfInvalid = this.getDefaultCountryCodeIfInvalid.bind(this);
    }

    get oauthHost(): string {
        return this.env.INPOST_OAUTH_HOST.startsWith('https://')
            ? this.env.INPOST_OAUTH_HOST
            : `https://${this.env.INPOST_OAUTH_HOST}`;
    }

    get baseURL(): string {
        return this.env.INPOST_API_HOST.startsWith('https://')
            ? this.env.INPOST_API_HOST
            : `https://${this.env.INPOST_API_HOST}`;
    }

    get oauthClientID(): string {
        return this.env.INPOST_OAUTH_CLIENT_ID;
    }

    get oauthClientSecret(): string {
        return this.env.INPOST_OAUTH_CLIENT_SECRET;
    }

    async getAccessToken(): Promise<InPostOAuthToken> {
        const response = await this.httpClient.request<{ data: InPostOAuthToken }>({
            method: 'post',
            url: `${this.oauthHost}/auth/realms/accounts/protocol/openid-connect/token`,
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                Connection: 'keep-alive',
            },
            httpsAgentOptions: { rejectUnauthorized: false },
            data: {
                client_id: this.oauthClientID,
                client_secret: this.oauthClientSecret,
                grant_type: 'client_credentials',
            },
        });

        return response.data;
    }

    async reportBoxMachineFunctionalityError(
        accessToken: AccessToken,
        boxmachinename: string,
        countryCode: string,
        body: FunctionalityErrorRequest,
    ): Promise<GlobalResponseModel> {
        const response = await this.httpClient.request<{ data: GlobalResponseModel }>({
            method: 'post',
            url: `${this.baseURL}/api/v1/functionalityerror/reportBoxMachineFunctionalityError`,
            headers: {
                'Content-Type': 'application/json',
                Connection: 'keep-alive',
                boxmachinename,
                CountryCode: this.getDefaultCountryCodeIfInvalid(countryCode),
                Authorization: `Bearer ${accessToken.token}`,
            },
            data: body,
        });

        if (response?.data?.status === GlobalResponseModelStatus.OK) {
            return response.data;
        } else {
            throw new PartnerResponseError({
                code: response.data?.status,
                message:
                    response.data?.exceptionMessage ?? 'Error on reporting functionality error',
            });
        }
    }

    async getUserRolesDelta(
        accessToken: AccessToken,
        boxmachinename: string,
        countryCode: string,
        body: UserRolesDeltaRequest,
    ): Promise<GlobalResponseModelUserRolesPayload> {
        const response = await this.httpClient.request<{
            data: GlobalResponseModelUserRolesPayload;
        }>({
            method: 'post',
            url: `${this.baseURL}/api/v1/boxmachinesynchronization/getUserRolesDelta`,
            headers: {
                'Content-Type': 'application/json',
                Connection: 'keep-alive',
                boxmachinename,
                CountryCode: this.getDefaultCountryCodeIfInvalid(countryCode),
                Authorization: `Bearer ${accessToken.token}`,
            },
            data: body,
        });

        return response.data;
    }

    async getCouriersDeletedDeltaPage(
        accessToken: AccessToken,
        boxmachinename: string,
        countryCode: string,
        body: CouriersDeltaPageRequest,
    ): Promise<GlobalResponseModel> {
        const response = await this.httpClient.request<{ data: GlobalResponseModel }>({
            method: 'post',
            url: `${this.baseURL}/api/v1/boxmachinesynchronization/getCouriersDeletedDeltaPage`,
            headers: {
                'Content-Type': 'application/json',
                Connection: 'keep-alive',
                boxmachinename,
                CountryCode: this.getDefaultCountryCodeIfInvalid(countryCode),
                Authorization: `Bearer ${accessToken.token}`,
            },
            data: body,
        });

        return response.data;
    }

    async getCouriersChangedDeltaPage(
        accessToken: AccessToken,
        boxmachinename: string,
        countryCode: string,
        body: CouriersDeltaPageRequest,
    ): Promise<GlobalResponseModelCouriersChangedPayload> {
        const response = await this.httpClient.request<{
            data: GlobalResponseModelCouriersChangedPayload;
        }>({
            method: 'post',
            url: `${this.baseURL}/api/v1/boxmachinesynchronization/getCouriersChangedDeltaPage`,
            headers: {
                'Content-Type': 'application/json',
                Connection: 'keep-alive',
                boxmachinename,
                CountryCode: this.getDefaultCountryCodeIfInvalid(countryCode),
                Authorization: `Bearer ${accessToken.token}`,
            },
            data: body,
        });

        return response.data;
    }

    async setBoxMachineLayout(
        accessToken: AccessToken,
        boxmachinename: string,
        countryCode: string,
        body: MachineLayoutRequest,
    ): Promise<GlobalResponseModel> {
        const response = await this.httpClient.request<{ data: GlobalResponseModel }>({
            method: 'post',
            url: `${this.baseURL}/api/v1/boxmachinestatus/setBoxMachineLayout`,
            headers: {
                'Content-Type': 'application/json',
                Connection: 'keep-alive',
                boxmachinename,
                CountryCode: this.getDefaultCountryCodeIfInvalid(countryCode),
                Authorization: `Bearer ${accessToken.token}`,
            },
            data: body,
        });

        return response.data;
    }

    async sendMassiveCompartmentOpenAlert(
        accessToken: AccessToken,
        countryCode: string,
        body: MassiveCompartmentOpenAlertRequest,
    ): Promise<GlobalResponseModel> {
        const response = await this.httpClient.request<{ data: GlobalResponseModel }>({
            method: 'post',
            url: `${this.baseURL}/api/v1/boxmachinestatus/sendMassiveCompartmentOpenAlert`,
            headers: {
                'Content-Type': 'application/json',
                Connection: 'keep-alive',
                CountryCode: this.getDefaultCountryCodeIfInvalid(countryCode),
                Authorization: `Bearer ${accessToken.token}`,
            },
            data: body,
        });

        return response.data;
    }

    async reportStatus(
        accessToken: AccessToken,
        countryCode: string,
        body: ReportStatusRequest,
    ): Promise<GlobalResponseModel> {
        const response = await this.httpClient.request<{ data: GlobalResponseModel }>({
            method: 'post',
            url: `${this.baseURL}/api/v1/boxmachinestatus/reportStatus`,
            headers: {
                'Content-Type': 'application/json',
                Connection: 'keep-alive',
                CountryCode: this.getDefaultCountryCodeIfInvalid(countryCode),
                Authorization: `Bearer ${accessToken.token}`,
            },
            data: body,
        });

        return response.data;
    }

    async reportCompartmentStates(
        accessToken: AccessToken,
        countryCode: string,
        boxmachinename: string,
        body: ReportCompartmentStates,
    ): Promise<GlobalResponseModel> {
        const response = await this.httpClient.request<{ data: GlobalResponseModel }>({
            method: 'post',
            url: `${this.baseURL}/api/v1/boxmachinestatus/reportCompartmentStates`,
            headers: {
                'Content-Type': 'application/json',
                Connection: 'keep-alive',
                CountryCode: this.getDefaultCountryCodeIfInvalid(countryCode),
                Authorization: `Bearer ${accessToken.token}`,
                boxmachinename,
            },
            data: body,
        });

        return response.data;
    }

    async notifyAlive(accessToken: AccessToken, countryCode: string): Promise<GlobalResponseModel> {
        const response = await this.httpClient.request<{ data: GlobalResponseModel }>({
            method: 'post',
            headers: {
                Connection: 'keep-alive',
                CountryCode: this.getDefaultCountryCodeIfInvalid(countryCode),
                Authorization: `Bearer ${accessToken.token}`,
            },
            url: `${this.baseURL}/api/v1/boxmachinestatus/notifyAlive`,
            data: undefined,
        });

        return response.data;
    }

    async changeParcelStatus(
        accessToken: AccessToken,
        boxmachinename: string,
        countryCode: string,
        body: ChangeParcelStatusRequest,
    ): Promise<GlobalResponseModel> {
        const response = await this.httpClient.request<{ data: GlobalResponseModel }>({
            method: 'post',
            url: `${this.baseURL}/api/v1/boxmachineparcel/changeParcelStatus`,
            headers: {
                'Content-Type': 'application/json',
                Connection: 'keep-alive',
                boxmachinename,
                CountryCode: this.getDefaultCountryCodeIfInvalid(countryCode),
                Authorization: `Bearer ${accessToken.token}`,
            },
            data: body,
        });

        if (response?.data?.status === GlobalResponseModelStatus.OK) {
            return response.data;
        } else {
            throw new PartnerResponseError({
                code: response.data?.status,
                message:
                    response.data?.exceptionMessage ?? `Error on changing parcel status of parcel`,
            });
        }
    }

    async getUnknownParcelFaultMessage(
        accessToken: AccessToken,
        countryCode: string,
        body: any,
    ): Promise<GlobalResponseModel> {
        const response = await this.httpClient.request<{ data: GlobalResponseModel }>({
            method: 'post',
            url: `${this.baseURL}/api/v1/boxmachine/getUnknownParcelFaultMessage`,
            headers: {
                'Content-Type': 'application/json',
                Connection: 'keep-alive',
                CountryCode: this.getDefaultCountryCodeIfInvalid(countryCode),
                Authorization: `Bearer ${accessToken.token}`,
            },
            data: body,
        });

        return response.data;
    }

    async packExist(
        accessToken: AccessToken,
        boxmachinename: string,
        countryCode: string,
        body: PackExistsRequest,
    ): Promise<GlobalResponseModel> {
        const response = await this.httpClient.request<{ data: GlobalResponseModel }>({
            method: 'post',
            url: `${this.baseURL}/api/v1/backofficeparcel/packExist`,
            headers: {
                'Content-Type': 'application/json',
                Connection: 'keep-alive',
                boxmachinename,
                CountryCode: this.getDefaultCountryCodeIfInvalid(countryCode),
                Authorization: `Bearer ${accessToken.token}`,
            },
            data: body,
        });

        return response.data;
    }

    async getParcelByQuickSendCode(
        accessToken: AccessToken,
        boxMachineName: string,
        countryCode: string,
        body: GetParcelByQuickSendCodeRequest,
    ): Promise<GlobalResponseModelWithParcel> {
        const response = await this.httpClient.request<{ data: GlobalResponseModelWithParcel }>({
            method: 'post',
            url: `${this.baseURL}/api/v1/backofficeparcel/getParcelByQuickSendCode`,
            headers: {
                'Content-Type': 'application/json',
                boxmachinename: boxMachineName,
                Connection: 'keep-alive',
                CountryCode: this.getDefaultCountryCodeIfInvalid(countryCode),
                Authorization: `Bearer ${accessToken.token}`,
            },
            data: body,
        });

        if (response?.data?.status === GlobalResponseModelStatus.OK_WITH_RESPONSE) {
            return response.data;
        } else {
            throw new PartnerResponseError({
                code: response.data?.status,
                message:
                    response.data?.exceptionMessage ??
                    `Parcel with code ${body.quickSendCode} not found`,
            });
        }
    }

    async getBoxMachineExtendedLayoutPage(
        accessToken: AccessToken,
        countryCode: string,
        body: any,
    ): Promise<GlobalResponseModel> {
        const response = await this.httpClient.request<{ data: GlobalResponseModel }>({
            method: 'post',
            url: `${this.baseURL}/api/v1/backofficeparcel/getBoxMachineExtendedLayoutPage`,
            headers: {
                'Content-Type': 'application/json',
                Connection: 'keep-alive',
                CountryCode: this.getDefaultCountryCodeIfInvalid(countryCode),
                Authorization: `Bearer ${accessToken.token}`,
            },
            data: body,
        });

        return response.data;
    }

    async createReverseReturnParcelFromBoxMachine(
        accessToken: AccessToken,
        boxMachineName: string,
        countryCode: string,
        body: CreateReverseReturnParcelFromBoxMachineRequest,
    ): Promise<GlobalResponseModel> {
        const response = await this.httpClient.request<{ data: GlobalResponseModel }>({
            method: 'post',
            url: `${this.baseURL}/api/v1/backofficeparcel/createReverseReturnParcelFromBoxMachine`,
            headers: {
                'Content-Type': 'application/json',
                boxmachinename: boxMachineName,
                Connection: 'keep-alive',
                CountryCode: this.getDefaultCountryCodeIfInvalid(countryCode),
                Authorization: `Bearer ${accessToken.token}`,
            },
            data: body,
        });

        if (response?.data?.status === GlobalResponseModelStatus.OK_WITH_RESPONSE) {
            return response.data;
        } else {
            throw new PartnerResponseError({
                code: response.data?.status,
                message:
                    response.data?.exceptionMessage ?? `Parcel with code ${body.code} not found`,
            });
        }
    }

    async checkPrelabeledParcel(
        accessToken: AccessToken,
        boxMachineName: string,
        countryCode: string,
        body: CheckPrelabeledParcelRequest,
    ): Promise<GlobalResponseModelWithParcel> {
        const response = await this.httpClient.request<{ data: GlobalResponseModelWithParcel }>({
            method: 'post',
            url: `${this.baseURL}/api/v1/backofficeparcel/checkPrelabeledParcel`,
            headers: {
                'Content-Type': 'application/json',
                boxmachinename: boxMachineName,
                Connection: 'keep-alive',
                CountryCode: this.getDefaultCountryCodeIfInvalid(countryCode),
                Authorization: `Bearer ${accessToken.token}`,
            },
            data: body,
        });

        if (response?.data?.status === GlobalResponseModelStatus.OK_WITH_RESPONSE) {
            return response.data;
        } else {
            throw new PartnerResponseError({
                code: response.data?.status,
                message:
                    response.data?.exceptionMessage ??
                    `Parcel with code ${body.packCode} not found`,
            });
        }
    }

    async changeBoxSizeName(
        accessToken: AccessToken,
        countryCode: string,
        body: any,
    ): Promise<GlobalResponseModel> {
        const response = await this.httpClient.request<{ data: GlobalResponseModel }>({
            method: 'post',
            url: `${this.baseURL}/api/v1/backofficeparcel/changeBoxSizeName`,
            headers: {
                'Content-Type': 'application/json',
                Connection: 'keep-alive',
                CountryCode: this.getDefaultCountryCodeIfInvalid(countryCode),
                Authorization: `Bearer ${accessToken.token}`,
            },
            data: body,
        });

        return response.data;
    }

    async courierUpdateAdHoc(
        accessToken: AccessToken,
        login: string,
        boxMachineName: string,
        countryCode: string,
    ): Promise<GlobalResponseModel> {
        const response = await this.httpClient.request<{ data: GlobalResponseModel }>({
            method: 'get',
            url: `${this.baseURL}/api/v1/courier/courierUpdateAdHoc/${login}`,
            headers: {
                'Content-Type': 'application/json',
                Connection: 'keep-alive',
                boxmachinename: boxMachineName,
                CountryCode: this.getDefaultCountryCodeIfInvalid(countryCode),
                Authorization: `Bearer ${accessToken.token}`,
            },
            data: undefined,
        });

        return response.data;
    }

    async getBoxMachineInfo(accessToken: AccessToken): Promise<GlobalResponseModel> {
        const response = await this.httpClient.request<{ data: GlobalResponseModel }>({
            method: 'get',
            headers: {
                'Content-Type': 'application/json',
                Connection: 'keep-alive',
                Authorization: `Bearer ${accessToken.token}`,
            },
            url: `${this.baseURL}/api/v1/boxmachinesynchronization/getBoxMachineInfo`,
            data: undefined,
        });

        return response.data;
    }

    async getBoxMachineConfigurationParameters(
        accessToken: AccessToken,
    ): Promise<GlobalResponseModel> {
        const response = await this.httpClient.request<{ data: GlobalResponseModel }>({
            method: 'get',
            headers: {
                'Content-Type': 'application/json',
                Connection: 'keep-alive',
                Authorization: `Bearer ${accessToken.token}`,
            },
            url: `${this.baseURL}/api/v1/boxmachinesynchronization/getBoxMachineConfigurationParameters`,
            data: undefined,
        });

        return response.data;
    }

    // TODO: Remove after tests
    private getDefaultCountryCodeIfInvalid(countryCode: string): string {
        const effectiveCountryCode =
            !countryCode || countryCode.trim() === ''
                ? this.defaultCountryCode
                : countryCode.trim();

        return effectiveCountryCode;
    }

    async getParcelByQRCode(
        accessToken: AccessToken,
        countryCode: string,
        body: QRCodeParcelRequest,
    ): Promise<QRCodeParcelResponse> {
        const response = await this.httpClient.request<{
            data: QRCodeParcelResponse | QRCodeErrorResponse;
        }>({
            method: 'post',
            url: `${this.baseURL}/api/v1/parcels/qrcode`,
            headers: {
                'Content-Type': 'application/json',
                boxmachinename: body.boxMachineName,
                Connection: 'keep-alive',
                CountryCode: this.getDefaultCountryCodeIfInvalid(countryCode),
                Authorization: `Bearer ${accessToken.token}`,
            },
            data: body,
        });

        if ('parcelCode' in response.data) {
            return response.data;
        } else {
            const errorResponse = response.data as QRCodeErrorResponse;
            throw new PartnerResponseError({
                code: errorResponse.exceptionKey ?? 'QRCodeParcelNotFound',
                message:
                    errorResponse.exceptionKey ??
                    `Parcel with code QRCode: ${body.qrCode} not found`,
            });
        }
    }
}
