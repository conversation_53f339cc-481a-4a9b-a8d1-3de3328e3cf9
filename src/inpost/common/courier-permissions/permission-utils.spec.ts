import { BloqCourierPermissions } from '../../../bloqit/bloqs/models/courier-access-codes';
import { convertToBloqPermissions, decodePermissions } from './permission-utils';

describe('decodePermissions', () => {
    it('returns empty array for mask 0', () => {
        expect(decodePermissions(0)).toEqual(['NONE']);
    });

    it('returns correct permissions for mask 134225565', () => {
        expect(decodePermissions(134225565)).toEqual([
            'SEND_PARCELS',
            'COLLECT_AVIZO_PARCELS',
            'COLLECT_CLAIMED_PARCELS',
            'COLLECT_EXPIRED_PARCELS',
            'SEND_CHOOSE_SIZE',
            'CHANGE_PAPER',
            'CLEAN_BOXES',
            'CLEAN_EMPTY_BOXES',
            'CLEAN_SOILED_BOXES',
            'EXECUTE_TASKS',
        ]);
    });
});

describe('convertToBloqPermissions', () => {
    it('returns empty array for empty input', () => {
        expect(convertToBloqPermissions([])).toEqual([]);
    });

    it('returns empty array for NONE permission', () => {
        expect(convertToBloqPermissions(['NONE'])).toEqual([]);
    });

    it('returns correct bloq permissions for given permissions', () => {
        expect(
            convertToBloqPermissions([
                'SEND_PARCELS',
                'COLLECT_AVIZO_PARCELS',
                'SEND_CHOOSE_SIZE',
                'CHANGE_PAPER',
                'NONE',
            ]),
        ).toEqual([
            BloqCourierPermissions.DROP_OFF,
            BloqCourierPermissions.PICK_UP,
            BloqCourierPermissions.MAINTENANCE_ACCESS,
        ]);
    });
});
