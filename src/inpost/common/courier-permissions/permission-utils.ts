import { BloqCourierPermissions } from '../../../bloqit/bloqs/models/courier-access-codes';
import { Permission } from './permission';

export function decodePermissions(mask: number): string[] {
    if (mask === Permission.NONE) return ['NONE'];

    const matched: string[] = [];

    for (const [key, value] of Object.entries(Permission)) {
        if (typeof value === 'number' && mask & value) {
            matched.push(key);
        }
    }

    return matched;
}

export function convertToBloqPermissions(permissions: string[]): BloqCourierPermissions[] {
    const dropOffPermissions: (keyof typeof Permission)[] = [
        'SEND_PARCELS',
        'SEND_AVIZO_PARCELS',
        'SEND_CHOOSE_SIZE',
        'SEND_SELECTED_SIZE',
    ];
    const pickUpPermissions: (keyof typeof Permission)[] = [
        'COLLECT_AVIZO_PARCELS',
        'COLLECT_CLAIMED_PARCELS',
        'COLLECT_EXPIRED_PARCELS',
        'COLLECT_NOTLABELED_PARCELS',
        'COLLECT_PRELABELED_PARCELS',
    ];
    const maintenanceAccessPermissions: (keyof typeof Permission)[] = [
        'SERVICE_MODE',
        'CHANGE_PAPER',
        'VERIFIED_DAMAGED',
        'VERIFIED_DAMAGED_CARRIERNAME_REQUIRED',
        'OPEN_SERVICE_BOX',
        'OPEN_SERVICE_DOOR',
        'CHANGE_CASHBOX',
        'PRINT_CASH_REPORTS',
    ];
    const cleanBloqPermissions: (keyof typeof Permission)[] = [
        'CLEAN_BOXES',
        'CLEAN_EMPTY_BOXES',
        'CLEAN_SOILED_BOXES',
    ];
    const inspectBloqPermissions: (keyof typeof Permission)[] = [
        'INSPECT_BLOCKED',
        'INSPECT_BLOCKED_CARRIERNAME_REQUIRED',
    ];
    const emergencyAccessPermissions: (keyof typeof Permission)[] = [
        'EMERGENCY_OPEN',
        'EMERGENCY_TAKEOUT',
        'EMERGENCY_TAKEOUT_CARRIERNAME_REQUIRED',
    ];
    const adminAccessPermissions: (keyof typeof Permission)[] = [
        'HANDLE_PROMOTIONS',
        'EXECUTE_TASKS',
        'PRINT_REPORTS',
    ];

    const inpostToBloqMap: Partial<Record<keyof typeof Permission, BloqCourierPermissions>> = {
        ...Object.fromEntries(dropOffPermissions.map(p => [p, BloqCourierPermissions.DROP_OFF])),
        ...Object.fromEntries(pickUpPermissions.map(p => [p, BloqCourierPermissions.PICK_UP])),
        ...Object.fromEntries(
            maintenanceAccessPermissions.map(p => [p, BloqCourierPermissions.MAINTENANCE_ACCESS]),
        ),
        ...Object.fromEntries(
            cleanBloqPermissions.map(p => [p, BloqCourierPermissions.CLEAN_BLOQ]),
        ),
        ...Object.fromEntries(
            inspectBloqPermissions.map(p => [p, BloqCourierPermissions.INSPECT_BLOQ]),
        ),
        ...Object.fromEntries(
            emergencyAccessPermissions.map(p => [p, BloqCourierPermissions.EMERGENCY_ACCESS]),
        ),
        ...Object.fromEntries(
            adminAccessPermissions.map(p => [p, BloqCourierPermissions.ADMIN_ACCESS]),
        ),
    };

    return Array.from(
        new Set(
            permissions
                .map(key => inpostToBloqMap[key as keyof typeof Permission])
                .filter((v): v is BloqCourierPermissions => v !== undefined),
        ),
    );
}
