import { LockerType, LockerUsageType } from '../../bloqit/lockers/models';
import { BoxMachineParcelStatus, BoxSize } from '../models';

export const getLockerTypeFromBoxSize = (boxSize: string): string | undefined => {
    switch (boxSize) {
        case BoxSize.A:
            return LockerType.S;
        case BoxSize.B:
            return LockerType.M;
        case BoxSize.C:
            return LockerType.L;
        case BoxSize.D:
            return LockerType.XS;
        default:
            return undefined;
    }
};

export const getBoxSizeFromLockerType = (lockerType: string): string | undefined => {
    switch (lockerType) {
        case LockerType.S:
            return BoxSize.A;
        case LockerType.M:
            return BoxSize.B;
        case LockerType.L:
            return BoxSize.C;
        case LockerType.XL:
            return BoxSize.C;
        case LockerType.XS:
            return BoxSize.D;
        default:
            return undefined;
    }
};

export const getParcelStatusFromRentState = (state: string): BoxMachineParcelStatus => {
    switch (state) {
        case 'created':
        case 'starting':
            return BoxMachineParcelStatus.COURIER_DELIVERING;
        case 'in_progress':
            return BoxMachineParcelStatus.PLACED_BY_COURIER;
        case 'expired':
            return BoxMachineParcelStatus.EXPIRED;
        case 'finished':
            return BoxMachineParcelStatus.DELIVERED;
        case 'cancelled':
            return BoxMachineParcelStatus.CANCELED;
        default:
            return BoxMachineParcelStatus.NONE;
    }
};

export const getAlternativeFunctionFromUsageType = (
    usageType: LockerUsageType,
): string | undefined => {
    switch (usageType) {
        case LockerUsageType.ECU:
            return 'controllerBox';
        case LockerUsageType.BATTERY:
            return 'serviceBox';
        case LockerUsageType.STORAGE:
            return undefined;
    }
};

export const generateRandomPin = (length: number = 4): number => {
    const min = Math.pow(10, length - 1);
    const max = Math.pow(10, length) - 1;

    return Math.floor(Math.random() * (max - min + 1)) + min;
};
