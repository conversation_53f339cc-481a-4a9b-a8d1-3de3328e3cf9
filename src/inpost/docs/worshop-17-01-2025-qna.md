| Group                            | Question                                                                                                                                                                                                                      | Answer                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    |
| -------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| General questions                | Should we reportMachineFunctionalityError for all workflows that are interrupted?                                                                                                                                             | Correct we should.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
| Syncing courier credentials      | We understand PSP/CourierUpdateAdHoc is used to remotely validate a courier. Can we think of it as a way to "rehydrate" or cached courier credentials; for cases when a courier was introduced after we synced; for instance? | We can save courier details cached from out last sync ƒƒcall                                                                                                                                                                                                                                                                                                                                                                                                                                              |
| Courier drop-off / PutIn parcels | Which endpoint should we call to retrieve parcel data; given a barcode / parcel code? Is it checkPrelabeledParcel?                                                                                                            | It supposed to be there already with pre announce ….                                                                                                                                                                                                                                                                                                                                                                                                                                                      |
| Courier drop-off / PutIn parcels | What if the compartment didn't open? Are we following the same workflow as for "compartment soiled" or "compartment needs repair"?                                                                                            | The process stops and compartments ar marked damaged for investigation. Last 3 compartments assume no compartments are free?                                                                                                                                                                                                                                                                                                                                                                              |
| Courier drop-off / PutIn parcels | In the "parcel was already placed" flow; should we also reportMachineFunctionalityError?                                                                                                                                      | Apparently not needed.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    |
| Courier drop-off / PutIn parcels | Shouldn't we call "ChangeParcelStatus" when the compartment opens; so the parcel state moves to "COURIER_DELIVERING"? When exactly do the parcels change state; in relation to door openings and closings?                    | In general the courier delivering should be the input status for the parcel as they expect that the customer status to be on the parcel before courier enters the APM when talking about the last mile and after the manual confirmation the the parcel is stored in the compartment. The door closes and the courier confirms that parcel is inside then we change the parcel status in the BE (to stored). More generally this is intended to change the physical status f parcel in several situations |
| Courier drop-off / PutIn parcels | Should we send "ReportCompartmentState" for all door openings and closings?                                                                                                                                                   | No. Stands for changing the status for the compartment like damaged; verified; inspection; etc. If the APM is idle and the compartment is opened we should also r3eport. Unexpected states lbasically                                                                                                                                                                                                                                                                                                     |
| Courier drop-off / PutIn parcels | What's changeBoxSizeName for?                                                                                                                                                                                                 | Change box size to change the compartment parcel ? Not quite sure about this. Updates the binding between the parcel and the compartment …                                                                                                                                                                                                                                                                                                                                                                |
| Courier drop-off / PutIn parcels | /reportCompartmentStates: the "state" field does not have an enum for reference in the OpenAPI spec. What are the possible values?                                                                                            | Action to InPost. Will be sent later                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |
| Courier drop-off / PutIn parcels | In the Miro board there is "NotAvailableSizeMenu"; whilst in the openAPI spec there is "NoFreeCompartments". We assumed the first to be the "UI equivalent"; and the latter to be the one we should use. Is that correct?     | To be confirmed offline                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   |
| Courier pick-up                  | Does the parcel ever change states after TakenByCourier?                                                                                                                                                                      | We have but not from the APM (box) perspective. If this was first mile for example there are lots of states like sorting etc;but none relative to this APM                                                                                                                                                                                                                                                                                                                                                |
| Customer general menu            | Should we report "locker full" for the "no available compartments" flow?                                                                                                                                                      | Yes; we should                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |
| Customer dropoff                 | We assume createReverseReturnParcelFromBoxMachine is for returns. How to identify?                                                                                                                                            | --                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
| Customer dropoff                 | What does checkPrelabeledParcel do;exactly?                                                                                                                                                                                   | Its calling PSP to find the parcel by the parcel code;it should return the parcel entity                                                                                                                                                                                                                                                                                                                                                                                                                  |
| Customer dropoff                 | We assume getParcelByQuickSendCode is for unnanounced / labeless. Is that correct?                                                                                                                                            | The method for the label parcels identify by the regex parcel. If the input is 9 digit we assume it's the labels parcel and return exact the same parcel entity like the checkprelabeledparcel                                                                                                                                                                                                                                                                                                            |
| Customer dropoff                 | Should we always call the endpoints in parallel; given a code? If not;how to identify each type?                                                                                                                              | There are masks and regexes that will allow us to identify each separate case and act accordingly                                                                                                                                                                                                                                                                                                                                                                                                         |
