import { type RequestHandler, type Router } from 'express';
import { InPostInboundController } from './controller';
import { BloqitClientImpl } from '../../bloqit/sdk/impl';
import { type Logger } from '../../core/logger';
import { type HTTPClient } from '../../core/http-client';
import { type ApplicationEnvironment } from '../../core/env';
import { MappedLockerRepositoryImpl } from '../data-access/repositories/mapped-locker/impl/mapped-locker-repository-impl';

export const setup = (deps: {
    router: Router;
    httpClient: HTTPClient;
    logger: Logger;
    env: ApplicationEnvironment;
    mappedLockerRepository: MappedLockerRepositoryImpl;
}): Router => {
    const { env, router, httpClient, logger, mappedLockerRepository } = deps;

    const apiKey = env.INPOST_BLOQIT_API_KEY;
    const bloqitClient = new BloqitClientImpl({ httpClient, logger, apiKey });

    const ctrl = new InPostInboundController({
        logger,
        bloqitClient,
        mappedLockerRepository,
    });

    /**
     * Healthcheck endpoint
     * @url /health
     * @method GET
     */
    router.get('/health', ctrl.healthcheck as unknown as RequestHandler);

    /**
     * Toggle the machine block status
     * @url /v1/management/machineBlock
     * @method POST
     * @header boxMachineName - The ID of the machine to block/unblock
     * @body - The request body containing the block status {MachineBlockRequestBody}
     */
    router.post(
        '/v1/management/machineBlock',
        ctrl.toggleMachineBlock as unknown as RequestHandler,
    );

    /**
     * Get compartment info
     * @url /v1/management/compartmentInfoDTO
     * @method GET
     * @header boxMachineName - The ID of the machine to get compartment info from
     */
    router.get(
        '/v1/management/compartmentInfoDTO',
        ctrl.getCompartmentInfo as unknown as RequestHandler,
    );

    /**
     * Open compartments for a machine
     * @url /v1/management/openCompartments
     * @method POST
     * @header boxMachineName - The ID of the machine on which to open compartments
     * @body - The request body containing the list of box names (external id) {OpenCompartmentsRequestBody}
     */
    router.post(
        '/v1/management/openCompartments',
        ctrl.openCompartments as unknown as RequestHandler,
    );

    /**
     * Create or update a parcel
     * @url /v1/parcelStationService/parcel
     * @method POST
     * @header boxMachineName - The ID of the machine to create the parcel in
     * @body - The request body containing the parcel details {CreateParcelRequestBody}
     */
    router.post(
        '/v1/parcelStationService/parcel',
        ctrl.createOrUpdateParcel as unknown as RequestHandler,
    );

    /**
     * Deliver a parcel
     * @url /v1/parcelstationservice/delivered/:parcelCode
     * @method POST
     * @header boxMachineName - The ID of the machine to deliver the parcel to
     * @param parcelCode - The code of the parcel to be delivered
     */
    router.post(
        '/v1/parcelstationservice/delivered/:parcelCode',
        ctrl.deliverParcel as unknown as RequestHandler,
    );

    /**
     * Delete a parcel
     * @url /v1/parcelStationService/deleteParcel
     * @method POST
     * @header boxMachineName - The ID of the machine to delete the parcel from
     * @body - The request body containing the packCode
     */
    router.post(
        '/v1/parcelStationService/deleteParcel',
        ctrl.deleteParcel as unknown as RequestHandler,
    );

    /**
     * Update the compartment state
     * @url /v1/management/setCompartmentsState
     * @method POST
     * @header boxMachineName - The ID of the machine to update the compartment state
     * @body - The request body containing the compartment state {UpdateCompartmentRequestBody}
     */
    router.post(
        '/v1/management/setCompartmentsState',
        ctrl.updateCompartment as unknown as RequestHandler,
    );

    /**
     * Get the proximity code
     * @url /v1/management/proximityCode
     * @method GET
     * @header boxMachineName - The ID of the machine to get the proximity code from
     */
    router.get('/v1/management/proximityCode', ctrl.getProximityCode as unknown as RequestHandler);

    return router;
};
