openapi: 3.0.3
info:
    title: InPost -> Bloq.it Management API
    description: Inbound API for managing InPost machines, parcels, and compartments.
    version: 1.0.0
servers:
    - url: /inpost
paths:
    /health:
        get:
            summary: Healthcheck endpoint
            description: Checks the health of the API.
            responses:
                '200':
                    description: API is healthy.
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    status:
                                        type: string
                                        example: 'OK'
                                    message:
                                        type: string
                                        example: 'InPost API is healthy'

    /v1/management/machineBlock:
        post:
            summary: Toggle the machine block status
            description: Blocks or unblocks a machine.
            parameters:
                - name: boxMachineName
                  in: header
                  required: true
                  description: The ID of the machine to block/unblock.
                  schema:
                      type: string
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/MachineBlockRequest'
            responses:
                '200':
                    description: Machine block status updated successfully.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/SuccessResponse'
                '400':
                    description: Invalid request.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ErrorResponse'

    /v1/management/compartmentInfoDTO:
        get:
            summary: Get compartment info
            description: Retrieves information about compartments for a machine.
            parameters:
                - name: boxMachineName
                  in: header
                  required: true
                  description: The ID of the machine to get compartment info from.
                  schema:
                      type: string
            responses:
                '200':
                    description: Compartment information retrieved successfully.
                    content:
                        application/json:
                            schema:
                                type: array
                                items:
                                    $ref: '#/components/schemas/CompartmentInfo'
                '400':
                    description: Invalid request.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ErrorResponse'

    /v1/management/openCompartments:
        post:
            summary: Open compartments for a machine
            description: Opens specified compartments for a machine.
            parameters:
                - name: boxMachineName
                  in: header
                  required: true
                  description: The ID of the machine on which to open compartments.
                  schema:
                      type: string
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/OpenCompartmentsRequest'
            responses:
                '200':
                    description: Compartments opened successfully.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/OpenCompartmentsResponse'
                '400':
                    description: Invalid request.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ErrorResponse'

    /v1/parcelStationService/parcel:
        post:
            summary: Create or update a parcel
            description: Creates or updates a parcel in a machine.
            parameters:
                - name: boxMachineName
                  in: header
                  required: true
                  description: The ID of the machine to create the parcel in.
                  schema:
                      type: string
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreateParcelRequest'
            responses:
                '200':
                    description: Parcel created or updated successfully.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/SuccessResponse'
                '400':
                    description: Invalid request.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ErrorResponse'

    /v1/parcelstationservice/delivered/{parcelCode}:
        post:
            summary: Deliver a parcel
            description: Marks a parcel as delivered.
            parameters:
                - name: boxMachineName
                  in: header
                  required: true
                  description: The ID of the machine to deliver the parcel to.
                  schema:
                      type: string
                - name: parcelCode
                  in: path
                  required: true
                  description: The code of the parcel to be delivered.
                  schema:
                      type: string
            responses:
                '200':
                    description: Parcel delivered successfully.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/SuccessResponse'
                '400':
                    description: Invalid request.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ErrorResponse'

    /v1/parcelStationService/deleteParcel:
        post:
            summary: Delete a parcel
            description: Deletes a parcel from a machine.
            parameters:
                - name: boxMachineName
                  in: header
                  required: true
                  description: The ID of the machine to delete the parcel from.
                  schema:
                      type: string
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/DeleteParcelRequest'
            responses:
                '200':
                    description: Parcel deleted successfully.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/SuccessResponse'
                '400':
                    description: Invalid request.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ErrorResponse'

    /v1/management/proximityCode:
        get:
            summary: Get proximity code
            description: Retrieves the proximity code for a machine.
            parameters:
                - name: boxMachineName
                  in: header
                  required: true
                  description: The ID of the machine to get the proximity code for.
                  schema:
                      type: string
            responses:
                '200':
                    description: Proximity code retrieved successfully.
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    proximityCode:
                                        type: string
                                        pattern: '^\d{4}$'
                                        description: The proximity code for the machine.
                '400':
                    description: Invalid request.
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    status:
                                        type: string
                                        example: 'error'
                                    message:
                                        type: string
                                        example: 'Invalid machine ID or proximity code not found.'

    /v1/management/setCompartmentsState:
        post:
            summary: Update the compartment state
            description: Updates the state of compartments for a machine.
            parameters:
                - name: boxMachineName
                  in: header
                  required: true
                  description: The ID of the machine to update the compartment state.
                  schema:
                      type: string
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UpdateCompartmentRequest'
            responses:
                '200':
                    description: Compartment state updated successfully.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/SuccessResponse'
                '400':
                    description: Invalid request.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ErrorResponse'

components:
    schemas:
        MachineBlockRequest:
            type: object
            properties:
                block:
                    type: boolean
                    description: Whether to block or unblock the machine.

        OpenCompartmentsRequest:
            type: object
            properties:
                boxlist:
                    type: array
                    items:
                        type: string
                    description: List of compartment names to open.

        OpenCompartmentsResponse:
            type: object
            properties:
                boxlist:
                    type: array
                    items:
                        type: object
                        properties:
                            boxName:
                                type: string
                                description: Name of the compartment.
                            status:
                                type: string
                                description: Status of the compartment (e.g., OPENED, FAILED).

        CreateParcelRequest:
            type: object
            properties:
                boxMachine:
                    type: string
                    description: ID of the target box machine.
                carrier:
                    type: string
                    description: Carrier name.
                carrierReference:
                    type: string
                    description: Carrier reference for the parcel.
                customerPhone:
                    type: string
                    description: Customer's phone number.
                directParcel:
                    type: boolean
                    description: Whether the parcel is a direct parcel.
                endOfWeekCollection:
                    type: boolean
                    description: Whether the parcel is delivered during weekend.
                multiCompPotentialSize:
                    type: string
                    description: Potential size of the parcel for multicompartment.
                multiCompPotentialUuid:
                    type: string
                    description: Potential UUID for multicompartment.
                onDeliveryAmount:
                    type: number
                    description: Amount on delivery for the parcel.
                openCode:
                    type: number
                    description: Open code for the parcel.
                parcelAttributes:
                    type: array
                    items:
                        $ref: '#/components/schemas/ParcelAttribute'
                    description: List of attributes for the parcel.
                parcelCode:
                    type: string
                    description: Parcel code.
                payCode:
                    type: number
                    description: Payment code for the parcel.
                pickupWithoutLabel:
                    type: boolean
                    description: Whether the parcel is labelless.
                senderBoxMachine:
                    type: string
                    description: Name of the sender's box machine.
                size:
                    type: string
                    description: Size of the parcel.
                status:
                    type: string
                    description: Status of the parcel.

        DeleteParcelRequest:
            type: object
            properties:
                packCode:
                    type: string
                    description: Code of the parcel to delete.

        UpdateCompartmentRequest:
            type: object
            properties:
                boxes:
                    type: array
                    items:
                        type: string
                    description: List of compartments to update.
                state:
                    type: string
                    description: New state of the compartments.

        CompartmentInfo:
            type: object
            properties:
                isBusy:
                    type: boolean
                    description: Whether the compartment is busy.
                isInspectioned:
                    type: boolean
                    description: Whether the compartment is inspected.
                isOpened:
                    type: boolean
                    description: Whether the compartment is open.
                isSoiled:
                    type: boolean
                    description: Whether the compartment is soiled.
                isVerified:
                    type: boolean
                    description: Whether the compartment is verified.
                name:
                    type: string
                    description: Name of the compartment.
                parcelInfo:
                    type: array
                    items:
                        type: object
                        properties:
                            code:
                                type: string
                                description: Parcel code.
                            size:
                                type: string
                                description: Parcel size.
                            state:
                                type: string
                                description: Parcel state.
                parcelStationColumn:
                    type: integer
                    description: Column number of the compartment in the parcel station.
                parcelStationColumnName:
                    type: string
                    description: Name of the column in the parcel station.
                parcelStationSite:
                    type: string
                    description: Site number of the compartment in the parcel station.
                size:
                    type: string
                    description: Size of the compartment.
                state:
                    type: string
                    description: State of the compartment (e.g., EFFICIENT, BROKEN).
                stateDetails:
                    type: string
                    description: Details about the compartment state.
                usedDate:
                    type: string
                    format: date-time
                    description: Date when the compartment was last used.

        ParcelAttribute:
            type: object
            properties:
                name:
                    type: string
                    description: Name of the attribute.
                value:
                    type: string
                    description: Value of the attribute.

        SuccessResponse:
            type: object
            properties:
                status:
                    type: string
                    example: 'success'
                message:
                    type: string
                    example: 'Operation completed successfully.'
                payload:
                    type: object
                    description: Additional data related to the operation.

        ErrorResponse:
            type: object
            properties:
                status:
                    type: string
                    example: 'error'
                message:
                    type: string
                    example: 'An error occurred.'
