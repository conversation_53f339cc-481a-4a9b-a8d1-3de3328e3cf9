export enum Status {
    OK = 'OK',
    ERROR = 'ERROR',
}

export interface InPostBaseResponse {
    statusMessage: string;
    status: Status;
    timestamp: string;
    payload?: any;
}

export const InPostBaseResponseFactory = {
    create(props: { status: Status; message: string; payload?: any }): InPostBaseResponse {
        const { status, message, payload } = props;
        return {
            statusMessage: message,
            status,
            timestamp: new Date().toISOString(),
            payload,
        };
    },
    success(props: { message: string; payload?: any }): InPostBaseResponse {
        const { message, payload } = props;
        return this.create({ status: Status.OK, message, payload });
    },
    error(props: { message: string; payload?: any }): InPostBaseResponse {
        const { message, payload } = props;
        return this.create({ status: Status.ERROR, message, payload });
    },
};
