import type { HTTPRequest } from '../../../../http/models/extensions/express';
import { InboundCompartmentState } from '../../../models';

export interface UpdateCompartmentRequest extends HTTPRequest {
    body: UpdateCompartmentRequestBody;
}

/**
 * @description Interface for request body of update compartment request
 */
export interface UpdateCompartmentRequestBody {
    boxes: string[];
    state: InboundCompartmentState;
}
