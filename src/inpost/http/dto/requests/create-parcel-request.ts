import type { HTTPRequest } from '../../../../http/models/extensions/express';
import type { BoxMachineParcelStatus, BoxSize } from '../../../models';

export interface CreateParcelRequest extends HTTPRequest {
    body: CreateParcelRequestBody;
}

/**
 * @description Interface for request body of create parcel request
 */
export interface CreateParcelRequestBody {
    boxMachine: string;
    carrier: string;
    carrierReference: string;
    customerPhone: string;
    directParcel: boolean;
    endOfWeekCollection: boolean;
    multiCompPotentialSize: string;
    multiCompPotentialUuid: string;
    onDeliveryAmount: number;
    openCode: string;
    parcelAttributes: ParcelAttributeDto[];
    parcelCode: string;
    payCode: number;
    pickupWithoutLabel: boolean;
    senderBoxMachine: string;
    size: BoxSize;
    status: BoxMachineParcelStatus;
}

export interface ParcelAttributeDto {
    name: string;
    value: string;
}
