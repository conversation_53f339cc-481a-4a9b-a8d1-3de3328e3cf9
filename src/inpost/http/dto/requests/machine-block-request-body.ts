import type { HTTPRequest } from '../../../../http/models/extensions/express';

export interface MachineBlockRequest extends HTTPRequest {
    body: MachineBlockRequestBody;
}

/**
 * @description Interface for request body of machine block request
 * @param {boolean} block - Block or unblock machine
 * @param {string} machineId - Machine ID
 */
export interface MachineBlockRequestBody {
    block: boolean;
}
