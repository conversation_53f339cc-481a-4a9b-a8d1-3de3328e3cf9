# [DRAFT] InPost

## Syncing courier credentials

<details>
<summary>Happy path</summary>

```mermaid
sequenceDiagram
    locker ->> core : woke_up
    core ->> pis : [NEW] GET couriers/credentials
    pis ->> psp : PSP/getUserRolesDelta
    psp ->> pis : UserRoles[]
    pis ->> core: UserRoles[]
    core ->> locker: set_info
    locker ->> locker: save data locally
```

</details>

## Courier drop-off | PutIn parcels

A courier dropping off a parcel in a smart-locker.

To-do:

- clarify pre-announcement process
- add "compartment did not open" flow. Same as compartment did not close

<details>
<summary>Happy path (unannounced)</summary>

```mermaid
sequenceDiagram
    participant locker as locker
    participant core as core
    participant pis as pis
    participant psp as psp

    locker ->> core: validate_code
    core ->> pis: GET /rents/:id
    pis ->> psp: GET /parcels/:id/tbd
    psp ->> pis: ParcelData
    pis ->> core: ParcelData
    core ->> core: Set parcel state to CREATED
    core ->> locker: set_info { parcelData }
    locker ->> core: door_opened
    core ->> core: Set parcel state to STARTING
    core ->> pis: [?? WH] POST /rent/:rentId/events/starting
    pis ->> psp: POST /ChangeParcelStatus (COURIER_DELIVERING)
    locker ->> core: door_closed
    core ->> core: Set parcel state to IN_PROGRESS
    core ->> pis: [WH] POST /rent/:rentId/events/drop-off-confirmation
    pis ->> psp: POST /ChangeParcelStatus (STORED)
    pis ->> psp: POST /ChangeBoxSizeName
```

</details>

<details>
<summary>Parcel not found</summary>

```mermaid
sequenceDiagram
    locker ->> core : validate_code
    core ->> pis : GET rents/:id
    pis ->> psp : GET /parcels/tbd
    psp ->> pis : 400 ParcelNotFound
    pis ->> core : 400 ParcelNotFound
    core ->> locker: code_validation_result { success: false }
    locker ->> core: [NEW] workflow_interrupted { reason: ParcelNotFound }
    core ->> pis: [NEW WH] workflow_interrupted { parcelNotFound }
    pis ->> psp: POST /reportBoxMachineFunctionalityError [ParcelNotFound]
```

</details>

<details>
<summary>No compartments available</summary>

```mermaid
sequenceDiagram
    locker ->> core: locker_is_full
    core ->> pis: [WH | NEW] PUT /bloqs/events/full
    pis ->> psp: POST /reportBoxMachineFunctionalityError [NoFreeCompartments]
```

</details>

<details>
<summary>Parcel already placed</summary>

```mermaid
sequenceDiagram
    actor courier
    courier -->> locker: *scans code*
    locker ->> locker: validate_code
    locker --> courier: *displays "parcel already placed" msg*
```

</details>

<details>
<summary>Compartment needs cleaning</summary>

```mermaid
sequenceDiagram
    locker ->> core: validate_code
    core ->> pis: GET rents/:id
    pis ->> psp: GET /parcels/tbd
    psp ->> pis: ParcelData
    pis ->> core: ParcelData
    core ->> locker: set_info { parcelData }
    locker ->> core: door_opened
    core ->> core: Set parcel state to STARTING
    core ->> pis: [WH | NEW] /rents/events/starting
    pis ->> psp: POST /ChangeParcelStatus (CourierDelivering)
    locker ->> core: door_problem_reported
    core ->> pis: [WH] POST rents/:rendId/events [soiled]
    pis ->> psp: POST reportCompartmentStates [damaged]
    pis ->> psp: POST /reportBoxMachineFunctionalityError [soiled]
```

</details>

<details>
<summary>Compartment needs repair</summary>

```mermaid
sequenceDiagram
    locker ->> core: validate_code
    core ->> pis: GET rents/:id
    pis ->> psp: GET /parcels/tbd
    psp ->> pis: ParcelData
    pis ->> core: ParcelData
    core ->> locker: set_info { parcelData }
    locker ->> core: door_opened
    core ->> core: Set parcel state to STARTING
    core ->> pis: [WH | NEW] /rents/events/starting
    pis ->> psp: POST /ChangeParcelStatus (CourierDelivering)
    locker ->> core: door_problem_reported
    core ->> pis: [WH] POST rents/:rendId/events [needs repair]
    pis ->> psp: POST reportCompartmentStates [damaged]
    pis ->> psp: POST /reportBoxMachineFunctionalityError [needs repair]
```

</details>

<details>
<summary>Compartment do not close</summary>

```mermaid
sequenceDiagram
    locker ->> core: validate_code
    core ->> pis: GET rents/:id
    pis ->> psp: GET /parcels/tbd
    psp ->> pis: ParcelData
    pis ->> core: ParcelData
    core ->> locker: set_info { parcelData }
    locker ->> core: door_opened
    core ->> core: Set parcel state to STARTING
    core ->> pis: [WH | NEW] /rents/events/starting
    pis ->> psp: POST /ChangeParcelStatus (CourierDelivering)
    locker ->> core: door_problem_reported
    core ->> pis: [WH] POST rents/:rendId/events [do_not_close]
    pis ->> psp: POST reportCompartmentStates [Damaged]
    pis ->> psp: POST /reportBoxMachineFunctionalityError [DamagedCompartment]
```

</details>

## Courier pick-up

### Labeless parcels

<details>
<summary>Happy path</summary>

```mermaid
sequenceDiagram
    actor courier
    courier ->> locker: scans label / sticker
    locker ->> core: associate_label
    core ->> pis: POST rents/:id/label
    pis ->> psp: POST /api/v1/backofficeparcel/packExist
    psp ->> pis: 200 OK { packCode }
    pis ->> psp: [??] GET /tbd/parcels/packCode
    psp ->> pis: 200 OK { ParcelData }
    pis ->> core: 200 OK { parcelData }
    core ->> locker: associate_label_result { success: true }
    locker ->> core: door_opened
    core ->> pis: POST /rents/events/collect-item
    pis ->> psp: POST /changeParcelStatus [TakenByCourier]
    psp ->> pis: 200 OK
    pis ->> core: 200 OK
    locker ->> core: door_closed
    core ->> pis: POST /rents/events/finished
    pis ->> psp: POST /changeParcelStatus [??]
    psp ->> pis: 200 OK
    pis ->> core: 200 OK
```

</details>

<details>
<summary>Problem: Invalid label</summary>

```mermaid
sequenceDiagram
    locker ->> core: associate_label
    core ->> pis: POST rents/:id/label
    pis ->> psp: POST /api/v1/backofficeparcel/packExist
    psp ->> pis: 400 BAD REQUEST { error: 'invalid_label' }
    pis ->> psp: POST /reportBoxMachineFunctionalityError [InvalidLabel]
    pis ->> core: 400 BAD REQUEST { error: 'invalid_label' }
    core ->> locker: associate_label_result { success: false }
```

</details>

<details>
<summary>Problem: Label already used</summary>

```mermaid
sequenceDiagram
    locker ->> core: associate_label
    core ->> pis: POST rents/:id/label
    pis ->> psp: POST /api/v1/backofficeparcel/packExist
    psp ->> pis: 400 BAD REQUEST { error: 'invalid_label' }
    pis ->> psp: POST /reportBoxMachineFunctionalityError [IncorrectCodeIdenticalStickers]
    pis ->> core: 400 BAD REQUEST { error: 'invalid_label' }
    core ->> locker: associate_label_result { success: false }
```

</details>

### Labeled parcels

<details>
<summary>Happy path: parcels placed by customer</summary>

```mermaid
sequenceDiagram
    actor courier
    courier ->> locker: scans barcode
    locker ->> locker: validate_code
    locker ->> core: door_opened
    core ->> pis: [WH] POST /rents/events/collect-item
    pis ->> psp: POST /changeParcelStatus [TakenByCourier]
    courier ->> locker: scans barcode
    locker ->> locker: validate_code
    locker ->> core: door_closed
    core ->> pis: [WH] POST /rents/events/finished
    pis ->> psp: POST /changeParcelStatus [??]
```

</details>

<details>
<summary>Happy path: expired parcels</summary>

```mermaid
sequenceDiagram
    actor courier
    courier ->> locker: scans barcode
    locker ->> locker: validate_code
    locker ->> core: door_opened
    core ->> pis: [WH] POST /rents/events/collect-item
    pis ->> psp: POST /changeParcelStatus [NotAvailable]
    courier ->> locker: scans barcode
    locker ->> locker: validate_code
    locker ->> core: door_closed
    core ->> pis: [WH] POST /rents/events/finished
    pis ->> psp: POST /changeParcelStatus [??]
```

</details>

<details>
<summary>Happy path: claimed parcels</summary>

```mermaid
sequenceDiagram
    actor courier
    courier ->> locker: scans barcode
    locker ->> locker: validate_code
    locker ->> core: door_opened
    core ->> pis: [WH] POST /rents/events/collect-item
    pis ->> psp: POST /changeParcelStatus [Claimed]
    courier ->> locker: scans barcode
    locker ->> locker: validate_code
    locker ->> core: door_closed
    core ->> pis: [WH] POST /rents/events/finished
    pis ->> psp: POST /changeParcelStatus [??]
```

</details>

<details>
<summary>Problem: compartment do not close</summary>

```mermaid
sequenceDiagram
    actor courier
    courier ->> locker: scans barcode
    locker ->> locker: validate_code
    locker ->> core: door_opened
    core ->> pis: [WH] POST /rents/events/collect-item
    pis ->> psp: POST /changeParcelStatus [TakenByCourier]
    courier ->> locker: scans barcode
    locker ->> locker: validate_code
    locker ->> core: door_problem_reported (do_not_close)
    core ->> pis: [WH] POST rents/:rendId/events [compartment_do_not_close]
    pis ->> psp: POST reportCompartmentStates [Damaged]
    pis ->> psp: POST /reportBoxMachineFunctionalityError [CompartmentDoNotClose]
```

</details>

<details>
<summary>Problem: Compartment was open</summary>

```mermaid
sequenceDiagram
    locker ->> core: pickup_attempted
    locker ->> core: door_problem_reported
    core ->> pis: [WH] POST rents/:rendId/events [compartment_was_open]
    pis ->> psp: POST reportCompartmentStates [Damaged]
    pis ->> psp: POST /reportBoxMachineFunctionalityError [CompartmentWasOpen*]
```

</details>

<details>
<summary>Problem: Compartment did not open</summary>

```mermaid
sequenceDiagram
    locker ->> core: pickup_attempted
    locker ->> core: door_problem_reported
    core ->> pis: [WH] POST rents/:rendId/events [compartment_did_not_open]
    pis ->> psp: POST reportCompartmentStates [Damaged]
    pis ->> psp: POST /reportBoxMachineFunctionalityError [CompartmentDidNotOpen]
```

</details>

<details>
<summary>Problem: Compartment soiled</summary>

```mermaid
sequenceDiagram
    actor courier
    courier ->> locker: scans barcode
    locker ->> core: pickup_attempted
    locker ->> core: door_problem_reported
    core ->> pis: [WH] POST rents/:rendId/events [compartment_soiled]
    pis ->> psp: POST reportCompartmentStates [Damaged]
    pis ->> psp: POST /reportBoxMachineFunctionalityError [CompartmentSoiled]
```

</details>

<details>
<summary>Problem: Parcel is missing</summary>

```mermaid
sequenceDiagram
    actor courier
    courier ->> locker: scans barcode
    locker ->> locker: validate_code
    locker ->> core: door_opened
    core ->> pis: [WH] POST /rents/events/collect-item
    pis ->> psp: POST /changeParcelStatus [TakenByCourier]
    locker ->> core: door_problem_reported (parcel_is_missing)
    core ->> pis: [WH] POST rents/:rendId/events [parcel_is_missing]
    pis ->> psp: POST /changeParcelStatus [MissingParcel]
    pis ->> psp: POST /reportBoxMachineFunctionalityError [MissingParcel]
```

</details>

## Emergency take-out

<details>
<summary>Happy path</summary>

```mermaid
sequenceDiagram
    locker ->> core: pickup_attempted
    locker ->> core: door_opened
    core ->> core: Set parcel state to COLLECT_ITEM
    core ->> pis: [WH] POST /rent/:rentId/events/collect-item
    locker ->> core: door_closed
    core ->> core: Set parcel state to FINISHED
    core ->> pis: [WH] POST /rent/:rentId/events/finished
    pis ->> psp: POST /ChangeParcelStatus (EMERGENCY_DELIVERY)
    pis ->> psp: POST /checkPrelabeledParcel
```

</details>

<details>
<summary>Compartment do not open</summary>

```mermaid
sequenceDiagram
    locker ->> core: pickup_attempted
    locker ->> core: door_problem_reported
    core ->> pis: [WH] POST rents/:rendId/events [compartment_do_not_open]
    pis ->> psp: POST reportCompartmentStates [Damaged]
    pis ->> psp: POST /reportBoxMachineFunctionalityError [CannotOpenCompartment]
```

</details>

<details>
<summary>Compartment opened, but damaged</summary>

```mermaid
sequenceDiagram
    locker ->> core: pickup_attempted
    locker ->> core: door_opened
    core ->> core: Set parcel state to COLLECT_ITEM
    core ->> pis: [WH] POST /rent/:rentId/events/collect-item
    locker ->> core: door_problem_reported
    core ->> pis: [WH] POST rents/:rendId/events [compartment_damaged]
    pis ->> psp: POST reportCompartmentStates [Damaged]
    pis ->> psp: POST /reportBoxMachineFunctionalityError [CompartmentDamaged]
```

</details>

<details>
<summary>Parcel does not exist</summary>

```mermaid
sequenceDiagram
    locker ->> core: pickup_attempted
    locker ->> core: door_opened
    core ->> core: Set parcel state to COLLECT_ITEM
    core ->> pis: [WH] POST /rent/:rentId/events/collect-item
    locker ->> core: [??] parcel_do_not_exist
    core ->> pis: [?? WH] POST rents/:rendId/events [parcel_do_not_exist]
    pis ->> psp: [??] POST /reportBoxMachineFunctionalityError [ParcelDoNotExist]
```

</details>

<details>
<summary>Compartment do not close</summary>

```mermaid
sequenceDiagram
    locker ->> core: pickup_attempted
    locker ->> core: door_opened
    core ->> core: Set parcel state to COLLECT_ITEM
    core ->> pis: [?? WH] POST /rent/:rentId/events/collect-item
    locker ->> core: door_problem_reported { CompartmentDoNotClose }
    core ->> pis: [WH] POST rents/:rendId/events [compartment_damaged]
    pis ->> psp: POST reportCompartmentStates [Damaged]
    pis ->> psp: POST /reportBoxMachineFunctionalityError [CompartmentDamaged]
```

</details>

## Customer general menu

<details>
<summary>Customer sending</summary>

```mermaid
sequenceDiagram
    actor customer
    customer ->> locker: clicks "I'm sending"
    locker ->> locker: internal state is not "Operating"
    locker ->> customer: Displays unavailable screen
    locker ->> core: [?? NEW] workflow_interrupted [MachineIsNotOperational]
    core ->> pis: [?? NEW WH] /bloqs/events [workflow_interrupted | MachineIsNotOperational]
    pis ->> psp: /reportBoxMachineFunctionalityError [InvalidBoxMachineState]
```

</details>

<details>
<summary>Customer picking up</summary>

```mermaid
sequenceDiagram
    actor customer
    customer ->> locker: clicks "I'm picking up"
    locker ->> locker: internal state is not "Operating"
    locker ->> customer: Displays unavailable screen
    locker ->> core: [?? NEW] workflow_interrupted [MachineIsNotOperational]
    core ->> pis: [?? NEW WH] /bloqs/events [workflow_interrupted | MachineIsNotOperational]
    pis ->> psp: /reportBoxMachineFunctionalityError [InvalidBoxMachineState]
```

</details>

<details>
<summary>Customer scans barcode, but there are no available compartments</summary>

to-do: send to core the same way as the others
to-do: report "locker full"

```mermaid
sequenceDiagram
    actor customer
    customer ->> locker: scans barcode
    locker ->> locker: no available compartments
    locker ->> customer: Displays unavailable screen
```

</details>

## Customer dropoff

<details>
<summary>Happy path: parcel with label</summary>

```mermaid
sequenceDiagram
    actor customer
    customer ->> locker: enters parcel code
    locker ->> core: validate_code
    core ->> pis: GET rents/:id
    par
        pis ->> psp: createReverseReturnParcelFromBoxMachine
        psp ->> pis: ParcelData
    and
        pis ->> psp: checkPrelabeledParcel
        psp ->> pis: ParcelData
    and
        pis ->> psp: getParcelByQuickSendCode
        psp ->> pis: ParcelData
    end
    pis ->> core: 200 OK ParcelData
    core ->> locker: set_info { ParcelData }
    pis ->> psp: /changeBoxSizeName
    locker ->> core: door_opened
    core ->> pis: [?? NEW] POST rents/events/starting
    pis ->> psp: POST /changeParcelStatus: [CustomerDelivering]
    locker ->> core: door_closed
    core ->> pis: [WH] POST rents/events/in-progress
    locker ->> core: operation_confirmed
    core ->> pis: POST rents/events/drop-off-confirmation
    pis ->> psp: POST /changeParcelStatus: [PlacedByCustomer]
```

</details>

<details>
<summary>Customer inserted an incorrect code</summary>

- to-do: should report machine functionality error

```mermaid
sequenceDiagram
    actor customer
    customer ->> locker: enters parcel code / phone number
    locker ->> core: validate_code
    core ->> pis: GET /rents/:id
    psp ->> pis: 400 BAD REQUEST { error: 'incorrect_data' }
    pis ->> psp: POST /reportBoxMachineFunctionalityError [IncorrectData]
    pis ->> core: 400 BAD REQUEST { error: 'incorrect_data' }
    core ->> locker: code_validation_result { success: false }
```

</details>

<details>
<summary>Parcel already placed by courier</summary>

- to-do: should report machine functionality error

```mermaid
sequenceDiagram
    actor customer
    customer ->> locker: enters parcel code / phone number
    locker ->> locker: Parcel already sent by courier
    locker ->> customer: Displays "Already sent by courier"
```

</details>

<details>
<summary>Problem: no available compartments</summary>

```mermaid
sequenceDiagram
    actor customer
    customer ->> locker: enters parcel code / phone number
    locker ->> core: validate_code
    core ->> pis: GET rents/:id
    par
        pis ->> psp: createReverseReturnParcelFromBoxMachine
        psp ->> pis: ParcelData
    and
        pis ->> psp: checkPrelabeledParcel
        psp ->> pis: ParcelData
    and
        pis ->> psp: getParcelByQuickSendCode
        psp ->> pis: ParcelData
    end
    pis ->> core: 200 OK ParcelData
    core ->> locker: set_info { ParcelData }
    locker ->> core: operation_canceled [no_available_compartments]
    core ->> pis: [?? NEW WH] POST /rents/events | workflow_interrupted [NoAvailableComparments]
    pis ->> psp: POST /changeParcelStatus [Deleted]
    pis ->> psp: POST /reportBoxMachineFunctionalityError [NoAvailableCompartments]
```

</details>

<details>
<summary>Problem: Compartment do not open</summary>

```mermaid
sequenceDiagram
    actor customer
    customer ->> locker: enters parcel code / phone number
    locker ->> core: validate_code
    core ->> pis: GET rents/:id
    par
        pis ->> psp: createReverseReturnParcelFromBoxMachine
        psp ->> pis: ParcelData
    and
        pis ->> psp: checkPrelabeledParcel
        psp ->> pis: ParcelData
    and
        pis ->> psp: getParcelByQuickSendCode
        psp ->> pis: ParcelData
    end
    pis ->> core: 200 OK ParcelData
    core ->> locker: set_info { ParcelData }
    locker ->> core: door_problem_reported
    core ->> pis: [WH] POST rents/:rendId/events [compartment_do_not_open]
    pis ->> psp: POST reportCompartmentStates [Damaged]
    pis ->> psp: POST /reportBoxMachineFunctionalityError [CompartmentDoNotOpen]
```

</details>

<details>
<summary>Customer cancels the operation</summary>

- to-do: update parcel status on door_opened

```mermaid
sequenceDiagram
    actor customer
    customer ->> locker: enters parcel code / phone number
    locker ->> core: validate_code
    core ->> pis: GET rents/:id
    par
        pis ->> psp: createReverseReturnParcelFromBoxMachine
        psp ->> pis: ParcelData
    and
        pis ->> psp: checkPrelabeledParcel
        psp ->> pis: ParcelData
    and
        pis ->> psp: getParcelByQuickSendCode
        psp ->> pis: ParcelData
    end
    pis ->> core: 200 OK ParcelData
    core ->> locker: set_info { ParcelData }
    pis ->> psp: /changeBoxSizeName
    locker ->> core: door_opened
    locker ->> core: door_closed
    locker ->> core: operation_canceled
    core ->> pis: [?? NEW WH] POST /rents/events/drop-off-cancellation
    pis ->> psp: POST /changeParcelStatus [Deleted]
```

</details>

## Customer pickup

<details>
<summary>Happy path | Expired parcel</summary>

```mermaid
sequenceDiagram
    actor customer
    customer ->> locker: type code and phone number
    locker ->> locker : (locally) validate_code
    locker ->> core: door_opened
    locker ->> core: door_closed
    core ->> pis: [WH] POST /rent/:rentId/events/drop-off-confirmation
    pis ->> psp: POST /ChangeParcelStatus (Delivered)

```

</details>

<details>
<summary>Invalid code | Parcel already collected | Parcel in transit | Parcel taken by courier</summary>

```mermaid
sequenceDiagram
    actor customer
    customer ->> locker: *types code and phone number*
    locker ->> locker : (locally) validate_code
    locker ->> customer : *displays invalid code msg*
    locker ->> core: [NEW] workflow_interrupted { reason: InvalidCode }
    core ->> pis: [NEW WH] workflow_interrupted { InvalidCode }
    pis ->> psp: POST /reportBoxMachineFunctionalityError [InvalidCode]
```

</details>

<details>
<summary>Compartment do not open</summary>

```mermaid
sequenceDiagram
    actor customer
    customer ->> locker: *types code and phone number*
    locker ->> locker : (locally) validate_code
    customer ->> locker : *reports that compartment did not open*
    locker ->> core: door_problem_reported
    core ->> pis: [WH] POST rents/:rendId/events [compartment_do_not_open]
    pis ->> psp: POST reportCompartmentStates [Damaged]
    pis ->> psp: POST /reportBoxMachineFunctionalityError [CompartmentDoNotOpen]
```

</details>

## Legend

- ??: Highly debatable | I'm not sure

- NEW: New development needed

- WH: Webhook
