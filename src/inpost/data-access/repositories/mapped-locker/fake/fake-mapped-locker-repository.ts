import { MappedLocker } from '../../../../../bloqit/lockers/models';
import { MappedLockerRepository } from '../mapped-locker-repository';

export class FakeMappedLockerRepository implements MappedLockerRepository {
    async create(_args: { mappedLocker: MappedLocker }): Promise<void> {
        throw new Error('Not implemented. Please override this method in your test.');
    }

    async retrieveByPartnerName(_args: { partnerName: string }): Promise<MappedLocker | undefined> {
        throw new Error('Not implemented. Please override this method in your test.');
    }

    async retrieveByBloqId(_args: { bloqId: string }): Promise<MappedLocker | undefined> {
        throw new Error('Not implemented. Please override this method in your test.');
    }

    async retrieveBySharedId(_args: { sharedId: string }): Promise<MappedLocker | undefined> {
        throw new Error('Not implemented. Please override this method in your test.');
    }

    async remove(_args: { bloqId: string }): Promise<void> {
        throw new Error('Not implemented. Please override this method in your test.');
    }
}
