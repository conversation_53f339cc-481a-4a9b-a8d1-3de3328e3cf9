import { MappedLocker } from '../../../../../bloqit/lockers/models';
import { DocumentDatabaseClient } from '../../../../../core/data-access/document';
import { MappedLockerRepository } from '../mapped-locker-repository';

export class MappedLockerRepositoryImpl implements MappedLockerRepository {
    private readonly collection: DocumentDatabaseClient<MappedLocker>;

    constructor({ collection }: { collection: DocumentDatabaseClient<MappedLocker> }) {
        this.collection = collection;
    }

    async create(_args: { mappedLocker: MappedLocker }): Promise<void> {
        const { mappedLocker } = _args;
        await this.collection.save(mappedLocker);
    }

    async retrieveByBloqId(_args: { bloqId: string }): Promise<MappedLocker | undefined> {
        const { bloqId } = _args;
        return this.collection.findBy({ bloqId });
    }

    async retrieveBySharedId(_args: { sharedId: string }): Promise<MappedLocker | undefined> {
        const { sharedId } = _args;
        return this.collection.findBy({ sharedId });
    }

    async remove(_args: { bloqId: string }): Promise<void> {
        const { bloqId } = _args;
        await this.collection.removeOne({ bloqId });
    }
}
