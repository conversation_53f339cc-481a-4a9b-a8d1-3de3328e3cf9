import { MappedLocker } from '../../../../../bloqit/lockers/models';
import { DocumentDatabaseClient } from '../../../../../core/data-access/document';
import { FakeDocumentDatabaseClient } from '../../../../../core/data-access/document/fake';
import { MappedLockerRepository } from '../mapped-locker-repository';
import { MappedLockerRepositoryImpl } from './mapped-locker-repository-impl';

describe('MappedLockerRepositoryImpl', () => {
    let repository: MappedLockerRepository;
    let collection: DocumentDatabaseClient<MappedLocker>;

    beforeEach(() => {
        collection = new FakeDocumentDatabaseClient<MappedLocker>();
        repository = new MappedLockerRepositoryImpl({ collection });
    });

    describe('retrieve', () => {
        it('should retrieve a mapped locker by bloqId', async () => {
            const bloqId = 'test-bloq-id';
            const expectedLocker: MappedLocker = {
                bloqId,
                sharedId: 'shared-id',
                layout: [],
            };

            jest.spyOn(collection, 'findBy').mockResolvedValue(expectedLocker);

            const result = await repository.retrieveByBloqId({ bloqId });

            expect(result).toEqual(expectedLocker);
            expect(collection.findBy).toHaveBeenCalledWith({ bloqId });
        });

        it('should retrieve a mapped locker by sharedId', async () => {
            const sharedId = 'test-shared-id';
            const expectedLocker: MappedLocker = { bloqId: 'bloq-id', sharedId, layout: [] };

            jest.spyOn(collection, 'findBy').mockResolvedValue(expectedLocker);

            const result = await repository.retrieveBySharedId({ sharedId });

            expect(result).toEqual(expectedLocker);
            expect(collection.findBy).toHaveBeenCalledWith({ sharedId });
        });
    });

    describe('create', () => {
        it('should create a new mapped locker', async () => {
            const mappedLocker: MappedLocker = {
                bloqId: 'new-bloq-id',
                sharedId: 'new-shared-id',
                layout: [],
            };

            jest.spyOn(collection, 'save').mockResolvedValue();

            await repository.create({ mappedLocker });

            expect(collection.save).toHaveBeenCalledWith(mappedLocker);
        });
    });

    describe('remove', () => {
        it('should remove a mapped locker', async () => {
            const bloqId = 'test-bloq-id';
            jest.spyOn(collection, 'removeOne').mockResolvedValue();
            await repository.remove({ bloqId });
            expect(collection.removeOne).toHaveBeenCalledWith({ bloqId });
        });
    });
});
