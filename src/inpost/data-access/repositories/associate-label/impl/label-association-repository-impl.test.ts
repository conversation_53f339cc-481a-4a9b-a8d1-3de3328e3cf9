import { LabelAssociationRepositoryImpl } from './label-association-repository-impl';
import { DocumentDatabaseClient } from '../../../../../core/data-access/document';
import { LabelAssociation } from '../../../../models';
import { FakeDocumentDatabaseClient } from '../../../../../core/data-access/document/fake';

jest.mock('../../../../../core/data-access/document');

describe('LabelAssociationRepositoryImpl', () => {
    let repository: LabelAssociationRepositoryImpl;
    let collection;
    FakeDocumentDatabaseClient<LabelAssociation>;

    beforeEach(() => {
        collection = new FakeDocumentDatabaseClient<LabelAssociation>();
        repository = new LabelAssociationRepositoryImpl({ collection: collection });
    });

    describe('retrieve', () => {
        it('should return a label association when found', async () => {
            const parcelCode = '123';
            const mockAssociation = { partnerRentId: parcelCode } as LabelAssociation;
            jest.spyOn(collection, 'findBy').mockResolvedValueOnce(mockAssociation);

            const result = await repository.retrieve({ parcelCode });

            expect(collection.findBy).toHaveBeenCalledWith({ partnerRentId: parcelCode });
            expect(result).toEqual(mockAssociation);
        });

        it('should return undefined when no label association is found', async () => {
            const parcelCode = '123';
            jest.spyOn(collection, 'findBy').mockResolvedValueOnce(undefined);

            const result = await repository.retrieve({ parcelCode });

            expect(collection.findBy).toHaveBeenCalledWith({ partnerRentId: parcelCode });
            expect(result).toBeUndefined();
        });
    });

    describe('create', () => {
        it('should save a label association', async () => {
            const labelAssociation = {
                partnerRentId: '123',
                label: 'Temporary Label',
            } as LabelAssociation;
            jest.spyOn(collection, 'save').mockResolvedValueOnce(undefined);

            await repository.create({ labelAssociation });

            expect(collection.save).toHaveBeenCalledWith(labelAssociation);
        });
    });

    describe('remove', () => {
        it('should remove a label association by parcel code', async () => {
            const parcelCode = '123';
            jest.spyOn(collection, 'removeOne').mockResolvedValueOnce(undefined);

            await repository.remove({ parcelCode });

            expect(collection.removeOne).toHaveBeenCalledWith({ partnerRentId: parcelCode });
        });
    });
});
