import { DocumentDatabaseClient } from '../../../../../core/data-access/document';
import { LabelAssociation } from '../../../../models';
import { LabelAssociationRepository } from '../label-association-repository';

export class LabelAssociationRepositoryImpl implements LabelAssociationRepository {
    private readonly collection: DocumentDatabaseClient<LabelAssociation>;
    constructor(props: { collection: DocumentDatabaseClient<LabelAssociation> }) {
        this.collection = props.collection;
    }

    async retrieve(args: { parcelCode: string }): Promise<LabelAssociation | undefined> {
        const { parcelCode } = args;
        const association = await this.collection.findBy({ partnerRentId: parcelCode });
        if (!association) {
            return undefined;
        }

        return association;
    }

    async create(args: { labelAssociation: LabelAssociation }): Promise<void> {
        const { labelAssociation } = args;
        await this.collection.save(labelAssociation);
    }

    async remove(args: { parcelCode: string }): Promise<void> {
        const { parcelCode } = args;
        await this.collection.removeOne({ partnerRentId: parcelCode });
    }
}
