import { LabelAssociation } from '../../../../models';
import { LabelAssociationRepository } from '../label-association-repository';

export class FakeLabelAssociationRepository implements LabelAssociationRepository {
    async create(_args: { labelAssociation: LabelAssociation }): Promise<void> {
        throw new Error('Not implemented. Please override this method in your test.');
    }

    async retrieve(args: { parcelCode: string }): Promise<LabelAssociation | undefined> {
        throw new Error('Not implemented. Please override this method in your test.');
    }

    async remove(args: { parcelCode: string }): Promise<void> {
        throw new Error('Not implemented. Please override this method in your test.');
    }
}
