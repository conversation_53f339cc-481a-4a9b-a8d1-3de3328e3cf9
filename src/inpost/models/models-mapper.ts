import {
    GlobalResponseModelWithReverseReturnParcel,
    ParcelAttributeVO,
    ParcelRequest,
    ParcelResponse,
    QRCodeParcelResponse,
    Box,
    MachineLayoutRequest,
    BoxMachineLayout,
} from '.';
import { BloqLocker } from '../../bloqit/bloqs/models';
import { LockerUsageType, MappedLocker } from '../../bloqit/lockers/models';
import {
    PostPickupAction,
    PrePickupAction,
    PrePickupActionType,
    Rent,
} from '../../bloqit/rents/models/rent';
import {
    generateRandomPin,
    getAlternativeFunctionFromUsageType,
    getBoxSizeFromLockerType,
    getLockerTypeFromBoxSize,
} from '../common';

export class InPostModelsMapper {
    static mapParcelToBloqitRent(responseWithParcel: ParcelResponse | undefined): Rent {
        if (responseWithParcel === undefined) {
            throw new Error('Parcel data is undefined');
        }
        const parcelData = responseWithParcel;

        const parcelIdentifier = parcelData.parcelCode;
        const pickUpCodes = { pin: parcelData.openCode };
        const customer = {
            name: parcelData.targetAddressVO?.name,
            email: parcelData.targetAddressVO?.email,
            phone: parcelData.customerPhone,
        };

        const isLabelless = parcelData.pickupWithoutLabel ?? false;
        const lockerType = getLockerTypeFromBoxSize(parcelData.size);

        const parcelAttributesToMetadata = this.convertParcelAttributesToRecord(
            parcelData.parcelAttributes,
        );

        const prePickupActions: PrePickupAction[] = [];
        const postPickupAction: PostPickupAction[] = [];

        if (isLabelless) {
            prePickupActions.push({
                order: 0,
                action: PrePickupActionType.ASSOCIATE_LABEL,
            });
        }

        const bloqitRent: Rent = {
            externalID: parcelIdentifier,
            dropOffCode: parcelIdentifier,
            customer,
            pickUpCodes,
            prePickupActions,
            postPickupAction,
            metadata: {
                size: lockerType,
                ...parcelAttributesToMetadata,
            },
            priority: isLabelless ? 1 : 0,
            expiryDate: undefined,
            setID: undefined,
            dimensions: {
                length: 0,
                width: 0,
                height: 0,
            },
        };

        return bloqitRent;
    }

    static convertParcelAttributesToRecord(
        attributes: ParcelAttributeVO[],
    ): Record<string, string> {
        const result: Record<string, string> = {};

        for (const attr of attributes) {
            result[attr.name] = attr.value;
        }

        return result;
    }

    static mapReverseReturnParcelToBloqitRent(
        responseReverseReturnParcel: GlobalResponseModelWithReverseReturnParcel,
    ): Rent {
        if (responseReverseReturnParcel.payload === undefined) {
            throw new Error('Parcel data is undefined');
        }

        const parcelData = responseReverseReturnParcel.payload;

        const parcelIdentifier = parcelData.parcelCode;
        const pickUpCodes = { pin: parcelData.openCode };
        const customer = {
            name: parcelData.targetAddress.name,
            email: parcelData.targetAddress.email,
            phone: parcelData.customerPhone,
        };

        const lockerType = getLockerTypeFromBoxSize(parcelData.size);

        const prePickupActions: PrePickupAction[] = [];
        const postPickupAction: PostPickupAction[] = [];

        const isLabelless = parcelData.pickupWithoutLabel ?? false;

        if (isLabelless) {
            prePickupActions.push({
                order: 0,
                action: PrePickupActionType.ASSOCIATE_LABEL,
            });
        }

        const bloqitRent: Rent = {
            externalID: parcelIdentifier,
            dropOffCode: parcelIdentifier,
            customer,
            pickUpCodes,
            prePickupActions,
            postPickupAction,
            metadata: {
                size: lockerType,
            },
            priority: isLabelless ? 1 : 0,
            expiryDate: undefined,
            setID: undefined,
            dimensions: {
                length: 0,
                width: 0,
                height: 0,
            },
        };

        return bloqitRent;
    }

    static mapQRCodeParcelToBloqitRent(responseQRCodeParcel: QRCodeParcelResponse): Rent {
        if (responseQRCodeParcel === undefined) {
            throw new Error('Parcel data is undefined');
        }

        const parcelData = responseQRCodeParcel;

        const parcelIdentifier = parcelData.parcelCode;

        /* Labelless InPost parcels have openCode set to 0 because they aren't picked up by customers.
         * However, core still requires a pickupCode that is:
         * - a number between 4 and 16 digits
         * - unique within a locker
         *
         * To meet these rules, we generate a random 10-digit PIN. This helps ensure:
         * - uniqueness among other parcels
         * - low risk of someone guessing the code and opening the locker by accident
         */
        const pickUpCodes = {
            pin: parcelData.openCode != 0 ? parcelData.openCode : generateRandomPin(10),
        };
        const customer = {
            phone: parcelData.customerPhone,
        };

        const isLabelless = parcelData.pickupWithoutLabel ?? false;
        const lockerType = getLockerTypeFromBoxSize(parcelData.parcelSize);

        const parcelAttributesToMetadata = this.convertParcelAttributesToRecord(
            parcelData.parcelAttributes,
        );

        const prePickupActions: PrePickupAction[] = [];
        const postPickupAction: PostPickupAction[] = [];

        if (isLabelless) {
            prePickupActions.push({
                order: 0,
                action: PrePickupActionType.ASSOCIATE_LABEL,
            });
        }

        const bloqitRent: Rent = {
            externalID: parcelIdentifier,
            dropOffCode: parcelIdentifier,
            customer,
            pickUpCodes,
            prePickupActions,
            postPickupAction,
            metadata: {
                size: lockerType,
                ...parcelAttributesToMetadata,
            },
            priority: isLabelless ? 1 : 0,
            expiryDate: undefined,
            setID: undefined,
            dimensions: {
                length: 0,
                width: 0,
                height: 0,
            },
        };

        return bloqitRent;
    }

    static mapBloqitRentToParcel(bloqitRent: Rent): ParcelRequest {
        return {
            carrier: bloqitRent.carrier ?? '',
            customerPhone: bloqitRent.customer?.phone,
            openCode: Object.values(bloqitRent.pickUpCodes ?? {})[0] as number,
            parcelAttributes: Object.entries(bloqitRent.metadata ?? {}).map(([name, value]) => ({
                name,
                value: String(value),
            })) as ParcelAttributeVO[],
            parcelCode: bloqitRent.externalID,
            pickupWithoutLabel:
                bloqitRent.prePickupActions?.some(
                    action => action.action === PrePickupActionType.ASSOCIATE_LABEL,
                ) ?? false,
        };
    }

    static mapBloqitRentToParcelTempFix(bloqitRent: any): ParcelRequest {
        return {
            carrier: bloqitRent.carrier ?? '',
            customerPhone: bloqitRent.customer?.phone,
            openCode: (bloqitRent.details?.pickUpCodes?.pin as number) ?? 0,
            parcelAttributes: Object.entries(bloqitRent.metadata ?? {}).map(([name, value]) => ({
                name,
                value: String(value),
            })) as ParcelAttributeVO[],
            parcelCode: bloqitRent.externalID,
            pickupWithoutLabel: bloqitRent.priority === 1,
        };
    }

    static transformLockers(
        bloqLockers: BloqLocker[],
        bloqId: string,
        externalId: string,
    ): MappedLocker {
        const ecuLocker = bloqLockers.find(locker => locker.usageType === 'ecu' && locker.layout);
        if (!ecuLocker || !ecuLocker.layout) {
            throw new Error('ECU locker with layout not found');
        }

        const ecuColumn = ecuLocker.layout.column;

        // Group lockers by column
        const lockersByColumn = new Map<number, BloqLocker[]>();

        for (const locker of bloqLockers) {
            if (!locker.layout) continue;
            const column = locker.layout.column;
            if (!lockersByColumn.has(column)) {
                lockersByColumn.set(column, []);
            }
            lockersByColumn.get(column)!.push(locker);
        }

        // Sort each column's lockers by row (ascending = top to bottom)
        for (const [column, lockers] of Array.from(lockersByColumn.entries())) {
            lockers.sort((a, b) => {
                const rowA = a.layout?.row ?? 0;
                const rowB = b.layout?.row ?? 0;
                return rowA - rowB;
            });
        }

        // Map lockers to inpost layout format
        const layout = Array.from(lockersByColumn.entries()).flatMap(([column, lockers]) => {
            let columnLabel: string;

            if (column === ecuColumn) {
                columnLabel = '1R';
            } else if (column > ecuColumn) {
                columnLabel = `${column - ecuColumn + 1}R`;
            } else {
                columnLabel = `${ecuColumn - column}L`;
            }

            return lockers.map((locker, index) => ({
                lockerId: locker._id,
                lockerTitle: locker.lockerTitle,
                partnerName: `${columnLabel}${index + 1}`,
                usageType: locker.usageType,
                partnerUsageType: getAlternativeFunctionFromUsageType(
                    locker.usageType ?? LockerUsageType.STORAGE,
                ),
                size: locker.type,
                partnerSize: getBoxSizeFromLockerType(locker.type),
                column: locker.layout!.column,
                row: locker.layout!.row,
            }));
        });

        const result: MappedLocker = {
            bloqId: bloqId,
            sharedId: externalId,
            layout,
        };

        return result;
    }

    static mapToMachineLayoutRequest(mappedLockers: MappedLocker): MachineLayoutRequest {
        const groupedByColumn: Record<string, Box[]> = {};

        for (const locker of mappedLockers.layout) {
            // Extract column name (e.g. from '2R4' to '2R')
            const columnMatch = locker.partnerName.match(/^\d+[RL]/);
            if (!columnMatch) continue;
            const column = columnMatch[0];

            if (!groupedByColumn[column]) {
                groupedByColumn[column] = [];
            }

            const box: Box = {
                size: locker.partnerSize,
                ...(locker.partnerUsageType
                    ? { alternativeFunction: locker.partnerUsageType }
                    : {}),
            };

            groupedByColumn[column].push(box);
        }

        const boxMachineLayout: BoxMachineLayout[] = Object.entries(groupedByColumn).map(
            ([column, boxes]) => ({
                column,
                moduleId: 'moduleId',
                boxes,
            }),
        );

        return {
            boxMachineLayout,
        };
    }
}
