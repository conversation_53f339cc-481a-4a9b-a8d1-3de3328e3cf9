export enum GlobalResponseModelStatus {
    OK = 'OK',
    OK_WITH_RESPONSE = 'OK_WITH_RESPONSE',
    EXCEPTION = 'EXCEPTION',
}

export enum FunctionalityContext {
    DataSynchronization = 'DataSynchronization',
    EmergencyTakeout = 'EmergencyTakeout',
    EmergencyOpen = 'EmergencyOpen',
    CleanEmptyBoxes = 'CleanEmptyBoxes',
    CleanSoiledBoxes = 'CleanSoiledBoxes',
    CleanBoxes = 'CleanBoxes',
    PickUpAvizoParcels = 'PickUpAvizoParcels',
    PutInParcels = 'PutInParcels',
    PutInAvizoParcels = 'PutInAvizoParcels',
    PickUpExpiredParcels = 'PickUpExpiredParcels',
    PickUpClaimedParcels = 'PickUpClaimedParcels',
    PickUpPrelabeledParcels = 'PickUpPrelabeledParcels',
    PickUpNotlabeledParcels = 'PickUpNotlabeledParcels',
    PickUpCustomerStoredParcels = 'PickUpCustomerStoredParcels',
    PutInMultiParcels = 'PutInMultiParcels',
    BusinessCustomerSendParcel = 'BusinessCustomerSendParcel',
    CustomerMultiParcel = 'CustomerMultiParcel',
    CustomerClaimMultiParcel = 'CustomerClaimMultiParcel',
    CustomerSendNotLabeledParcel = 'CustomerSendNotLabeledParcel',
    CustomerSendPrelabeledParcel = 'CustomerSendPrelabeledParcel',
    CustomerSendPrelabeledLaundry = 'CustomerSendPrelabeledLaundry',
    CustomerClaimParcel = 'CustomerClaimParcel',
    CustomerReturnParcel = 'CustomerReturnParcel',
    CustomerCollectParcel = 'CustomerCollectParcel',
    CustomerCollectGift = 'CustomerCollectGift',
    Idle = 'Idle',
    CourierMode = 'CourierMode',
    OperatorLogin = 'OperatorLogin',
    ServiceMode = 'ServiceMode',
    Unknown = 'Unknown',
    DiagnosticFiles = 'DiagnosticFiles',
    DVRMonitor = 'DVRMonitor',
    InspectionCompartments = 'InspectionCompartments',
    CustomerQuickSendParcel = 'CustomerQuickSendParcel',
}

export enum FunctionalityErrorRequestErrorCode {
    EventCannotBeSentToCentralSystem = 'EventCannotBeSentToCentralSystem',
    FatalErrorLogExists = 'FatalErrorLogExists',
    PresenceSensorUnknown = 'PresenceSensorUnknown',
    PresenceSensorTimeOut = 'PresenceSensorTimeOut',
    ReceivedEventAboutUnknownParcel = 'ReceivedEventAboutUnknownParcel',
    DVRError = 'DVRError',
    SendLimitExceeded = 'SendLimitExceeded',
    DamagedCompartment = 'DamagedCompartment',
    AffectedCompartment = 'AffectedCompartment',
    ParcelDoesNotExistInCentralSystem = 'ParcelDoesNotExistInCentralSystem',
    PaymentFault = 'PaymentFault',
    NotAdjustedCompartment = 'NotAdjustedCompartment',
    PresenceSensorWarning = 'PresenceSensorWarning',
    PresenceSensorCritical = 'PresenceSensorCritical',
    IRError = 'IRError',
    IRCalibrationError = 'IRCalibrationError',
    LastTransactionRestored = 'LastTransactionRestored',
    NoFreeCompartments = 'NoFreeCompartments',
    InvalidBoxMachineState = 'InvalidBoxMachineState',
    InspectionCompartment = 'InspectionCompartment',
    RescanParcel = 'RescanParcel',
    ReopenCompartment = 'ReopenCompartment',
    Timeout = 'Timeout',
    AlreadyOpenedCompartment = 'AlreadyOpenedCompartment',
    InvalidPreferredBoxMachine = 'InvalidPreferredBoxMachine',
}

export enum BoxAlternativeFunction {
    ServiceBox = 'serviceBox',
    ControllerBox = 'controllerBox',
}

export enum ComponentType {
    HAL = 'HAL',
    ShockSensor = 'ShockSensor',
    PowerSensor = 'PowerSensor',
    OverVoltageSensor = 'OverVoltageSensor',
    FloodSensor = 'FloodSensor',
    DoorSensorL = 'DoorSensorL',
    Fan = 'Fan',
    Heater = 'Heater',
    Siren = 'Siren',
    T = 'T',
    U = 'U',
    H = 'H',
    P = 'P',
    I = 'I',
    Dvr = 'Dvr',
    IWR724 = 'IWR724',
    BoxMachine = 'BoxMachine',
    NotAuthorizedOpenedCompartments = 'NotAuthorizedOpenedCompartments',
    Expander = 'Expander',
    BarcodeScanner = 'BarcodeScanner',
    LCD = 'LCD',
    Logger = 'Logger',
    Other = 'Other',
    Plc = 'Plc',
    PlcRobot = 'PlcRobot',
    UpsBattery = 'UpsBattery',
    PlcRobotCart = 'PlcRobotCart',
    Ax1Servo = 'Ax1Servo',
    Ax2Servo = 'Ax2Servo',
    BatteryStatus = 'BatteryStatus',
    ExternalPowerOk = 'ExternalPowerOk',
    BatterySlot1 = 'BatterySlot1',
    BatterySlot2 = 'BatterySlot2',
    BatterySlot3 = 'BatterySlot3',
}

export enum CompartmentState {
    Soiled = 'soiled',
    NotSoiled = 'notSoiled',
    Damaged = 'damaged',
    NotDamaged = 'notDamaged',
    Inspection = 'inspection',
    NotInspection = 'notInspection',
    Verified = 'verified',
    NotVerified = 'notVerified',
}

export enum InboundCompartmentState {
    Empty = '',
    Damaged = 'damaged',
    NotDamaged = 'notDamaged',
    Dirty = 'dirty',
    NotDirty = 'notDirty',
    Inspection = 'inspection',
    NotInspection = 'notInspection',
}

export enum CompartmentSize {
    A = 'A',
    B = 'B',
    C = 'C',
    D = 'D',
}

export enum ChangeSizeProcess {
    ChangeSizeAfterOpen = 'ChangeSizeAfterOpen',
    ChangeSizeAfterOpenIcra = 'ChangeSizeAfterOpenIcra',
    ChangeSizeBeforeOpen = 'ChangeSizeBeforeOpen',
    ChangeSizeExcludeParcelSize = 'ChangeSizeExcludeParcelSize',
    ChangeSizeExcludeParcelSizeIcra = 'ChangeSizeExcludeParcelSizeIcra',
}
export enum BoxMachineZone {
    MultiCompartment = 'multiCompartment',
    EasyAccess = 'easyAccess',
}

export enum SessionType {
    Courier = 'Courier',
    Service = 'Service',
    Customer = 'Customer',
    StandBy = 'StandBy',
    Escort = 'Escort',
    Webpanel = 'Webpanel',
    Webservice = 'Webservice',
}

export enum BoxMachineParcelStatus {
    NONE = 'NONE',
    CLAIMED = 'CLAIMED',
    CANCELED = 'CANCELED',
    DELIVERED = 'DELIVERED',
    NOT_AVAILABLE = 'NOT_AVAILABLE',
    PLACED_BY_COURIER = 'PLACED_BY_COURIER',
    COURIER_DELIVERING = 'COURIER_DELIVERING',
    PLACED_BY_CUSTOMER = 'PLACED_BY_CUSTOMER',
    TAKEN_BY_COURIER = 'TAKEN_BY_COURIER',
    MISSING_PARCEL = 'MISSING_PARCEL',
    NOT_READABLE = 'NOT_READABLE',
    EMERGENCY_DELIVERY = 'EMERGENCY_DELIVERY',
    EXPIRED = 'EXPIRED',
    CUSTOMER_DELIVERING = 'CUSTOMER_DELIVERING',
    LABEL_EXPIRED = 'LABEL_EXPIRED',
    OVERSIZED = 'OVERSIZED',
    STORED = 'STORED',
}

export enum BoxSize {
    A = 'A',
    B = 'B',
    C = 'C',
    D = 'D',
}

export interface GlobalResponseModel {
    payload?: any;
    exceptionMessage?: string;
    exceptionKey?: string;
    status: GlobalResponseModelStatus;
    iid?: string;
}

export interface GlobalResponseModelUserRolesPayload {
    payload?: UserRolesDeltaResponse;
    exceptionMessage?: string;
    exceptionKey?: string;
    status: GlobalResponseModelStatus;
    iid?: string;
}

export interface UserRolesDeltaResponse {
    timestamp: string;
    changed: UserRoleVO[];
    deleted: UserRoleVO[];
}

export interface UserRoleVO {
    id: number;
    name: string;
    mask: number;
}

export interface GlobalResponseModelCouriersDeletedPayload {
    payload: CourierDeletedResultResponse;
    exceptionMessage: string;
    exceptionKey: string;
    status: GlobalResponseModelStatus;
    iid: string;
}

export interface CourierDeletedResultResponse {
    count: number;
    result: CourierLogin[];
}

export interface CourierLogin {
    login: string;
}

export interface GlobalResponseModelCouriersChangedPayload {
    payload?: CourierChangedResultResponse;
    exceptionMessage?: string;
    exceptionKey?: string;
    status: GlobalResponseModelStatus;
    iid?: string;
}

export interface CourierChangedResultResponse {
    count: number;
    result: CourierResponse[];
}

export interface CourierResponse {
    id: number;
    documentNumber: string;
    login: string;
    password: string;
    userRole: string;
    carrier: string;
    active: boolean;
    startDate: string;
    endDate: string;
}

export interface GlobalResponseModelWithCourier {
    payload: CourierResponse;
    exceptionMessage: string;
    exceptionKey: string;
    status: GlobalResponseModelStatus;
    iid: string;
}

export interface GlobalResponseModelUknownParcelFaultMessage {
    payload: FaultMessage;
    exceptionMessage: string;
    exceptionKey: string;
    status: GlobalResponseModelStatus;
    iid: string;
}

export interface FaultMessage {
    faultMessage: string;
}

export interface GlobalResponseModelPackExists {
    payload?: boolean;
    exceptionMessage: string;
    exceptionKey: string;
    status: GlobalResponseModelStatus;
    iid: string;
}

export interface GlobalResponseModelWithParcel {
    payload?: ParcelResponse;
    exceptionMessage?: string;
    exceptionKey?: string;
    status: GlobalResponseModelStatus;
    iid?: string;
}

export interface GlobalResponseModelWithReverseReturnParcel {
    payload?: ReverseReturnParcelResponse;
    exceptionMessage?: string;
    exceptionKey?: string;
    status: GlobalResponseModelStatus;
    iid?: string;
}

export interface BoxMachineExtendedLayoutPageResultResponse {
    count: number;
    result: BoxMachineExtendedLayoutResponse[];
}

export interface GlobalResponseModelWithBoxMachineInfo {
    payload: BoxMachineInfoResponse;
    exceptionMessage: string;
    exceptionKey: string;
    status: GlobalResponseModelStatus;
    iid: string;
}

export interface GlobalResponseModelWithConfigurationParameters {
    payload: BoxMachineConfigurationParameterResponse;
    exceptionMessage: string;
    exceptionKey: string;
    status: GlobalResponseModelStatus;
    iid: string;
}

export interface FunctionalityErrorRequest {
    functionalityContext: string;
    errorCode: FunctionalityErrorRequestErrorCode;
    sessionType: string;
    timestamp: string;
    boxMachineParcelStatus?: string;
    parcelCode?: string;
    courierDocumentNr?: string;
    errorDescription?: string;
    compartment?: string;
}

export interface UserRolesDeltaRequest {
    timestamp?: string;
}

export interface CouriersDeltaPageRequest {
    timestamp?: string;
    paging: PagingVO;
}

export interface PagingVO {
    limit: number;
    offset: number;
}

export interface Box {
    size?: string;
    subBoxNr?: string;
    alternativeFunction?: string;
}

export interface BoxMachineLayout {
    column: string;
    moduleId: string;
    boxes: Box[];
}

export interface MachineLayoutRequest {
    boxMachineLayout: BoxMachineLayout[];
}

export interface MassiveCompartmentOpenAlertRequest {
    timestamp: string;
    openedCompartments: number;
    maximumCompartments: number;
}

export interface ComponentStatus {
    component: string;
    details: string;
    code: string;
    timestamp: string;
    sessionType: string;
    version: string;
}

export interface ReportStatusRequest {
    componentStatuses: ComponentStatus[];
}

export interface ReportCompartmentStates {
    timestamp: string;
    sessionType: string;
    state: string;
    names: string[];
}

export interface AvailableSizeVO {
    size: string;
}

export interface ChangeParcelStatusRequest {
    parcelCode: string[];
    multiCompartmentUuid?: string;
    multiCompartmentSize?: string;
    newParcelCode?: string;
    status: string;
    boxName?: string;
    timestamp?: string;
    courierDocumentNr?: string;
    claimReasonId?: number;
    messageSeq?: string;
    boxSize?: string;
    customerOperationDuration?: number;
    parcelAttributes?: ParcelAttributeVO[];
    changeSizeProcess?: string;
    changeSizeDate?: string;
    availableSizes?: AvailableSizeVO[];
    boxMachineZones?: string[];
    actionSource?: string;
}

export interface ParcelAttributeVO {
    name: string;
    value: string;
}

export interface UnknownParcelFaultyMessage {
    phoneNumber: string;
    openCode: string;
    language: string;
}

export interface PackExistsRequest {
    packCode: string;
}

export interface GetParcelByQuickSendCodeRequest {
    quickSendCode: number;
}

export interface BoxMachineExtendedLayoutPageRequest {
    paging: PagingVO;
}

export interface CreateReverseReturnParcelFromBoxMachineRequest {
    code: number;
    directParcel: boolean;
}

export interface CheckPrelabeledParcelRequest {
    packCode: string;
    operationType: string;
    courierLogin?: string;
}

export interface ChangeBoxSizeNameRequest {
    packCode: string;
    boxSize: string;
    boxName: string;
    changeSizeProcess: string;
    changeSizeDate: string;
    availableSizes: AvailableSizeVO[];
}

export interface BackOfficeRequest {
    packCode: string;
    status: string;
    boxSize: string;
    newPackCode: string;
    messageSeq: string;
    boxName: string;
    courierDocumentNr: string;
    customerOperationDuration: string;
    timestamp: string;
    changeSizeProcess: string;
    changeSizeDate: string;
    availableSizes: AvailableSizeVO[];
}

export interface ParcelRequest {
    boxMachine?: string;
    carrier?: string;
    carrierReference?: string;
    customerPhone?: string;
    directParcel?: boolean;
    endOfWeekCollection?: boolean;
    multiCompPotentialSize?: string;
    multiCompPotentialUuid?: string;
    onDeliveryAmount?: number;
    openCode?: number;
    parcelAttributes?: ParcelAttributeVO[];
    parcelCode?: string;
    payCode?: number;
    pickupWithoutLabel?: boolean;
    senderBoxMachine?: string;
    size?: BoxSize;
    status?: BoxMachineParcelStatus;
}

export interface ParcelResponse {
    customerPhone: string;
    senderPhone: string;
    boxMachine: string;
    size: string;
    onDeliveryAmount: number;
    customerReference?: string;
    parcelCode: string;
    automaticReturn: boolean;
    returnAddressVO?: DeliveryPackAddress;
    targetAddressVO: DeliveryPackAddress;
    bussinesCustomer: boolean;
    directParcel: boolean;
    payCode: number;
    openCode: number;
    returnCode: number;
    carrier: string;
    carrierReference?: string;
    customerDeliveringCode: number;
    id: number;
    status: string;
    payForReturn: boolean;
    personalID?: string;
    personalID2?: string;
    supplierPhone?: string;
    supplier: string;
    providerPhone?: string;
    provider: string;
    customerSpecialReq?: number;
    multipleParcelsId?: string;
    multipleParcelsCount?: number;
    initialPackCode?: string;
    pickupWithoutLabel: boolean;
    collectionBarcode?: string;
    sendingAmount: SendingAmount[];
    chargeForSending?: boolean;
    carrierAcquirerPayCode?: string;
    serviceName: string;
    reservationDataVO?: any;
    timestamp: string;
    parcelAttributes: ParcelAttributeVO[];
    multiCompPotentialUuid?: string;
    multiCompPotentialSize?: string;
    endOfWeekCollection: boolean;
    availablePM: boolean;
    senderBoxMachine?: string;
}

export interface DeliveryPackAddress {
    buildingNo?: string;
    buldingNo?: string;
    companyName?: string;
    countryCode?: string;
    email?: string;
    flatNo?: string;
    name?: string;
    phoneNum?: string;
    province?: string;
    street?: string;
    surName?: string;
    town?: string;
    zipCode?: string;
}

export interface ReverseReturnParcelResponse {
    parcelCode: string;
    boxMachine: string;
    openCode: number;
    customerPhone: string;
    size: string;
    targetAddress: DeliveryPackAddress;
    id: number;
    carrier?: string;
    personalID2?: string;
    serviceName: string;
    carrierAcquirerPayCode?: string;
    timestamp: string;
    directParcel: boolean;
    pickupWithoutLabel?: boolean;
}

export interface SendingAmount {
    size: string;
    amount: number;
}

export interface BoxMachineExtendedLayoutResponse {
    compartment: string;
    moduleId: string;
    parcelDetails: BoxRequestType;
    courierDocumentNumber: string;
    collectionBarcode: string;
}

export interface BoxRequestType {
    size: string;
    customerPhone: string;
    onDeliveryAmount: number;
    openCode: string;
    parcelCode: string;
    payCode: string;
    customerDeliveringCode: string;
    status: string;
    senderPhone: string;
    boxMachine: string;
    senderBoxMachine: string;
    customerReference?: string;
    automaticReturn: number;
    returnAddressVo: DeliveryPackAddress;
    targetAddressVo: DeliveryPackAddress;
    bussinessCustomer: number;
    directParcel: number;
    returnCode: string;
    personalID?: string;
    supplierPhone?: string;
    supplier: string;
    providerPhone?: string;
    provider: string;
    customerSpecialReq: string;
    parcelId: number;
    carrier: string;
    carrierReference: string;
    multipleParcelsId?: string;
    multipleParcelsCount?: number;
    initialPackCode?: string;
    pickupWithoutLabel: boolean;
    collectionBarcode: string;
    sendingAmountsVo: SendingAmount[];
    chargeForSending: number;
    carrierAcquirerPayCode: string;
    personalID2?: string;
    serviceName: string;
    reservationDataVo?: any;
    timestamp: string;
    parcelAttributes: ParcelAttributeVO[];
    multiCompPotentialUuid?: string;
    multiCompPotentialSize?: string;
    multiCompartmentUuid: string;
    endOfWeekCollection: boolean;
}

export interface BoxMachineInfoResponse {
    machineStatus: string;
    timezoneOffset: number;
    boxMachineAttributes: BoxMachineAttribute[];
    powerSupplyType: string;
}

export interface BoxMachineAttribute {
    name: string;
    value: string;
}

export interface BoxMachineConfigurationParameterResponse {
    boxMachineConfigurationParameters: BoxMachineConfigurationParameter[];
}

export interface BoxMachineConfigurationParameter {
    name: string;
    value: string;
}

export interface CompartmentInfo {
    isBusy?: boolean;
    isInspectioned?: boolean;
    isOpened?: boolean;
    isSoiled?: boolean;
    isVerified?: boolean;
    name: string;
    parcelInfo: ParcelInfo[];
    parcelStationColumn?: string;
    parcelStationColumnName?: string;
    parcelStationSite?: string;
    size: BoxSize;
    state: string;
    stateDetails?: string;
    usedDate?: string;
}

export interface ParcelInfo {
    code: string;
    initialCode?: string;
    size?: BoxSize;
    state: BoxMachineParcelStatus;
}

export interface QRCodeParcelRequest {
    qrCode: string;
    boxMachineName: string;
}

export interface QRCodeParcelResponse {
    parcelID: number; // 'id' field from SZOP BackOfficeParcelService#checkPrelabeledParcel, parcelID is required in APM v2.0, deprecated in APM v3.0
    timestamp: string; // SZOP request processing time, in ISO8601 in format "2011-12-03T10:15:30.123+01:00"
    parcelCode: string; // 26 digit long parcel code
    parcelSize: string; // 'dpt' field from SZOP BackOfficeParcelService#checkPrelabeledParcel
    parcelStatus: string; // Example: CUSTOMER_DELIVERING
    openCode: number; // Example: 123456
    payCode: number; // Example: 789012
    onDeliveryAmount: string; // Example: "10.11"
    customerPhone: string; // 'phoneNum' field from SZOP BackOfficeParcelService#checkPrelabeledParcel, Example: "+48777000111"
    boxMachineName?: string; // Example: "WAW35M"
    senderBoxMachineName?: string; // Example: "KRA12A"
    carrierName: string; // Example: "APMX"
    carrierReference?: string;
    serviceName: string; // Example: "STANDARD_COURIER"
    multiCompartmentPotentialUuid?: string; // UUID format
    multiCompartmentPotentialSize?: string;
    multiCompartmentUuid?: string; // UUID format, Currently SZOP BackOfficeParcelService#checkPrelabeledParcel does not return multiCompartmentUuid, as parcel is in CUSTOMER_DELIVERING it is not part of multi-compartment yet, returns always null.
    pickupWithoutLabel: boolean;
    endOfWeekCollection: boolean;
    availablePM: boolean; // Returns if parcel is requested to easy access zone
    parcelAttributes: ParcelAttributeVO[];
}

export interface QRCodeErrorResponse {
    exceptionMessage?: string;
    exceptionKey?: string;
}

export interface LabelAssociation {
    partnerRentId: string;
    label: string;
}
