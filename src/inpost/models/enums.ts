export enum MultiCompartmentSize {
    A = 'A',
    B = 'B',
    C = 'C',
    D = 'D',
    E = 'E',
    F = 'F',
    G = 'G',
    H = 'H',
    I = 'I',
    J = 'J',
    L = 'L',
    M = 'M',
    N = 'N',
    O = 'O',
}

export enum ActionSource {
    PANEL = 'PANEL',
    QR_CODE = 'QR_CODE',
    MOBILE_APP = 'MOBILE_APP',
    WEB_SERVICE = 'WEB_SERVICE',
}

export enum ParcelStatus {
    COURIER_DELIVERING = 'COURIER_DELIVERING',
    PLACED_BY_CUSTOMER = 'PLACED_BY_CUSTOMER',
    PLACED_BY_COURIER = 'PLACED_BY_COURIER',
    DELIVERED = 'DELIVERED',
    EXPIRED = 'EXPIRED',
    NOT_AVAILABLE = 'NOT_AVAILABLE',
    CANCELED = 'CANCELED',
    CUSTOMER_DELIVERING = 'CUSTOMER_DELIVERING',
    CLAIMED = 'CLAIMED',
    TAKEN_BY_COURIER = 'TAKEN_BY_COURIER',
    LABEL_EXPIRED = 'LABEL_EXPIRED',
    MISSING_PARCEL = 'MISSING_PARCEL',
    NOT_READABLE = 'NOT_READABLE',
    EMERGENCY_DELIVERY = 'EMERGENCY_DELIVERY',
    OVERSIZED = 'OVERSIZED',
}

export enum ParcelOperationType {
    PICKUP = 'PICKUP',
    RETURN = 'RETURN',
    COURIER = 'COURIER',
    EMERGENCY_DELIVERY = 'EMERGENCY_DELIVERY',
    DELIVER = 'DELIVER',
    DROPOFF = 'DROPOFF',
    COLLECT = 'COLLECT',
}

export enum CountryCode {
    GB = 'GB',
    FR = 'FR',
}

export enum CompartmentInfoState {
    EFFICIENT = 'Efficient',
    BROKEN = 'Broken',
}
