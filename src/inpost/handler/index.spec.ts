import { InpostHandlerImpl } from '.';
import { Blo<PERSON><PERSON>ocker } from '../../bloqit/bloqs/models';
import { LockerType, LockerUsageType } from '../../bloqit/lockers/models';
import {
    Roles,
    FunctionalityContext as BloqFunctionalityContext,
    OperationType,
    UIFlowContext,
} from '../../bloqit/rents/models/rent';
import { FakeBloqitClient } from '../../bloqit/sdk/fake';
import { FakeLogger } from '../../core/logger/fake';
import { FakeInPostApiClient } from '../client/fake';
import { FakeLabelAssociationRepository } from '../data-access/repositories/associate-label/fake/fake-label-association-repository';
import { FakeMappedLockerRepository } from '../data-access/repositories/mapped-locker/fake/fake-mapped-locker-repository';
import {
    BoxSize,
    CompartmentState,
    FunctionalityContext,
    FunctionalityErrorRequestErrorCode,
    GlobalResponseModelStatus,
    GlobalResponseModelWithParcel,
    GlobalResponseModelWithReverseReturnParcel,
    QRCodeErrorResponse,
    QRCodeParcelResponse,
} from '../models';
import { ParcelOperationType, ParcelStatus } from '../models/enums';

describe('InPostHandler', () => {
    const bloqitRentId = 'bloqit-rent-33';
    const partnerRentId = 'partner-rent1';
    const partnerLockerId = 'partner-locker1';
    const rentProductType = 'rent-product-type1';
    const bloqitBloqId = 'bloq-id-22';
    const partnerBloqId = 'partner-bloq-id-22';
    const bloqitLockerId = 'bloqit-locker-id-11';
    const accessToken = 'inpost-access-token';
    const bloqitPartnerId = 'partner-id-123';
    const labelAssociationRepository = new FakeLabelAssociationRepository();
    const mappedLockerRepository = new FakeMappedLockerRepository();

    describe('notifyDropOffConfirmation', () => {
        beforeEach(() => {
            jest.clearAllMocks();
        });

        const baseEventData = {
            rent: bloqitRentId,
            bloq: bloqitBloqId,
            locker: bloqitLockerId,
            customer: { email: '<EMAIL>' }, // We don't have a customer for this event at the core
            message: 'Successful drop off for rent bloqit-rent-1234',
            timestamp: new Date(),
            metadata: {
                externalID: partnerRentId,
                bloqExternalID: partnerBloqId,
                rentMetadata: { productType: rentProductType },
                country: 'UK',
                lockerExternalID: 'locker-external-id-123',
                lockerSize: 'M',
            },
        };

        it('should notify a drop off confirmation done by customer', async () => {
            const logger = new FakeLogger();
            const inpostClient = new FakeInPostApiClient();
            const bloqitClient = new FakeBloqitClient();

            jest.spyOn(inpostClient, 'changeParcelStatus').mockResolvedValueOnce({
                payload: '',
                exceptionMessage: '',
                exceptionKey: '',
                status: GlobalResponseModelStatus.OK,
                iid: '',
            });

            jest.spyOn(inpostClient, 'getAccessToken').mockResolvedValueOnce({
                access_token: accessToken,
                expires_in: 10000,
                refresh_expires_in: 3000,
                token_type: 'jwt',
                'not-before-policy': 0,
                scope: 'bloqit',
            });

            jest.spyOn(mappedLockerRepository, 'retrieveBySharedId').mockResolvedValueOnce({
                bloqId: bloqitBloqId,
                sharedId: partnerLockerId,
                layout: [
                    {
                        column: 1,
                        row: 1,
                        lockerId: 'locker-id',
                        lockerTitle: 'locker-title',
                        partnerName: '1R1',
                        size: LockerType.M,
                    },
                ],
            });

            const handler = new InpostHandlerImpl({
                logger,
                inpostClient,
                bloqitClient,
                labelAssociationRepository,
                mappedLockerRepository,
            });

            await handler.notifyDropOffConfirmation({
                ...baseEventData,
                actionInitiatedBy: { role: Roles.CUSTOMER, id: 'customer-id-123' },
            });

            expect(inpostClient.changeParcelStatus).toHaveBeenCalledTimes(1);
            expect(inpostClient.changeParcelStatus).toHaveBeenCalledWith(
                expect.objectContaining({ token: accessToken }),
                partnerBloqId,
                'UK',
                expect.objectContaining({ status: ParcelStatus.PLACED_BY_CUSTOMER }),
            );
        });

        it('should notify a drop off confirmation done by courier', async () => {
            const logger = new FakeLogger();
            const inpostClient = new FakeInPostApiClient();
            const bloqitClient = new FakeBloqitClient();

            jest.spyOn(inpostClient, 'changeParcelStatus').mockResolvedValueOnce({
                payload: '',
                exceptionMessage: '',
                exceptionKey: '',
                status: GlobalResponseModelStatus.OK,
                iid: '',
            });

            jest.spyOn(inpostClient, 'getAccessToken').mockResolvedValueOnce({
                access_token: accessToken,
                expires_in: 10000,
                refresh_expires_in: 3000,
                token_type: 'jwt',
                'not-before-policy': 0,
                scope: 'bloqit',
            });

            jest.spyOn(mappedLockerRepository, 'retrieveBySharedId').mockResolvedValueOnce({
                bloqId: bloqitBloqId,
                sharedId: partnerLockerId,
                layout: [
                    {
                        column: 1,
                        row: 1,
                        lockerId: 'locker-id',
                        lockerTitle: 'locker-title',
                        partnerName: '1R1',
                        size: LockerType.M,
                    },
                ],
            });

            const handler = new InpostHandlerImpl({
                logger,
                inpostClient,
                bloqitClient,
                labelAssociationRepository,
                mappedLockerRepository,
            });

            await handler.notifyDropOffConfirmation({
                ...baseEventData,
                actionInitiatedBy: { role: Roles.COURIER, id: 'courier-id-123' },
            });

            expect(inpostClient.changeParcelStatus).toHaveBeenCalledTimes(1);
            expect(inpostClient.changeParcelStatus).toHaveBeenCalledWith(
                expect.objectContaining({ token: accessToken }),
                partnerBloqId,
                'UK',
                expect.objectContaining({ status: ParcelStatus.PLACED_BY_COURIER }),
            );
        });
    });

    describe('handleRentFinishedEvent', () => {
        beforeEach(() => {
            jest.clearAllMocks();
        });

        const baseEventData = {
            rent: bloqitRentId,
            bloq: bloqitBloqId,
            locker: bloqitLockerId,
            customer: { email: '<EMAIL>' },
            timestamp: new Date(),
        };

        it('should notify a missing parcel', async () => {
            const logger = new FakeLogger();
            const inpostClient = new FakeInPostApiClient();
            const bloqitClient = new FakeBloqitClient();

            jest.spyOn(inpostClient, 'changeParcelStatus').mockResolvedValueOnce({
                payload: {},
                exceptionMessage: '',
                exceptionKey: '',
                status: GlobalResponseModelStatus.OK,
                iid: '',
            });

            jest.spyOn(inpostClient, 'reportBoxMachineFunctionalityError').mockResolvedValueOnce({
                payload: {},
                exceptionMessage: '',
                exceptionKey: '',
                status: GlobalResponseModelStatus.OK,
                iid: '',
            });

            jest.spyOn(inpostClient, 'getAccessToken').mockResolvedValueOnce({
                access_token: accessToken,
                expires_in: 10000,
                refresh_expires_in: 3000,
                token_type: 'jwt',
                'not-before-policy': 0,
                scope: 'bloqit',
            });

            jest.spyOn(mappedLockerRepository, 'retrieveBySharedId').mockResolvedValue({
                bloqId: bloqitBloqId,
                sharedId: partnerLockerId,
                layout: [
                    {
                        column: 1,
                        row: 1,
                        lockerId: 'locker-id',
                        lockerTitle: 'locker-title',
                        partnerName: '1R1',
                        size: LockerType.M,
                    },
                ],
            });

            const handler = new InpostHandlerImpl({
                logger,
                inpostClient,
                bloqitClient,
                labelAssociationRepository,
                mappedLockerRepository,
            });

            await handler.handleRentFinishedEvent({
                ...baseEventData,
                actionInitiatedBy: { role: Roles.CUSTOMER, id: 'customer-id-123' },
                description: 'Parcel reported as missing',
                metadata: {
                    country: 'UK',
                    externalID: partnerRentId,
                    bloqExternalID: partnerBloqId,
                    pickUpInfo: {
                        errorReasons: ['missing_parcel'],
                    },
                    lockerExternalID: 'locker-external-id-123',
                    lockerSize: 'M',
                },
            });

            expect(inpostClient.changeParcelStatus).toHaveBeenCalledTimes(1);
            expect(inpostClient.changeParcelStatus).toHaveBeenCalledWith(
                expect.objectContaining({ token: accessToken }),
                partnerBloqId,
                'UK',
                expect.objectContaining({ status: ParcelStatus.MISSING_PARCEL }),
            );
            expect(inpostClient.reportBoxMachineFunctionalityError).toHaveBeenCalledTimes(1);
        });

        it('should handle a rent finished by courier in emergency mode', async () => {
            const logger = new FakeLogger();
            const inpostClient = new FakeInPostApiClient();
            const bloqitClient = new FakeBloqitClient();

            jest.spyOn(inpostClient, 'changeParcelStatus').mockResolvedValueOnce({
                payload: {},
                status: GlobalResponseModelStatus.OK,
            });

            jest.spyOn(inpostClient, 'checkPrelabeledParcel').mockResolvedValueOnce({
                payload: {},
                status: GlobalResponseModelStatus.OK_WITH_RESPONSE,
            });

            jest.spyOn(inpostClient, 'getAccessToken').mockResolvedValueOnce({
                access_token: accessToken,
                expires_in: 10000,
                refresh_expires_in: 3000,
                token_type: 'jwt',
                'not-before-policy': 0,
                scope: 'bloqit',
            });

            jest.spyOn(mappedLockerRepository, 'retrieveBySharedId').mockResolvedValueOnce({
                bloqId: bloqitBloqId,
                sharedId: partnerLockerId,
                layout: [
                    {
                        column: 1,
                        row: 1,
                        lockerId: 'locker-id',
                        lockerTitle: 'locker-title',
                        partnerName: '1R1',
                        size: LockerType.M,
                    },
                ],
            });
            const handler = new InpostHandlerImpl({
                logger,
                inpostClient,
                bloqitClient,
                labelAssociationRepository,
                mappedLockerRepository,
            });

            await handler.handleRentFinishedEvent({
                ...baseEventData,
                actionInitiatedBy: { role: Roles.COURIER, id: 'customer-id-123' },
                functionalityContext: BloqFunctionalityContext.EmergencyTakeoutFlow,
                description: 'Emergency takeout flow',
                metadata: {
                    country: 'UK',
                    externalID: partnerRentId,
                    bloqExternalID: partnerBloqId,
                    pickUpInfo: {
                        success: true,
                    },
                    lockerExternalID: 'locker-external-id-123',
                    lockerSize: 'M',
                },
            });

            expect(inpostClient.changeParcelStatus).toHaveBeenCalledTimes(1);
            expect(inpostClient.checkPrelabeledParcel).toHaveBeenCalledTimes(1);
            expect(inpostClient.changeParcelStatus).toHaveBeenCalledWith(
                expect.objectContaining({ token: accessToken }),
                partnerBloqId,
                'UK',
                expect.objectContaining({ status: ParcelStatus.EMERGENCY_DELIVERY }),
            );
            expect(inpostClient.checkPrelabeledParcel).toHaveBeenCalledWith(
                expect.objectContaining({ token: accessToken }),
                partnerBloqId,
                'UK',
                expect.objectContaining({ operationType: ParcelOperationType.EMERGENCY_DELIVERY }),
            );
        });

        it('should handle a rent finished by courier in the case of success', async () => {
            const logger = new FakeLogger();
            const inpostClient = new FakeInPostApiClient();
            const bloqitClient = new FakeBloqitClient();
            const fakeLabelRepository = new FakeLabelAssociationRepository();

            jest.spyOn(fakeLabelRepository, 'retrieve').mockResolvedValueOnce(undefined);

            jest.spyOn(inpostClient, 'changeParcelStatus').mockResolvedValueOnce({
                payload: {},
                exceptionMessage: '',
                exceptionKey: '',
                status: GlobalResponseModelStatus.OK,
                iid: '',
            });

            jest.spyOn(inpostClient, 'getAccessToken').mockResolvedValueOnce({
                access_token: accessToken,
                expires_in: 10000,
                refresh_expires_in: 3000,
                token_type: 'jwt',
                'not-before-policy': 0,
                scope: 'bloqit',
            });

            jest.spyOn(mappedLockerRepository, 'retrieveBySharedId').mockResolvedValueOnce({
                bloqId: bloqitBloqId,
                sharedId: partnerLockerId,
                layout: [
                    {
                        column: 1,
                        row: 1,
                        lockerId: 'locker-id',
                        lockerTitle: 'locker-title',
                        partnerName: '1R1',
                        size: LockerType.M,
                    },
                ],
            });
            const handler = new InpostHandlerImpl({
                logger,
                inpostClient,
                bloqitClient,
                labelAssociationRepository: fakeLabelRepository,
                mappedLockerRepository,
            });

            await handler.handleRentFinishedEvent({
                ...baseEventData,
                actionInitiatedBy: { role: Roles.COURIER, id: 'customer-id-123' },
                description: 'Rent has transited to the FINISHED state',
                metadata: {
                    country: 'UK',
                    externalID: partnerRentId,
                    bloqExternalID: partnerBloqId,
                    pickUpInfo: {
                        success: true,
                    },
                    lockerExternalID: 'locker-external-id-123',
                    lockerSize: 'M',
                },
            });

            expect(inpostClient.changeParcelStatus).toHaveBeenCalledTimes(1);
            expect(inpostClient.changeParcelStatus).toHaveBeenCalledWith(
                expect.objectContaining({ token: accessToken }),
                partnerBloqId,
                'UK',
                expect.objectContaining({ status: ParcelStatus.TAKEN_BY_COURIER }),
            );
            expect(inpostClient.reportBoxMachineFunctionalityError).toHaveBeenCalledTimes(0);
        });

        it('should handle a expired rent finished by courier in the case of success', async () => {
            const logger = new FakeLogger();
            const inpostClient = new FakeInPostApiClient();
            const bloqitClient = new FakeBloqitClient();
            const fakeLabelRepository = new FakeLabelAssociationRepository();

            jest.spyOn(fakeLabelRepository, 'retrieve').mockResolvedValueOnce(undefined);

            jest.spyOn(inpostClient, 'changeParcelStatus').mockResolvedValueOnce({
                payload: {},
                exceptionMessage: '',
                exceptionKey: '',
                status: GlobalResponseModelStatus.OK,
                iid: '',
            });

            jest.spyOn(inpostClient, 'getAccessToken').mockResolvedValueOnce({
                access_token: accessToken,
                expires_in: 10000,
                refresh_expires_in: 3000,
                token_type: 'jwt',
                'not-before-policy': 0,
                scope: 'bloqit',
            });

            jest.spyOn(mappedLockerRepository, 'retrieveBySharedId').mockResolvedValueOnce({
                bloqId: bloqitBloqId,
                sharedId: partnerLockerId,
                layout: [
                    {
                        column: 1,
                        row: 1,
                        lockerId: 'locker-id',
                        lockerTitle: 'locker-title',
                        partnerName: '1R1',
                        size: LockerType.M,
                    },
                ],
            });
            const handler = new InpostHandlerImpl({
                logger,
                inpostClient,
                bloqitClient,
                labelAssociationRepository: fakeLabelRepository,
                mappedLockerRepository,
            });

            await handler.handleRentFinishedEvent({
                ...baseEventData,
                actionInitiatedBy: { id: 'customer-id-123' },
                description: 'Rent has transited to the FINISHED state',
                uiFlowContext: UIFlowContext.COURIER_PICKUP_EXPIRED_PARCELS_FLOW,
                metadata: {
                    country: 'GB',
                    externalID: partnerRentId,
                    bloqExternalID: partnerBloqId,
                    pickUpInfo: {
                        success: true,
                    },
                    lockerExternalID: 'locker-external-id-123',
                    lockerSize: 'M',
                },
            });

            expect(inpostClient.changeParcelStatus).toHaveBeenCalledTimes(1);
            expect(inpostClient.changeParcelStatus).toHaveBeenCalledWith(
                expect.objectContaining({ token: accessToken }),
                partnerBloqId,
                'GB',
                expect.objectContaining({ status: ParcelStatus.TAKEN_BY_COURIER }),
            );
            expect(inpostClient.reportBoxMachineFunctionalityError).toHaveBeenCalledTimes(0);
        });

        it('should handle a LABELLESS rent finished by courier in the case of success', async () => {
            const logger = new FakeLogger();
            const inpostClient = new FakeInPostApiClient();
            const bloqitClient = new FakeBloqitClient();

            const fakeLabelRepository = new FakeLabelAssociationRepository();

            const labelCode = 'label-code-123';

            jest.spyOn(fakeLabelRepository, 'retrieve').mockResolvedValueOnce({
                partnerRentId: partnerRentId,
                label: labelCode,
            });
            jest.spyOn(fakeLabelRepository, 'remove').mockResolvedValueOnce(undefined);

            jest.spyOn(inpostClient, 'changeParcelStatus').mockResolvedValueOnce({
                payload: {},
                exceptionMessage: '',
                exceptionKey: '',
                status: GlobalResponseModelStatus.OK,
                iid: '',
            });

            jest.spyOn(inpostClient, 'getAccessToken').mockResolvedValueOnce({
                access_token: accessToken,
                expires_in: 10000,
                refresh_expires_in: 3000,
                token_type: 'jwt',
                'not-before-policy': 0,
                scope: 'bloqit',
            });

            jest.spyOn(mappedLockerRepository, 'retrieveBySharedId').mockResolvedValueOnce({
                bloqId: bloqitBloqId,
                sharedId: partnerLockerId,
                layout: [
                    {
                        column: 1,
                        row: 1,
                        lockerId: 'locker-id',
                        lockerTitle: 'locker-title',
                        partnerName: '1R1',
                        size: LockerType.M,
                    },
                ],
            });
            const handler = new InpostHandlerImpl({
                logger,
                inpostClient,
                bloqitClient,
                labelAssociationRepository: fakeLabelRepository,
                mappedLockerRepository,
            });

            await handler.handleRentFinishedEvent({
                ...baseEventData,
                actionInitiatedBy: { role: Roles.COURIER, id: 'customer-id-123' },
                description: 'Rent has transited to the FINISHED state',
                metadata: {
                    country: 'UK',
                    externalID: partnerRentId,
                    bloqExternalID: partnerBloqId,
                    pickUpInfo: {
                        success: true,
                    },
                    lockerExternalID: 'locker-external-id-123',
                    lockerSize: 'M',
                },
            });

            expect(inpostClient.changeParcelStatus).toHaveBeenCalledTimes(1);
            expect(inpostClient.changeParcelStatus).toHaveBeenCalledWith(
                expect.objectContaining({ token: accessToken }),
                partnerBloqId,
                'UK',
                expect.objectContaining({
                    newParcelCode: labelCode,
                    status: ParcelStatus.TAKEN_BY_COURIER,
                }),
            );
            expect(fakeLabelRepository.retrieve).toHaveBeenCalledTimes(1);
            expect(fakeLabelRepository.retrieve).toHaveBeenCalledWith({
                parcelCode: partnerRentId,
            });
            expect(fakeLabelRepository.remove).toHaveBeenCalledTimes(1);
            expect(fakeLabelRepository.remove).toHaveBeenCalledWith({ parcelCode: partnerRentId });

            expect(inpostClient.reportBoxMachineFunctionalityError).toHaveBeenCalledTimes(0);
        });

        it('should handle a rent finished by customer in the case of success', async () => {
            const logger = new FakeLogger();
            const inpostClient = new FakeInPostApiClient();
            const bloqitClient = new FakeBloqitClient();

            jest.spyOn(inpostClient, 'changeParcelStatus').mockResolvedValueOnce({
                payload: {},
                exceptionMessage: '',
                exceptionKey: '',
                status: GlobalResponseModelStatus.OK,
                iid: '',
            });

            jest.spyOn(inpostClient, 'getAccessToken').mockResolvedValueOnce({
                access_token: accessToken,
                expires_in: 10000,
                refresh_expires_in: 3000,
                token_type: 'jwt',
                'not-before-policy': 0,
                scope: 'bloqit',
            });

            jest.spyOn(mappedLockerRepository, 'retrieveBySharedId').mockResolvedValueOnce({
                bloqId: bloqitBloqId,
                sharedId: partnerLockerId,
                layout: [
                    {
                        column: 1,
                        row: 1,
                        lockerId: 'locker-id',
                        lockerTitle: 'locker-title',
                        partnerName: '1R1',
                        size: LockerType.M,
                    },
                ],
            });
            const handler = new InpostHandlerImpl({
                logger,
                inpostClient,
                bloqitClient,
                labelAssociationRepository,
                mappedLockerRepository,
            });

            await handler.handleRentFinishedEvent({
                ...baseEventData,
                actionInitiatedBy: { role: Roles.CUSTOMER, id: 'customer-id-123' },
                description: 'Rent has transited to the FINISHED state',
                metadata: {
                    country: 'UK',
                    externalID: partnerRentId,
                    bloqExternalID: partnerBloqId,
                    pickUpInfo: {
                        success: true,
                    },
                    lockerExternalID: 'locker-external-id-123',
                    lockerSize: 'M',
                },
            });

            expect(inpostClient.changeParcelStatus).toHaveBeenCalledTimes(1);
            expect(inpostClient.changeParcelStatus).toHaveBeenCalledWith(
                expect.objectContaining({ token: accessToken }),
                partnerBloqId,
                'UK',
                expect.objectContaining({ status: ParcelStatus.DELIVERED }),
            );
            expect(inpostClient.reportBoxMachineFunctionalityError).toHaveBeenCalledTimes(0);
        });
    });

    describe('handleRentFlowInterruptedEvent', () => {
        beforeEach(() => {
            jest.clearAllMocks();
        });

        const baseEventData = {
            rent: bloqitRentId,
            bloq: bloqitBloqId,
            locker: bloqitLockerId,
            customer: { email: '<EMAIL>' },
            functionalityContext: BloqFunctionalityContext.CustomerDropOffParcelsFlow,
            timestamp: new Date(),
            actionInitiatedBy: { role: Roles.CUSTOMER, id: 'customer-id-123' },
            description: 'Parcel reported as missing',
            metadata: {
                country: 'UK',
                externalID: partnerRentId,
                bloqExternalID: partnerBloqId,
                lockerExternalID: 'locker-external-id-123',
                lockerSize: 'M',
            },
        };

        it('should handle a workflow interrupted event by customer', async () => {
            const logger = new FakeLogger();
            const inpostClient = new FakeInPostApiClient();
            const bloqitClient = new FakeBloqitClient();

            jest.spyOn(inpostClient, 'reportBoxMachineFunctionalityError').mockResolvedValueOnce({
                payload: {},
                exceptionMessage: '',
                exceptionKey: '',
                status: GlobalResponseModelStatus.OK,
                iid: '',
            });

            jest.spyOn(inpostClient, 'getAccessToken').mockResolvedValueOnce({
                access_token: accessToken,
                expires_in: 10000,
                refresh_expires_in: 3000,
                token_type: 'jwt',
                'not-before-policy': 0,
                scope: 'bloqit',
            });

            jest.spyOn(mappedLockerRepository, 'retrieveBySharedId').mockResolvedValueOnce({
                bloqId: bloqitBloqId,
                sharedId: partnerLockerId,
                layout: [
                    {
                        column: 1,
                        row: 1,
                        lockerId: 'locker-id',
                        lockerTitle: 'locker-title',
                        partnerName: '1R1',
                        size: LockerType.M,
                    },
                ],
            });
            const handler = new InpostHandlerImpl({
                logger,
                inpostClient,
                bloqitClient,
                labelAssociationRepository,
                mappedLockerRepository,
            });

            await handler.handleRentFlowInterruptedEvent({
                ...baseEventData,
            });

            expect(inpostClient.reportBoxMachineFunctionalityError).toHaveBeenCalledTimes(1);
            expect(inpostClient.reportBoxMachineFunctionalityError).toHaveBeenCalledWith(
                expect.objectContaining({ token: accessToken }),
                partnerBloqId,
                'UK',
                expect.objectContaining({
                    errorCode: FunctionalityErrorRequestErrorCode.ParcelDoesNotExistInCentralSystem,
                    functionalityContext: FunctionalityContext.CustomerSendPrelabeledParcel,
                }),
            );
        });
    });

    describe('notifyDirtyDoor', () => {
        beforeEach(() => {
            jest.clearAllMocks();
        });

        const baseEventData = {
            rent: bloqitRentId,
            bloq: bloqitBloqId,
            locker: bloqitLockerId,
            customer: { email: '<EMAIL>' },
            message: 'Bloq reported that the door is dirty',
            timestamp: new Date(),
            metadata: {
                externalID: partnerRentId,
                bloqExternalID: partnerBloqId,
            },
        };

        it('should notify a dirty door', async () => {
            const logger = new FakeLogger();
            const inpostClient = new FakeInPostApiClient();
            const bloqitClient = new FakeBloqitClient();

            jest.spyOn(inpostClient, 'reportCompartmentStates').mockResolvedValueOnce({
                payload: '',
                exceptionMessage: '',
                exceptionKey: '',
                status: GlobalResponseModelStatus.OK,
                iid: '',
            });

            jest.spyOn(inpostClient, 'reportBoxMachineFunctionalityError').mockResolvedValueOnce({
                payload: '',
                exceptionMessage: '',
                exceptionKey: '',
                status: GlobalResponseModelStatus.OK,
                iid: '',
            });

            jest.spyOn(inpostClient, 'getAccessToken').mockResolvedValueOnce({
                access_token: accessToken,
                expires_in: 10000,
                refresh_expires_in: 3000,
                token_type: 'jwt',
                'not-before-policy': 0,
                scope: 'bloqit',
            });

            jest.spyOn(mappedLockerRepository, 'retrieveBySharedId').mockResolvedValueOnce({
                bloqId: bloqitBloqId,
                sharedId: partnerLockerId,
                layout: [
                    {
                        column: 1,
                        row: 1,
                        lockerId: 'locker-id',
                        lockerTitle: 'locker-title',
                        partnerName: '1R1',
                        size: LockerType.M,
                    },
                ],
            });
            const handler = new InpostHandlerImpl({
                logger,
                inpostClient,
                bloqitClient,
                labelAssociationRepository,
                mappedLockerRepository,
            });

            await handler.notifyDirtyDoor({
                ...baseEventData,
                actionInitiatedBy: { role: Roles.CUSTOMER, id: 'customer-id-123' },
            });

            expect(inpostClient.reportCompartmentStates).toHaveBeenCalledTimes(1);
            expect(inpostClient.reportBoxMachineFunctionalityError).toHaveBeenCalledTimes(1);
        });

        it('should notify a dirty door with CourierMode functionality context', async () => {
            const logger = new FakeLogger();
            const inpostClient = new FakeInPostApiClient();
            const bloqitClient = new FakeBloqitClient();

            jest.spyOn(inpostClient, 'reportCompartmentStates').mockResolvedValue({
                payload: '',
                exceptionMessage: '',
                exceptionKey: '',
                status: GlobalResponseModelStatus.OK,
                iid: '',
            });

            jest.spyOn(inpostClient, 'reportBoxMachineFunctionalityError').mockResolvedValue({
                payload: '',
                exceptionMessage: '',
                exceptionKey: '',
                status: GlobalResponseModelStatus.OK,
                iid: '',
            });

            jest.spyOn(mappedLockerRepository, 'retrieveBySharedId').mockResolvedValueOnce({
                bloqId: bloqitBloqId,
                sharedId: partnerLockerId,
                layout: [
                    {
                        column: 1,
                        row: 1,
                        lockerId: 'locker-id',
                        lockerTitle: 'locker-title',
                        partnerName: '1R1',
                        size: LockerType.M,
                    },
                ],
            });
            jest.spyOn(inpostClient, 'getAccessToken').mockResolvedValueOnce({
                access_token: accessToken,
                expires_in: 10000,
                refresh_expires_in: 3000,
                token_type: 'jwt',
                'not-before-policy': 0,
                scope: 'bloqit',
            });

            const handler = new InpostHandlerImpl({
                logger,
                inpostClient,
                bloqitClient,
                labelAssociationRepository,
                mappedLockerRepository,
            });

            await handler.notifyDirtyDoor({
                ...baseEventData,
                actionInitiatedBy: { role: Roles.COURIER, id: 'courier-id-123' },
                title: 'Drop off parcel',
            });

            expect(inpostClient.reportBoxMachineFunctionalityError).toHaveBeenCalledWith(
                expect.anything(),
                partnerBloqId,
                '',
                expect.objectContaining({
                    functionalityContext: FunctionalityContext.CourierMode,
                }),
            );
        });

        it('should notify a dirty door with CustomerPickupParcel functionality context', async () => {
            const logger = new FakeLogger();
            const inpostClient = new FakeInPostApiClient();
            const bloqitClient = new FakeBloqitClient();

            jest.spyOn(inpostClient, 'reportCompartmentStates').mockResolvedValue({
                payload: '',
                exceptionMessage: '',
                exceptionKey: '',
                status: GlobalResponseModelStatus.OK,
                iid: '',
            });

            jest.spyOn(inpostClient, 'reportBoxMachineFunctionalityError').mockResolvedValue({
                payload: '',
                exceptionMessage: '',
                exceptionKey: '',
                status: GlobalResponseModelStatus.OK,
                iid: '',
            });

            jest.spyOn(inpostClient, 'getAccessToken').mockResolvedValueOnce({
                access_token: accessToken,
                expires_in: 10000,
                refresh_expires_in: 3000,
                token_type: 'jwt',
                'not-before-policy': 0,
                scope: 'bloqit',
            });

            jest.spyOn(mappedLockerRepository, 'retrieveBySharedId').mockResolvedValueOnce({
                bloqId: bloqitBloqId,
                sharedId: partnerLockerId,
                layout: [
                    {
                        column: 1,
                        row: 1,
                        lockerId: 'locker-id',
                        lockerTitle: 'locker-title',
                        partnerName: '1R1',
                        size: LockerType.M,
                    },
                ],
            });
            const handler = new InpostHandlerImpl({
                logger,
                inpostClient,
                bloqitClient,
                labelAssociationRepository,
                mappedLockerRepository,
            });

            await handler.notifyDirtyDoor({
                ...baseEventData,
                actionInitiatedBy: { role: Roles.CUSTOMER, id: 'customer-id-123' },
                title: 'Pickup parcel',
            });

            expect(inpostClient.reportBoxMachineFunctionalityError).toHaveBeenCalledWith(
                expect.anything(),
                partnerBloqId,
                '',
                expect.objectContaining({
                    functionalityContext: FunctionalityContext.CustomerCollectParcel,
                }),
            );
        });

        it('should notify a dirty door with CustomerReturnParcel functionality context', async () => {
            const logger = new FakeLogger();
            const inpostClient = new FakeInPostApiClient();
            const bloqitClient = new FakeBloqitClient();

            jest.spyOn(inpostClient, 'reportCompartmentStates').mockResolvedValue({
                payload: '',
                exceptionMessage: '',
                exceptionKey: '',
                status: GlobalResponseModelStatus.OK,
                iid: '',
            });

            jest.spyOn(inpostClient, 'reportBoxMachineFunctionalityError').mockResolvedValue({
                payload: '',
                exceptionMessage: '',
                exceptionKey: '',
                status: GlobalResponseModelStatus.OK,
                iid: '',
            });

            jest.spyOn(inpostClient, 'getAccessToken').mockResolvedValueOnce({
                access_token: accessToken,
                expires_in: 10000,
                refresh_expires_in: 3000,
                token_type: 'jwt',
                'not-before-policy': 0,
                scope: 'bloqit',
            });

            jest.spyOn(mappedLockerRepository, 'retrieveBySharedId').mockResolvedValueOnce({
                bloqId: bloqitBloqId,
                sharedId: partnerLockerId,
                layout: [
                    {
                        column: 1,
                        row: 1,
                        lockerId: 'locker-id',
                        lockerTitle: 'locker-title',
                        partnerName: '1R1',
                        size: LockerType.M,
                    },
                ],
            });
            const handler = new InpostHandlerImpl({
                logger,
                inpostClient,
                bloqitClient,
                labelAssociationRepository,
                mappedLockerRepository,
            });

            await handler.notifyDirtyDoor({
                ...baseEventData,
                actionInitiatedBy: { role: Roles.CUSTOMER, id: 'customer-id-123' },
                title: 'Return parcel',
            });

            expect(inpostClient.reportBoxMachineFunctionalityError).toHaveBeenCalledWith(
                expect.anything(),
                partnerBloqId,
                '',
                expect.objectContaining({
                    functionalityContext: FunctionalityContext.CustomerReturnParcel,
                }),
            );
        });
    });

    describe('notifyStuckRent', () => {
        beforeEach(() => {
            jest.clearAllMocks();
        });

        const baseEventData = {
            rent: bloqitRentId,
            bloq: bloqitBloqId,
            locker: bloqitLockerId,
            customer: { email: '<EMAIL>' },
            message: 'Bloq reported that the door is stuck',
            timestamp: new Date(),
            metadata: {
                externalID: partnerRentId,
                bloqExternalID: partnerBloqId,
            },
        };

        it('should notify a stuck rent', async () => {
            const logger = new FakeLogger();
            const inpostClient = new FakeInPostApiClient();
            const bloqitClient = new FakeBloqitClient();

            jest.spyOn(inpostClient, 'reportCompartmentStates').mockResolvedValueOnce({
                payload: '',
                exceptionMessage: '',
                exceptionKey: '',
                status: GlobalResponseModelStatus.OK,
                iid: '',
            });

            jest.spyOn(inpostClient, 'reportBoxMachineFunctionalityError').mockResolvedValueOnce({
                payload: '',
                exceptionMessage: '',
                exceptionKey: '',
                status: GlobalResponseModelStatus.OK,
                iid: '',
            });

            jest.spyOn(inpostClient, 'getAccessToken').mockResolvedValueOnce({
                access_token: accessToken,
                expires_in: 10000,
                refresh_expires_in: 3000,
                token_type: 'jwt',
                'not-before-policy': 0,
                scope: 'bloqit',
            });

            jest.spyOn(mappedLockerRepository, 'retrieveBySharedId').mockResolvedValueOnce({
                bloqId: bloqitBloqId,
                sharedId: partnerLockerId,
                layout: [
                    {
                        column: 1,
                        row: 1,
                        lockerId: 'locker-id',
                        lockerTitle: 'locker-title',
                        partnerName: '1R1',
                        size: LockerType.M,
                    },
                ],
            });
            const handler = new InpostHandlerImpl({
                logger,
                inpostClient,
                bloqitClient,
                labelAssociationRepository,
                mappedLockerRepository,
            });

            await handler.notifyStuckRent({
                ...baseEventData,
                actionInitiatedBy: { role: Roles.CUSTOMER, id: 'customer-id-123' },
            });

            expect(inpostClient.reportCompartmentStates).toHaveBeenCalledTimes(1);
            expect(inpostClient.reportBoxMachineFunctionalityError).toHaveBeenCalledTimes(1);
        });
    });

    describe('getRentById', () => {
        const parcelCodeIdentifier = 'EXTERNAL-RENT-123';
        const externalBoxMachineId = 'BOXMACHINE-123';
        const country = 'UK';

        const numericParcelCodeIdentifier = 123456;

        const pickupCode = 123456;

        const checkPrelabeledParcelResponse: GlobalResponseModelWithParcel = {
            status: GlobalResponseModelStatus.OK_WITH_RESPONSE,
            payload: {
                customerPhone: '7000000000',
                senderPhone: '7000000000',
                boxMachine: externalBoxMachineId,
                senderBoxMachine: undefined,
                size: 'B',
                onDeliveryAmount: 0,
                customerReference: undefined,
                parcelCode: parcelCodeIdentifier,
                automaticReturn: false,
                returnAddressVO: undefined,
                targetAddressVO: {
                    buildingNo: undefined,
                    buldingNo: undefined,
                    companyName: undefined,
                    countryCode: undefined,
                    email: undefined,
                    flatNo: undefined,
                    name: undefined,
                    phoneNum: undefined,
                    province: undefined,
                    street: undefined,
                    surName: undefined,
                    town: undefined,
                    zipCode: undefined,
                },
                bussinesCustomer: false,
                directParcel: false,
                payCode: 0,
                openCode: pickupCode,
                returnCode: 0,
                carrier: 'MenziesUK',
                carrierReference: undefined,
                customerDeliveringCode: 0,
                id: 15154143,
                status: 'COURIER_DELIVERING',
                payForReturn: false,
                personalID: undefined,
                personalID2: undefined,
                supplierPhone: undefined,
                supplier: 'InditexTest',
                providerPhone: undefined,
                provider: 'ZARA',
                customerSpecialReq: undefined,
                multipleParcelsId: undefined,
                multipleParcelsCount: undefined,
                initialPackCode: undefined,
                pickupWithoutLabel: false,
                collectionBarcode: undefined,
                sendingAmount: [
                    {
                        size: 'A',
                        amount: 0,
                    },
                    {
                        size: 'B',
                        amount: 0,
                    },
                    {
                        size: 'C',
                        amount: 0,
                    },
                    {
                        size: 'D',
                        amount: 0,
                    },
                ],
                chargeForSending: undefined,
                carrierAcquirerPayCode: undefined,
                serviceName: 'STANDARD',
                reservationDataVO: undefined,
                timestamp: '2025-04-03T14:53:29.498+00:00',
                parcelAttributes: [
                    {
                        name: 'originSystem',
                        value: 'xFile',
                    },
                ],
                multiCompPotentialUuid: undefined,
                multiCompPotentialSize: undefined,
                endOfWeekCollection: false,
                availablePM: false,
            },
        };

        const reverseReturnResponse: GlobalResponseModelWithReverseReturnParcel = {
            status: GlobalResponseModelStatus.OK_WITH_RESPONSE,
            payload: {
                customerPhone: '7000000000',
                boxMachine: externalBoxMachineId,
                size: 'B',
                parcelCode: numericParcelCodeIdentifier.toString(),
                targetAddress: {
                    buildingNo: undefined,
                    buldingNo: undefined,
                    companyName: undefined,
                    countryCode: undefined,
                    email: undefined,
                    flatNo: undefined,
                    name: undefined,
                    phoneNum: undefined,
                    province: undefined,
                    street: undefined,
                    surName: undefined,
                    town: undefined,
                    zipCode: undefined,
                },
                directParcel: false,
                openCode: pickupCode,
                carrier: 'MenziesUK',
                id: 15154143,
                personalID2: undefined,
                pickupWithoutLabel: false,
                carrierAcquirerPayCode: undefined,
                serviceName: 'STANDARD',
                timestamp: '2025-04-03T14:53:29.498+00:00',
            },
        };

        const quickCodeResponse: GlobalResponseModelWithParcel = {
            status: GlobalResponseModelStatus.OK_WITH_RESPONSE,
            payload: {
                customerPhone: '7000000000',
                senderPhone: '7000000000',
                boxMachine: externalBoxMachineId,
                senderBoxMachine: undefined,
                size: 'B',
                onDeliveryAmount: 0,
                customerReference: undefined,
                parcelCode: numericParcelCodeIdentifier.toString(),
                automaticReturn: false,
                returnAddressVO: undefined,
                targetAddressVO: {
                    buildingNo: undefined,
                    buldingNo: undefined,
                    companyName: undefined,
                    countryCode: undefined,
                    email: undefined,
                    flatNo: undefined,
                    name: undefined,
                    phoneNum: undefined,
                    province: undefined,
                    street: undefined,
                    surName: undefined,
                    town: undefined,
                    zipCode: undefined,
                },
                bussinesCustomer: false,
                directParcel: false,
                payCode: 0,
                openCode: pickupCode,
                returnCode: 0,
                carrier: 'MenziesUK',
                carrierReference: undefined,
                customerDeliveringCode: 0,
                id: 15154143,
                status: 'COURIER_DELIVERING',
                payForReturn: false,
                personalID: undefined,
                personalID2: undefined,
                supplierPhone: undefined,
                supplier: 'InditexTest',
                providerPhone: undefined,
                provider: 'ZARA',
                customerSpecialReq: undefined,
                multipleParcelsId: undefined,
                multipleParcelsCount: undefined,
                initialPackCode: undefined,
                pickupWithoutLabel: true,
                collectionBarcode: undefined,
                sendingAmount: [
                    {
                        size: 'A',
                        amount: 0,
                    },
                    {
                        size: 'B',
                        amount: 0,
                    },
                    {
                        size: 'C',
                        amount: 0,
                    },
                    {
                        size: 'D',
                        amount: 0,
                    },
                ],
                chargeForSending: undefined,
                carrierAcquirerPayCode: undefined,
                serviceName: 'STANDARD',
                reservationDataVO: undefined,
                timestamp: '2025-04-03T14:53:29.498+00:00',
                parcelAttributes: [
                    {
                        name: 'originSystem',
                        value: 'xFile',
                    },
                ],
                multiCompPotentialUuid: undefined,
                multiCompPotentialSize: undefined,
                endOfWeekCollection: false,
                availablePM: false,
            },
        };

        const badRequestResponseParcel: GlobalResponseModelWithParcel = {
            status: GlobalResponseModelStatus.EXCEPTION,
            exceptionKey: 'DeliveryPackDoesNotExist',
            iid: 'exception-id',
            exceptionMessage: 'Parcel with code does not exist.',
        };

        const badRequestResponseReverseReturnParcel: GlobalResponseModelWithReverseReturnParcel = {
            status: GlobalResponseModelStatus.EXCEPTION,
            exceptionKey: 'DeliveryPackDoesNotExist',
            iid: 'exception-id',
            exceptionMessage: 'Parcel with code does not exist.',
        };

        const QRCodeParcelResponse: QRCodeParcelResponse = {
            customerPhone: '7000000000',
            boxMachineName: externalBoxMachineId,
            senderBoxMachineName: undefined,
            parcelSize: 'B',
            parcelCode: parcelCodeIdentifier,
            payCode: 0,
            openCode: pickupCode,
            carrierName: 'MenziesUK',
            carrierReference: undefined,
            parcelID: 15154143,
            parcelStatus: 'COURIER_DELIVERING',
            multiCompartmentUuid: undefined,
            multiCompartmentPotentialSize: undefined,
            multiCompartmentPotentialUuid: undefined,
            pickupWithoutLabel: false,
            timestamp: '2025-04-03T14:53:29.498+00:00',
            parcelAttributes: [
                {
                    name: 'originSystem',
                    value: 'xFile',
                },
            ],
            endOfWeekCollection: false,
            availablePM: false,
            onDeliveryAmount: '',
            serviceName: 'SERVICE_NAME',
        };

        const badRequestQRCodeParcelResponse: QRCodeErrorResponse = {
            exceptionKey: 'DeliveryPackDoesNotExist',
            exceptionMessage: 'Parcel with code does not exist.',
        };

        it('should get a prelabeled parcel', async () => {
            const logger = new FakeLogger();
            const inpostClient = new FakeInPostApiClient();
            const bloqitClient = new FakeBloqitClient();
            const boxSize = BoxSize.B;

            if (checkPrelabeledParcelResponse.payload != null) {
                checkPrelabeledParcelResponse.payload.size = boxSize;
            }

            jest.spyOn(inpostClient, 'getAccessToken').mockResolvedValueOnce({
                access_token: accessToken,
                expires_in: 10000,
                refresh_expires_in: 3000,
                token_type: 'jwt',
                'not-before-policy': 0,
                scope: 'bloqit',
            });

            jest.spyOn(inpostClient, 'getParcelByQuickSendCode').mockResolvedValueOnce(
                badRequestResponseParcel,
            );

            jest.spyOn(
                inpostClient,
                'createReverseReturnParcelFromBoxMachine',
            ).mockResolvedValueOnce(badRequestResponseReverseReturnParcel);

            jest.spyOn(inpostClient, 'checkPrelabeledParcel').mockResolvedValueOnce(
                checkPrelabeledParcelResponse,
            );

            const handler = new InpostHandlerImpl({
                logger,
                inpostClient,
                bloqitClient,
                labelAssociationRepository,
                mappedLockerRepository,
            });

            const result = await handler.getRentById({
                partnerRentId: parcelCodeIdentifier,
                bloqId: externalBoxMachineId,
                country,
                locale: '',
                nodeId: externalBoxMachineId,
            });

            expect(inpostClient.getParcelByQuickSendCode).toHaveBeenCalledTimes(0);
            expect(inpostClient.createReverseReturnParcelFromBoxMachine).toHaveBeenCalledTimes(0);
            expect(inpostClient.checkPrelabeledParcel).toHaveBeenCalledTimes(1);

            expect(result.length).toBe(1);
            expect(result[0].externalID).toBe(parcelCodeIdentifier);
            expect(result[0].metadata?.size).toBe(LockerType.M);
        });

        it('should get a parcel by getQuickSendCode', async () => {
            const logger = new FakeLogger();
            const inpostClient = new FakeInPostApiClient();
            const bloqitClient = new FakeBloqitClient();
            const validNumericQuickSendCode = 123456789;

            jest.spyOn(inpostClient, 'getAccessToken').mockResolvedValueOnce({
                access_token: accessToken,
                expires_in: 10000,
                refresh_expires_in: 3000,
                token_type: 'jwt',
                'not-before-policy': 0,
                scope: 'bloqit',
            });

            if (quickCodeResponse.payload) {
                quickCodeResponse.payload.parcelCode = validNumericQuickSendCode.toString();
            }

            jest.spyOn(inpostClient, 'getParcelByQuickSendCode').mockResolvedValueOnce(
                quickCodeResponse,
            );

            jest.spyOn(
                inpostClient,
                'createReverseReturnParcelFromBoxMachine',
            ).mockResolvedValueOnce(badRequestResponseReverseReturnParcel);

            jest.spyOn(inpostClient, 'checkPrelabeledParcel').mockResolvedValueOnce(
                badRequestResponseParcel,
            );

            const handler = new InpostHandlerImpl({
                logger,
                inpostClient,
                bloqitClient,
                labelAssociationRepository,
                mappedLockerRepository,
            });

            const result = await handler.getRentById({
                partnerRentId: validNumericQuickSendCode.toString(),
                bloqId: externalBoxMachineId,
                country,
                locale: '',
                nodeId: externalBoxMachineId,
            });

            expect(inpostClient.getParcelByQuickSendCode).toHaveBeenCalledTimes(1);
            expect(inpostClient.createReverseReturnParcelFromBoxMachine).toHaveBeenCalledTimes(0);
            expect(inpostClient.checkPrelabeledParcel).toHaveBeenCalledTimes(0);

            expect(result.length).toBe(1);
            expect(result[0].externalID).toBe(validNumericQuickSendCode.toString());
        });

        it('should get a parcel by createReverseReturnParcel', async () => {
            const logger = new FakeLogger();
            const inpostClient = new FakeInPostApiClient();
            const bloqitClient = new FakeBloqitClient();
            const reverseReturnValidNumericParcelCode = 1234567890;

            if (reverseReturnResponse.payload) {
                reverseReturnResponse.payload.parcelCode =
                    reverseReturnValidNumericParcelCode.toString();
            }

            jest.spyOn(inpostClient, 'getAccessToken').mockResolvedValueOnce({
                access_token: accessToken,
                expires_in: 10000,
                refresh_expires_in: 3000,
                token_type: 'jwt',
                'not-before-policy': 0,
                scope: 'bloqit',
            });

            jest.spyOn(inpostClient, 'getParcelByQuickSendCode').mockResolvedValueOnce(
                badRequestResponseParcel,
            );

            jest.spyOn(
                inpostClient,
                'createReverseReturnParcelFromBoxMachine',
            ).mockResolvedValueOnce(reverseReturnResponse);

            jest.spyOn(inpostClient, 'checkPrelabeledParcel').mockResolvedValueOnce(
                badRequestResponseParcel,
            );

            const handler = new InpostHandlerImpl({
                logger,
                inpostClient,
                bloqitClient,
                labelAssociationRepository,
                mappedLockerRepository,
            });

            const result = await handler.getRentById({
                partnerRentId: reverseReturnValidNumericParcelCode.toString(),
                bloqId: externalBoxMachineId,
                country,
                locale: '',
                nodeId: externalBoxMachineId,
            });

            expect(inpostClient.getParcelByQuickSendCode).toHaveBeenCalledTimes(0);
            expect(inpostClient.createReverseReturnParcelFromBoxMachine).toHaveBeenCalledTimes(1);
            expect(inpostClient.checkPrelabeledParcel).toHaveBeenCalledTimes(0);

            expect(result.length).toBe(1);
            expect(result[0].externalID).toBe(reverseReturnValidNumericParcelCode.toString());
        });

        it('should return empty rent if not found', async () => {
            const logger = new FakeLogger();
            const inpostClient = new FakeInPostApiClient();
            const bloqitClient = new FakeBloqitClient();

            jest.spyOn(inpostClient, 'getAccessToken').mockResolvedValueOnce({
                access_token: accessToken,
                expires_in: 10000,
                refresh_expires_in: 3000,
                token_type: 'jwt',
                'not-before-policy': 0,
                scope: 'bloqit',
            });

            jest.spyOn(inpostClient, 'getParcelByQuickSendCode').mockResolvedValueOnce(
                badRequestResponseParcel,
            );

            jest.spyOn(
                inpostClient,
                'createReverseReturnParcelFromBoxMachine',
            ).mockResolvedValueOnce(badRequestResponseReverseReturnParcel);

            jest.spyOn(inpostClient, 'checkPrelabeledParcel').mockResolvedValueOnce(
                badRequestResponseParcel,
            );

            jest.spyOn(inpostClient, 'reportBoxMachineFunctionalityError').mockResolvedValueOnce({
                payload: {},
                exceptionMessage: '',
                exceptionKey: '',
                status: GlobalResponseModelStatus.OK,
                iid: '',
            });

            const handler = new InpostHandlerImpl({
                logger,
                inpostClient,
                bloqitClient,
                labelAssociationRepository,
                mappedLockerRepository,
            });

            const result = await handler.getRentById({
                partnerRentId: numericParcelCodeIdentifier.toString(),
                bloqId: externalBoxMachineId,
                country,
                locale: '',
                nodeId: externalBoxMachineId,
            });

            expect(inpostClient.getParcelByQuickSendCode).toHaveBeenCalledTimes(0);
            expect(inpostClient.createReverseReturnParcelFromBoxMachine).toHaveBeenCalledTimes(0);
            expect(inpostClient.checkPrelabeledParcel).toHaveBeenCalledTimes(1);
            expect(inpostClient.reportBoxMachineFunctionalityError).toHaveBeenCalledTimes(1);

            expect(result.length).toBe(0);
        });

        it('should get a parcel by getParcelByQRCode', async () => {
            const logger = new FakeLogger();
            const inpostClient = new FakeInPostApiClient();
            const bloqitClient = new FakeBloqitClient();

            const validQRCode = 'H4sIAAAAAAAAAHvzloG19VaA';
            const requestCountryFrance = 'FR';

            jest.spyOn(inpostClient, 'getAccessToken').mockResolvedValueOnce({
                access_token: accessToken,
                expires_in: 10000,
                refresh_expires_in: 3000,
                token_type: 'jwt',
                'not-before-policy': 0,
                scope: 'bloqit',
            });

            jest.spyOn(inpostClient, 'getParcelByQRCode').mockResolvedValueOnce(
                QRCodeParcelResponse,
            );

            const handler = new InpostHandlerImpl({
                logger,
                inpostClient,
                bloqitClient,
                labelAssociationRepository,
                mappedLockerRepository,
            });

            const result = await handler.getRentById({
                partnerRentId: validQRCode,
                bloqId: externalBoxMachineId,
                country: requestCountryFrance,
                locale: '',
                nodeId: externalBoxMachineId,
            });

            expect(inpostClient.getParcelByQuickSendCode).toHaveBeenCalledTimes(0);
            expect(inpostClient.createReverseReturnParcelFromBoxMachine).toHaveBeenCalledTimes(0);
            expect(inpostClient.getParcelByQRCode).toHaveBeenCalledTimes(1);
            expect(inpostClient.checkPrelabeledParcel).toHaveBeenCalledTimes(0);

            expect(result.length).toBe(1);
            expect(result[0].externalID).toBe(parcelCodeIdentifier);
            expect(result[0].customer?.phone).toBe(QRCodeParcelResponse.customerPhone);
        });

        it('should return empty rent if not found by getParcelByQRCode', async () => {
            const logger = new FakeLogger();
            const inpostClient = new FakeInPostApiClient();
            const bloqitClient = new FakeBloqitClient();

            const validQRCode = 'INVALID-QR-CODE';
            const requestCountryFrance = 'FR';

            jest.spyOn(inpostClient, 'getAccessToken').mockResolvedValueOnce({
                access_token: accessToken,
                expires_in: 10000,
                refresh_expires_in: 3000,
                token_type: 'jwt',
                'not-before-policy': 0,
                scope: 'bloqit',
            });

            jest.spyOn(inpostClient, 'getParcelByQRCode').mockResolvedValueOnce(
                badRequestQRCodeParcelResponse,
            );

            const handler = new InpostHandlerImpl({
                logger,
                inpostClient,
                bloqitClient,
                labelAssociationRepository,
                mappedLockerRepository,
            });

            const result = await handler.getRentById({
                partnerRentId: validQRCode,
                bloqId: externalBoxMachineId,
                country: requestCountryFrance,
                locale: '',
                nodeId: externalBoxMachineId,
            });

            expect(inpostClient.getParcelByQuickSendCode).toHaveBeenCalledTimes(0);
            expect(inpostClient.createReverseReturnParcelFromBoxMachine).toHaveBeenCalledTimes(0);
            expect(inpostClient.getParcelByQRCode).toHaveBeenCalledTimes(0);
            expect(inpostClient.checkPrelabeledParcel).toHaveBeenCalledTimes(1);

            expect(result.length).toBe(0);
        });

        it('should return empty if is QR code but the country is not FR', async () => {
            const logger = new FakeLogger();
            const inpostClient = new FakeInPostApiClient();
            const bloqitClient = new FakeBloqitClient();

            const validQRCode = 'H4sIAAAAAAAAAHvzloG19VaA';
            const otherCountryThanFrance = 'GB';

            jest.spyOn(inpostClient, 'getAccessToken').mockResolvedValueOnce({
                access_token: accessToken,
                expires_in: 10000,
                refresh_expires_in: 3000,
                token_type: 'jwt',
                'not-before-policy': 0,
                scope: 'bloqit',
            });

            jest.spyOn(inpostClient, 'getParcelByQRCode').mockResolvedValueOnce(
                badRequestQRCodeParcelResponse,
            );

            const handler = new InpostHandlerImpl({
                logger,
                inpostClient,
                bloqitClient,
                labelAssociationRepository,
                mappedLockerRepository,
            });

            const result = await handler.getRentById({
                partnerRentId: validQRCode,
                bloqId: externalBoxMachineId,
                country: otherCountryThanFrance,
                locale: '',
                nodeId: externalBoxMachineId,
            });

            expect(inpostClient.getParcelByQuickSendCode).toHaveBeenCalledTimes(0);
            expect(inpostClient.createReverseReturnParcelFromBoxMachine).toHaveBeenCalledTimes(0);
            expect(inpostClient.getParcelByQRCode).toHaveBeenCalledTimes(0);
            expect(inpostClient.checkPrelabeledParcel).toHaveBeenCalledTimes(1);

            expect(result.length).toBe(0);
        });
    });

    describe('associateLabel', () => {
        beforeEach(() => {
            jest.clearAllMocks();
        });

        it('pack exists and a change parcel status request is done with success', async () => {
            const logger = new FakeLogger();
            const inpostClient = new FakeInPostApiClient();
            const bloqitClient = new FakeBloqitClient();
            const fakeRepository = new FakeLabelAssociationRepository();
            const labelCode = 'label-code-123';

            jest.spyOn(fakeRepository, 'create').mockResolvedValueOnce(undefined);

            jest.spyOn(inpostClient, 'packExist').mockResolvedValueOnce({
                payload: true,
                status: GlobalResponseModelStatus.OK,
            });

            jest.spyOn(inpostClient, 'getAccessToken').mockResolvedValueOnce({
                access_token: accessToken,
                expires_in: 10000,
                refresh_expires_in: 3000,
                token_type: 'jwt',
                'not-before-policy': 0,
                scope: 'bloqit',
            });

            const handler = new InpostHandlerImpl({
                logger,
                inpostClient,
                bloqitClient,
                labelAssociationRepository: fakeRepository,
                mappedLockerRepository,
            });

            await handler.associateLabel({
                partnerRentId: partnerRentId,
                bloqId: partnerBloqId,
                locale: 'UK',
                code: labelCode,
                courier: '100',
                operationType: OperationType.FIRST_MILE,
                country: 'UK',
                bloqExternalId: partnerBloqId,
            });

            expect(inpostClient.packExist).toHaveBeenCalledTimes(1);
            expect(fakeRepository.create).toHaveBeenCalledTimes(1);
            expect(fakeRepository.create).toHaveBeenCalledWith({
                labelAssociation: {
                    partnerRentId,
                    label: labelCode,
                },
            });
        });

        it('should make the request with success if locale is different from country', async () => {
            const logger = new FakeLogger();
            const inpostClient = new FakeInPostApiClient();
            const bloqitClient = new FakeBloqitClient();
            const fakeRepository = new FakeLabelAssociationRepository();
            const labelCode = 'label-code-123';

            jest.spyOn(fakeRepository, 'create').mockResolvedValueOnce(undefined);

            jest.spyOn(inpostClient, 'packExist').mockResolvedValueOnce({
                payload: true,
                status: GlobalResponseModelStatus.OK,
            });

            jest.spyOn(inpostClient, 'getAccessToken').mockResolvedValueOnce({
                access_token: accessToken,
                expires_in: 10000,
                refresh_expires_in: 3000,
                token_type: 'jwt',
                'not-before-policy': 0,
                scope: 'bloqit',
            });

            const handler = new InpostHandlerImpl({
                logger,
                inpostClient,
                bloqitClient,
                labelAssociationRepository: fakeRepository,
                mappedLockerRepository,
            });

            await handler.associateLabel({
                partnerRentId: partnerRentId,
                bloqId: 'bloq-123',
                locale: 'FR',
                code: labelCode,
                courier: '100',
                operationType: OperationType.FIRST_MILE,
                country: 'UK',
                bloqExternalId: partnerBloqId,
            });

            expect(inpostClient.packExist).toHaveBeenCalledTimes(1);
            expect(fakeRepository.create).toHaveBeenCalledWith({
                labelAssociation: {
                    partnerRentId,
                    label: labelCode,
                },
            });
        });

        it('pack does not exist and a reportBoxMachineFunctionalityError request is done', async () => {
            const logger = new FakeLogger();
            const inpostClient = new FakeInPostApiClient();
            const bloqitClient = new FakeBloqitClient();
            const fakeRepository = new FakeLabelAssociationRepository();
            jest.spyOn(fakeRepository, 'create').mockResolvedValueOnce(undefined);

            jest.spyOn(inpostClient, 'reportBoxMachineFunctionalityError').mockResolvedValueOnce({
                payload: '',
                status: GlobalResponseModelStatus.OK,
            });

            jest.spyOn(inpostClient, 'packExist').mockResolvedValueOnce({
                payload: false,
                status: GlobalResponseModelStatus.OK,
            });

            jest.spyOn(inpostClient, 'getAccessToken').mockResolvedValueOnce({
                access_token: accessToken,
                expires_in: 10000,
                refresh_expires_in: 3000,
                token_type: 'jwt',
                'not-before-policy': 0,
                scope: 'bloqit',
            });

            const handler = new InpostHandlerImpl({
                logger,
                inpostClient,
                bloqitClient,
                labelAssociationRepository: fakeRepository,
                mappedLockerRepository,
            });

            await handler.associateLabel({
                partnerRentId: partnerRentId,
                bloqId: partnerBloqId,
                locale: 'UK',
                code: 'label-code-123',
                courier: '100',
                operationType: OperationType.FIRST_MILE,
                country: 'UK',
                bloqExternalId: partnerBloqId,
            });

            expect(inpostClient.packExist).toHaveBeenCalledTimes(1);
            expect(fakeRepository.create).not.toHaveBeenCalled();
            expect(inpostClient.reportBoxMachineFunctionalityError).toHaveBeenCalledTimes(1);
            expect(inpostClient.reportBoxMachineFunctionalityError).toHaveBeenCalledWith(
                expect.objectContaining({ token: accessToken }),
                partnerBloqId,
                'UK',
                expect.objectContaining({
                    errorCode: FunctionalityErrorRequestErrorCode.ParcelDoesNotExistInCentralSystem,
                    functionalityContext: FunctionalityContext.PickUpNotlabeledParcels,
                }),
            );
        });
    });

    describe('authenticateCourier', () => {
        beforeEach(() => {
            jest.clearAllMocks();
        });

        it('should return success: true given a correct password', async () => {
            const logger = new FakeLogger();
            const inpostClient = new FakeInPostApiClient();
            const bloqitClient = new FakeBloqitClient();

            jest.spyOn(inpostClient, 'getAccessToken').mockResolvedValueOnce({
                access_token: accessToken,
                expires_in: 10000,
                refresh_expires_in: 3000,
                token_type: 'jwt',
                'not-before-policy': 0,
                scope: 'bloqit',
            });

            jest.spyOn(inpostClient, 'courierUpdateAdHoc').mockResolvedValueOnce({
                payload: {
                    login: '0000000001',
                    password: '6934ad71bdccd3a05b94ed8eb9493119', // md5 of password-123
                },
                status: 'OK_WITH_RESPONSE',
            });

            const handler = new InpostHandlerImpl({
                logger,
                inpostClient,
                bloqitClient,
                labelAssociationRepository,
                mappedLockerRepository,
            });

            const result = await handler.authenticateCourier({
                bloqExternalId: partnerBloqId,
                courierId: 'courier-1',
                password: 'password-123',
                country: 'GB',
            });

            expect(inpostClient.getAccessToken).toHaveBeenCalledTimes(1);
            expect(inpostClient.courierUpdateAdHoc).toHaveBeenCalledTimes(1);
            expect(result.success).toBe(true);
        });

        it('should return success: false given an incorrect password', async () => {
            const logger = new FakeLogger();
            const inpostClient = new FakeInPostApiClient();
            const bloqitClient = new FakeBloqitClient();

            jest.spyOn(inpostClient, 'getAccessToken').mockResolvedValueOnce({
                access_token: accessToken,
                expires_in: 10000,
                refresh_expires_in: 3000,
                token_type: 'jwt',
                'not-before-policy': 0,
                scope: 'bloqit',
            });

            jest.spyOn(inpostClient, 'courierUpdateAdHoc').mockResolvedValueOnce({
                payload: {
                    login: '0000000001',
                    password: '6934ad71bdccd3a05b94ed8eb9493119', // md5 of password-123
                },
                status: 'OK_WITH_RESPONSE',
            });

            const handler = new InpostHandlerImpl({
                logger,
                inpostClient,
                bloqitClient,
                labelAssociationRepository,
                mappedLockerRepository,
            });

            const result = await handler.authenticateCourier({
                bloqExternalId: partnerBloqId,
                courierId: 'courier-1',
                password: 'password-456',
                country: 'GB',
            });

            expect(inpostClient.getAccessToken).toHaveBeenCalledTimes(1);
            expect(inpostClient.courierUpdateAdHoc).toHaveBeenCalledTimes(1);
            expect(result.success).toBe(false);
        });

        it('should return 400 when country code is undefined', async () => {
            const logger = new FakeLogger();
            const inpostClient = new FakeInPostApiClient();
            const bloqitClient = new FakeBloqitClient();

            jest.spyOn(inpostClient, 'getAccessToken').mockResolvedValueOnce({
                access_token: accessToken,
                expires_in: 10000,
                refresh_expires_in: 3000,
                token_type: 'jwt',
                'not-before-policy': 0,
                scope: 'bloqit',
            });

            jest.spyOn(inpostClient, 'courierUpdateAdHoc').mockResolvedValueOnce({
                payload: {
                    login: '0000000001',
                    password: '6934ad71bdccd3a05b94ed8eb9493119', // md5 of password-123
                },
                status: 'OK_WITH_RESPONSE',
            });

            const handler = new InpostHandlerImpl({
                logger,
                inpostClient,
                bloqitClient,
                labelAssociationRepository,
                mappedLockerRepository,
            });

            await expect(
                handler.authenticateCourier({
                    bloqExternalId: partnerBloqId,
                    courierId: 'courier-1',
                    password: 'password-123',
                }),
            ).rejects.toThrow();

            expect(inpostClient.getAccessToken).toHaveBeenCalledTimes(1);
            expect(inpostClient.courierUpdateAdHoc).not.toHaveBeenCalled();
        });
    });

    describe('getCouriersAccessCodes', () => {
        beforeEach(() => {
            jest.clearAllMocks();
        });

        it('should return couriers with permissions', async () => {
            const logger = new FakeLogger();
            const inpostClient = new FakeInPostApiClient();
            const bloqitClient = new FakeBloqitClient();

            jest.spyOn(inpostClient, 'getAccessToken').mockResolvedValueOnce({
                access_token: accessToken,
                expires_in: 10000,
                refresh_expires_in: 3000,
                token_type: 'jwt',
                'not-before-policy': 0,
                scope: 'bloqit',
            });

            jest.spyOn(inpostClient, 'getCouriersChangedDeltaPage').mockResolvedValueOnce({
                payload: {
                    count: 2,
                    result: [
                        {
                            id: 8036,
                            documentNumber: 'bloqit-courier-1',
                            login: 'bloqit-courier-1',
                            password: '2de5d16682c3c35007e4e92982f1a2ba',
                            userRole: '22',
                            carrier: 'DX',
                            active: true,
                            startDate: null,
                            endDate: null,
                        },
                        {
                            id: 29032,
                            documentNumber: 'bloqit-courier-2',
                            login: 'bloqit-courier-2',
                            password: '2de5d16682c3c35007e4e92982f1a2ba',
                            userRole: '22',
                            carrier: 'UK',
                            active: true,
                            startDate: '2020-11-08T00:00:00Z',
                            endDate: '2099-12-31T00:00:00Z',
                        },
                    ],
                },
                status: GlobalResponseModelStatus.OK_WITH_RESPONSE,
            });

            jest.spyOn(inpostClient, 'getUserRolesDelta').mockResolvedValueOnce({
                payload: {
                    timestamp: '2025-05-15T14:32:58.361+01:00',
                    changed: [
                        {
                            id: 41,
                            name: '22',
                            mask: 7569151,
                        },
                        {
                            id: 782,
                            name: '308',
                            mask: 0,
                        },
                    ],
                    deleted: [
                        {
                            id: 341,
                            name: '35',
                            mask: 0,
                        },
                    ],
                },
                status: GlobalResponseModelStatus.OK_WITH_RESPONSE,
            });

            const handler = new InpostHandlerImpl({
                logger,
                inpostClient,
                bloqitClient,
                labelAssociationRepository,
                mappedLockerRepository,
            });

            const result = await handler.getCouriersAccessCodes({
                bloqId: partnerBloqId,
                bloqExternalId: partnerBloqId,
                country: 'PL',
            });

            expect(inpostClient.getAccessToken).toHaveBeenCalledTimes(1);
            expect(inpostClient.getCouriersChangedDeltaPage).toHaveBeenCalledTimes(1);
            expect(inpostClient.getUserRolesDelta).toHaveBeenCalledTimes(1);
            expect(result).toEqual({
                couriers: [
                    {
                        code: '',
                        carrier: 'DX',
                        hashAlgorithm: 'MD5',
                        password: '2de5d16682c3c35007e4e92982f1a2ba',
                        permissions: [
                            'DROP_OFF',
                            'PICK_UP',
                            'MAINTENANCE_ACCESS',
                            'CLEAN_BLOQ',
                            'INSPECT_BLOQ',
                            'EMERGENCY_ACCESS',
                        ],
                        username: 'bloqit-courier-1',
                    },
                    {
                        code: '',
                        carrier: 'UK',
                        hashAlgorithm: 'MD5',
                        password: '2de5d16682c3c35007e4e92982f1a2ba',
                        permissions: [
                            'DROP_OFF',
                            'PICK_UP',
                            'MAINTENANCE_ACCESS',
                            'CLEAN_BLOQ',
                            'INSPECT_BLOQ',
                            'EMERGENCY_ACCESS',
                        ],
                        username: 'bloqit-courier-2',
                    },
                ],
            });
        });

        it('should return courier filtered by carrier', async () => {
            const logger = new FakeLogger();
            const inpostClient = new FakeInPostApiClient();
            const bloqitClient = new FakeBloqitClient();

            jest.spyOn(inpostClient, 'getAccessToken').mockResolvedValueOnce({
                access_token: accessToken,
                expires_in: 10000,
                refresh_expires_in: 3000,
                token_type: 'jwt',
                'not-before-policy': 0,
                scope: 'bloqit',
            });

            jest.spyOn(inpostClient, 'getCouriersChangedDeltaPage').mockResolvedValueOnce({
                payload: {
                    count: 2,
                    result: [
                        {
                            id: 8036,
                            documentNumber: 'bloqit-courier-1',
                            login: 'bloqit-courier-1',
                            password: '2de5d16682c3c35007e4e92982f1a2ba',
                            userRole: '22',
                            carrier: 'DX',
                            active: true,
                            startDate: null,
                            endDate: null,
                        },
                        {
                            id: 29032,
                            documentNumber: 'bloqit-courier-2',
                            login: 'bloqit-courier-2',
                            password: '2de5d16682c3c35007e4e92982f1a2ba',
                            userRole: '22',
                            carrier: 'UK',
                            active: true,
                            startDate: '2020-11-08T00:00:00Z',
                            endDate: '2099-12-31T00:00:00Z',
                        },
                    ],
                },
                status: GlobalResponseModelStatus.OK_WITH_RESPONSE,
            });

            jest.spyOn(inpostClient, 'getUserRolesDelta').mockResolvedValueOnce({
                payload: {
                    timestamp: '2025-05-15T14:32:58.361+01:00',
                    changed: [
                        {
                            id: 41,
                            name: '22',
                            mask: 7569151,
                        },
                        {
                            id: 782,
                            name: '308',
                            mask: 0,
                        },
                    ],
                    deleted: [
                        {
                            id: 341,
                            name: '35',
                            mask: 0,
                        },
                    ],
                },
                status: GlobalResponseModelStatus.OK_WITH_RESPONSE,
            });

            const handler = new InpostHandlerImpl({
                logger,
                inpostClient,
                bloqitClient,
                labelAssociationRepository,
                mappedLockerRepository,
            });

            const result = await handler.getCouriersAccessCodes({
                bloqId: partnerBloqId,
                bloqExternalId: partnerBloqId,
                carrier: 'DX',
                country: 'PL',
            });

            expect(inpostClient.getAccessToken).toHaveBeenCalledTimes(1);
            expect(inpostClient.getCouriersChangedDeltaPage).toHaveBeenCalledTimes(1);
            expect(inpostClient.getUserRolesDelta).toHaveBeenCalledTimes(1);
            expect(result).toEqual({
                couriers: [
                    {
                        code: '',
                        carrier: 'DX',
                        hashAlgorithm: 'MD5',
                        password: '2de5d16682c3c35007e4e92982f1a2ba',
                        permissions: [
                            'DROP_OFF',
                            'PICK_UP',
                            'MAINTENANCE_ACCESS',
                            'CLEAN_BLOQ',
                            'INSPECT_BLOQ',
                            'EMERGENCY_ACCESS',
                        ],
                        username: 'bloqit-courier-1',
                    },
                ],
            });
        });

        it('should return empty couriers list if no couriers are found', async () => {
            const logger = new FakeLogger();
            const inpostClient = new FakeInPostApiClient();
            const bloqitClient = new FakeBloqitClient();

            jest.spyOn(inpostClient, 'getAccessToken').mockResolvedValueOnce({
                access_token: accessToken,
                expires_in: 10000,
                refresh_expires_in: 3000,
                token_type: 'jwt',
                'not-before-policy': 0,
                scope: 'bloqit',
            });

            jest.spyOn(inpostClient, 'getCouriersChangedDeltaPage').mockResolvedValueOnce({
                payload: {
                    count: 0,
                    result: [],
                },
                status: GlobalResponseModelStatus.OK_WITH_RESPONSE,
            });

            jest.spyOn(inpostClient, 'getUserRolesDelta').mockResolvedValueOnce({
                payload: {
                    timestamp: '2025-05-15T14:32:58.361+01:00',
                    changed: [],
                    deleted: [],
                },
                status: GlobalResponseModelStatus.OK_WITH_RESPONSE,
            });

            const handler = new InpostHandlerImpl({
                logger,
                inpostClient,
                bloqitClient,
                labelAssociationRepository,
                mappedLockerRepository,
            });

            const result = await handler.getCouriersAccessCodes({
                bloqId: partnerBloqId,
                bloqExternalId: partnerBloqId,
                country: 'PL',
            });

            expect(inpostClient.getAccessToken).toHaveBeenCalledTimes(1);
            expect(inpostClient.getCouriersChangedDeltaPage).toHaveBeenCalledTimes(1);
            expect(result).toEqual({ couriers: [] });
        });

        it('should return courier without permissions if no permissions are found', async () => {
            const logger = new FakeLogger();
            const inpostClient = new FakeInPostApiClient();
            const bloqitClient = new FakeBloqitClient();

            jest.spyOn(inpostClient, 'getAccessToken').mockResolvedValueOnce({
                access_token: accessToken,
                expires_in: 10000,
                refresh_expires_in: 3000,
                token_type: 'jwt',
                'not-before-policy': 0,
                scope: 'bloqit',
            });

            jest.spyOn(inpostClient, 'getCouriersChangedDeltaPage').mockResolvedValueOnce({
                payload: {
                    count: 1,
                    result: [
                        {
                            id: 8036,
                            documentNumber: 'bloqit-courier-1',
                            login: 'bloqit-courier-1',
                            password: '2de5d16682c3c35007e4e92982f1a2ba',
                            userRole: '22',
                            carrier: 'DX',
                            active: true,
                            startDate: null,
                            endDate: null,
                        },
                    ],
                },
                status: GlobalResponseModelStatus.OK_WITH_RESPONSE,
            });

            jest.spyOn(inpostClient, 'getUserRolesDelta').mockResolvedValueOnce({
                payload: {
                    timestamp: '2025-05-15T14:32:58.361+01:00',
                    changed: [],
                    deleted: [],
                },
                status: GlobalResponseModelStatus.OK_WITH_RESPONSE,
            });

            const handler = new InpostHandlerImpl({
                logger,
                inpostClient,
                bloqitClient,
                labelAssociationRepository,
                mappedLockerRepository,
            });

            const result = await handler.getCouriersAccessCodes({
                bloqId: partnerBloqId,
                bloqExternalId: partnerBloqId,
                country: 'PL',
            });

            expect(inpostClient.getAccessToken).toHaveBeenCalledTimes(1);
            expect(inpostClient.getCouriersChangedDeltaPage).toHaveBeenCalledTimes(1);
            expect(result).toEqual({
                couriers: [
                    {
                        code: '',
                        carrier: 'DX',
                        hashAlgorithm: 'MD5',
                        password: '2de5d16682c3c35007e4e92982f1a2ba',
                        permissions: [],
                        username: 'bloqit-courier-1',
                    },
                ],
            });
        });

        it('should fail if country is not present', async () => {
            const logger = new FakeLogger();
            const inpostClient = new FakeInPostApiClient();
            const bloqitClient = new FakeBloqitClient();

            jest.spyOn(inpostClient, 'getAccessToken').mockResolvedValueOnce({
                access_token: accessToken,
                expires_in: 10000,
                refresh_expires_in: 3000,
                token_type: 'jwt',
                'not-before-policy': 0,
                scope: 'bloqit',
            });

            const handler = new InpostHandlerImpl({
                logger,
                inpostClient,
                bloqitClient,
                labelAssociationRepository,
                mappedLockerRepository,
            });

            await expect(
                handler.getCouriersAccessCodes({
                    bloqId: partnerBloqId,
                    bloqExternalId: partnerBloqId,
                    country: undefined,
                }),
            ).rejects.toThrow();
        });
    });

    describe('maintenanceStatusUpdate', () => {
        beforeEach(() => {
            jest.clearAllMocks();
        });

        const baseEventData = {
            partner: bloqitPartnerId,
            title: 'bloq-title',
            code: 'event-code',
            description: 'event-description',
            codeName: 'event-code-name',
            bloq: 'bloq-id',
            locker: bloqitLockerId,
            uiFlowContext: 'ui-flow-context',
            actionInitiatedBy: { role: Roles.CUSTOMER, id: 'customer-id-123' },
        };

        it('should notify a soiled compartment when reason is dirty_locker and status is none -> triage', async () => {
            const logger = new FakeLogger();
            const inpostClient = new FakeInPostApiClient();
            const bloqitClient = new FakeBloqitClient();

            jest.spyOn(inpostClient, 'reportCompartmentStates').mockResolvedValueOnce({
                payload: '',
                exceptionMessage: '',
                exceptionKey: '',
                status: GlobalResponseModelStatus.OK,
                iid: '',
            });

            jest.spyOn(inpostClient, 'getAccessToken').mockResolvedValueOnce({
                access_token: accessToken,
                expires_in: 10000,
                refresh_expires_in: 3000,
                token_type: 'jwt',
                'not-before-policy': 0,
                scope: 'bloqit',
            });

            jest.spyOn(mappedLockerRepository, 'retrieveBySharedId').mockResolvedValueOnce({
                bloqId: bloqitBloqId,
                sharedId: partnerLockerId,
                layout: [
                    {
                        column: 1,
                        row: 1,
                        lockerId: 'locker-id',
                        lockerTitle: 'locker-title',
                        partnerName: '1R1',
                        size: LockerType.M,
                    },
                ],
            });
            const handler = new InpostHandlerImpl({
                logger,
                inpostClient,
                bloqitClient,
                labelAssociationRepository,
                mappedLockerRepository,
            });

            await handler.maintenanceStatusUpdate({
                ...baseEventData,
                metadata: {
                    bloqExternalID: partnerBloqId,
                    lockerExternalID: 'locker-title',
                    previousState: 'none',
                    newState: 'triage',
                    reason: 'dirty_locker',
                    country: 'GB',
                },
            });

            expect(inpostClient.reportCompartmentStates).toHaveBeenCalledTimes(1);
            expect(inpostClient.reportCompartmentStates).toHaveBeenCalledWith(
                expect.objectContaining({ token: accessToken }),
                'GB',
                partnerBloqId,
                expect.objectContaining({
                    timestamp: expect.anything(),
                    sessionType: expect.any(String),
                    state: CompartmentState.Soiled,
                    names: ['1R1'],
                }),
            );
        });

        it('should notify a not soiled compartment when reason is dirty_locker and status is triage -> none', async () => {
            const logger = new FakeLogger();
            const inpostClient = new FakeInPostApiClient();
            const bloqitClient = new FakeBloqitClient();

            jest.spyOn(inpostClient, 'reportCompartmentStates').mockResolvedValueOnce({
                payload: '',
                exceptionMessage: '',
                exceptionKey: '',
                status: GlobalResponseModelStatus.OK,
                iid: '',
            });

            jest.spyOn(inpostClient, 'getAccessToken').mockResolvedValueOnce({
                access_token: accessToken,
                expires_in: 10000,
                refresh_expires_in: 3000,
                token_type: 'jwt',
                'not-before-policy': 0,
                scope: 'bloqit',
            });

            jest.spyOn(mappedLockerRepository, 'retrieveBySharedId').mockResolvedValueOnce({
                bloqId: bloqitBloqId,
                sharedId: partnerLockerId,
                layout: [
                    {
                        column: 1,
                        row: 1,
                        lockerId: 'locker-id',
                        lockerTitle: 'locker-title',
                        partnerName: '1R1',
                        size: LockerType.M,
                    },
                ],
            });

            const handler = new InpostHandlerImpl({
                logger,
                inpostClient,
                bloqitClient,
                labelAssociationRepository,
                mappedLockerRepository,
            });

            await handler.maintenanceStatusUpdate({
                ...baseEventData,
                metadata: {
                    bloqExternalID: partnerBloqId,
                    lockerExternalID: 'locker-title',
                    previousState: 'triage',
                    newState: 'none',
                    reason: 'dirty_locker',
                    country: 'GB',
                },
            });

            expect(inpostClient.reportCompartmentStates).toHaveBeenCalledTimes(1);
            expect(inpostClient.reportCompartmentStates).toHaveBeenCalledWith(
                expect.objectContaining({ token: accessToken }),
                'GB',
                partnerBloqId,
                expect.objectContaining({
                    timestamp: expect.anything(),
                    sessionType: expect.any(String),
                    state: CompartmentState.NotSoiled,
                    names: ['1R1'],
                }),
            );
        });

        it('should notify a damaged compartment when reason is doors_not_open and status is none -> triage', async () => {
            const logger = new FakeLogger();
            const inpostClient = new FakeInPostApiClient();
            const bloqitClient = new FakeBloqitClient();

            jest.spyOn(inpostClient, 'reportCompartmentStates').mockResolvedValueOnce({
                payload: '',
                exceptionMessage: '',
                exceptionKey: '',
                status: GlobalResponseModelStatus.OK,
                iid: '',
            });

            jest.spyOn(inpostClient, 'getAccessToken').mockResolvedValueOnce({
                access_token: accessToken,
                expires_in: 10000,
                refresh_expires_in: 3000,
                token_type: 'jwt',
                'not-before-policy': 0,
                scope: 'bloqit',
            });

            jest.spyOn(mappedLockerRepository, 'retrieveBySharedId').mockResolvedValueOnce({
                bloqId: bloqitBloqId,
                sharedId: partnerLockerId,
                layout: [
                    {
                        column: 1,
                        row: 1,
                        lockerId: 'locker-id',
                        lockerTitle: 'locker-title',
                        partnerName: '1R1',
                        size: LockerType.M,
                    },
                ],
            });

            const handler = new InpostHandlerImpl({
                logger,
                inpostClient,
                bloqitClient,
                labelAssociationRepository,
                mappedLockerRepository,
            });

            await handler.maintenanceStatusUpdate({
                ...baseEventData,
                metadata: {
                    bloqExternalID: partnerBloqId,
                    lockerExternalID: 'locker-title',
                    previousState: 'none',
                    newState: 'triage',
                    reason: 'doors_not_open',
                    country: 'GB',
                },
            });

            expect(inpostClient.reportCompartmentStates).toHaveBeenCalledTimes(1);
            expect(inpostClient.reportCompartmentStates).toHaveBeenCalledWith(
                expect.objectContaining({ token: accessToken }),
                'GB',
                partnerBloqId,
                expect.objectContaining({
                    timestamp: expect.anything(),
                    sessionType: expect.any(String),
                    state: CompartmentState.Damaged,
                    names: ['1R1'],
                }),
            );
        });

        it('should notify a damaged compartment when reason is doors_not_open and status is triage -> none', async () => {
            const logger = new FakeLogger();
            const inpostClient = new FakeInPostApiClient();
            const bloqitClient = new FakeBloqitClient();

            jest.spyOn(inpostClient, 'reportCompartmentStates').mockResolvedValueOnce({
                payload: '',
                exceptionMessage: '',
                exceptionKey: '',
                status: GlobalResponseModelStatus.OK,
                iid: '',
            });

            jest.spyOn(inpostClient, 'getAccessToken').mockResolvedValueOnce({
                access_token: accessToken,
                expires_in: 10000,
                refresh_expires_in: 3000,
                token_type: 'jwt',
                'not-before-policy': 0,
                scope: 'bloqit',
            });

            jest.spyOn(mappedLockerRepository, 'retrieveBySharedId').mockResolvedValueOnce({
                bloqId: bloqitBloqId,
                sharedId: partnerLockerId,
                layout: [
                    {
                        column: 1,
                        row: 1,
                        lockerId: 'locker-id',
                        lockerTitle: 'locker-title',
                        partnerName: '1R1',
                        size: LockerType.M,
                    },
                ],
            });

            const handler = new InpostHandlerImpl({
                logger,
                inpostClient,
                bloqitClient,
                labelAssociationRepository,
                mappedLockerRepository,
            });

            await handler.maintenanceStatusUpdate({
                ...baseEventData,
                metadata: {
                    bloqExternalID: partnerBloqId,
                    lockerExternalID: 'locker-title',
                    previousState: 'triage',
                    newState: 'none',
                    reason: 'doors_not_open',
                    country: 'GB',
                },
            });

            expect(inpostClient.reportCompartmentStates).toHaveBeenCalledTimes(1);
            expect(inpostClient.reportCompartmentStates).toHaveBeenCalledWith(
                expect.objectContaining({ token: accessToken }),
                'GB',
                partnerBloqId,
                expect.objectContaining({
                    timestamp: expect.anything(),
                    sessionType: expect.any(String),
                    state: CompartmentState.NotInspection,
                    names: ['1R1'],
                }),
            );
        });

        it('should notify a not inspection compartment when reason is damaged and status is triage -> none', async () => {
            const logger = new FakeLogger();
            const inpostClient = new FakeInPostApiClient();
            const bloqitClient = new FakeBloqitClient();

            jest.spyOn(inpostClient, 'reportCompartmentStates').mockResolvedValueOnce({
                payload: '',
                exceptionMessage: '',
                exceptionKey: '',
                status: GlobalResponseModelStatus.OK,
                iid: '',
            });

            jest.spyOn(inpostClient, 'getAccessToken').mockResolvedValueOnce({
                access_token: accessToken,
                expires_in: 10000,
                refresh_expires_in: 3000,
                token_type: 'jwt',
                'not-before-policy': 0,
                scope: 'bloqit',
            });

            jest.spyOn(mappedLockerRepository, 'retrieveBySharedId').mockResolvedValueOnce({
                bloqId: bloqitBloqId,
                sharedId: partnerLockerId,
                layout: [
                    {
                        column: 1,
                        row: 1,
                        lockerId: 'locker-id',
                        lockerTitle: 'locker-title',
                        partnerName: '1R1',
                        size: LockerType.M,
                    },
                ],
            });

            const handler = new InpostHandlerImpl({
                logger,
                inpostClient,
                bloqitClient,
                labelAssociationRepository,
                mappedLockerRepository,
            });

            await handler.maintenanceStatusUpdate({
                ...baseEventData,
                metadata: {
                    bloqExternalID: partnerBloqId,
                    lockerExternalID: 'locker-title',
                    previousState: 'triage',
                    newState: 'none',
                    reason: 'damaged',
                    country: 'GB',
                },
            });

            expect(inpostClient.reportCompartmentStates).toHaveBeenCalledTimes(1);
            expect(inpostClient.reportCompartmentStates).toHaveBeenCalledWith(
                expect.objectContaining({ token: accessToken }),
                'GB',
                partnerBloqId,
                expect.objectContaining({
                    timestamp: expect.anything(),
                    sessionType: expect.any(String),
                    state: CompartmentState.NotInspection,
                    names: ['1R1'],
                }),
            );
        });

        it('should notify a inspection compartment when reason is damaged and status is triage -> verification', async () => {
            const logger = new FakeLogger();
            const inpostClient = new FakeInPostApiClient();
            const bloqitClient = new FakeBloqitClient();

            jest.spyOn(inpostClient, 'reportCompartmentStates').mockResolvedValueOnce({
                payload: '',
                exceptionMessage: '',
                exceptionKey: '',
                status: GlobalResponseModelStatus.OK,
                iid: '',
            });

            jest.spyOn(inpostClient, 'getAccessToken').mockResolvedValueOnce({
                access_token: accessToken,
                expires_in: 10000,
                refresh_expires_in: 3000,
                token_type: 'jwt',
                'not-before-policy': 0,
                scope: 'bloqit',
            });

            jest.spyOn(mappedLockerRepository, 'retrieveBySharedId').mockResolvedValueOnce({
                bloqId: bloqitBloqId,
                sharedId: partnerLockerId,
                layout: [
                    {
                        column: 1,
                        row: 1,
                        lockerId: 'locker-id',
                        lockerTitle: 'locker-title',
                        partnerName: '1R1',
                        size: LockerType.M,
                    },
                ],
            });

            const handler = new InpostHandlerImpl({
                logger,
                inpostClient,
                bloqitClient,
                labelAssociationRepository,
                mappedLockerRepository,
            });

            await handler.maintenanceStatusUpdate({
                ...baseEventData,
                metadata: {
                    bloqExternalID: partnerBloqId,
                    lockerExternalID: 'locker-title',
                    previousState: 'triage',
                    newState: 'verification',
                    reason: 'damaged',
                    country: 'GB',
                },
            });

            expect(inpostClient.reportCompartmentStates).toHaveBeenCalledTimes(1);
            expect(inpostClient.reportCompartmentStates).toHaveBeenCalledWith(
                expect.objectContaining({ token: accessToken }),
                'GB',
                partnerBloqId,
                expect.objectContaining({
                    timestamp: expect.anything(),
                    sessionType: expect.any(String),
                    state: CompartmentState.Inspection,
                    names: ['1R1'],
                }),
            );
        });

        it('should notify a not damaged compartment when reason is damaged and status is verification -> none', async () => {
            const logger = new FakeLogger();
            const inpostClient = new FakeInPostApiClient();
            const bloqitClient = new FakeBloqitClient();

            jest.spyOn(inpostClient, 'reportCompartmentStates').mockResolvedValueOnce({
                payload: '',
                exceptionMessage: '',
                exceptionKey: '',
                status: GlobalResponseModelStatus.OK,
                iid: '',
            });

            jest.spyOn(inpostClient, 'getAccessToken').mockResolvedValueOnce({
                access_token: accessToken,
                expires_in: 10000,
                refresh_expires_in: 3000,
                token_type: 'jwt',
                'not-before-policy': 0,
                scope: 'bloqit',
            });

            jest.spyOn(mappedLockerRepository, 'retrieveBySharedId').mockResolvedValueOnce({
                bloqId: bloqitBloqId,
                sharedId: partnerLockerId,
                layout: [
                    {
                        column: 1,
                        row: 1,
                        lockerId: 'locker-id',
                        lockerTitle: 'locker-title',
                        partnerName: '1R1',
                        size: LockerType.M,
                    },
                ],
            });

            const handler = new InpostHandlerImpl({
                logger,
                inpostClient,
                bloqitClient,
                labelAssociationRepository,
                mappedLockerRepository,
            });

            await handler.maintenanceStatusUpdate({
                ...baseEventData,
                metadata: {
                    bloqExternalID: partnerBloqId,
                    lockerExternalID: 'locker-title',
                    previousState: 'verification',
                    newState: 'none',
                    reason: 'damaged',
                    country: 'GB',
                },
            });

            expect(inpostClient.reportCompartmentStates).toHaveBeenCalledTimes(1);
            expect(inpostClient.reportCompartmentStates).toHaveBeenCalledWith(
                expect.objectContaining({ token: accessToken }),
                'GB',
                partnerBloqId,
                expect.objectContaining({
                    timestamp: expect.anything(),
                    sessionType: expect.any(String),
                    state: CompartmentState.NotDamaged,
                    names: ['1R1'],
                }),
            );
        });

        it('should notify a verified compartment when reason is damaged and status is verification -> maintenance', async () => {
            const logger = new FakeLogger();
            const inpostClient = new FakeInPostApiClient();
            const bloqitClient = new FakeBloqitClient();

            jest.spyOn(inpostClient, 'reportCompartmentStates').mockResolvedValueOnce({
                payload: '',
                exceptionMessage: '',
                exceptionKey: '',
                status: GlobalResponseModelStatus.OK,
                iid: '',
            });

            jest.spyOn(inpostClient, 'getAccessToken').mockResolvedValueOnce({
                access_token: accessToken,
                expires_in: 10000,
                refresh_expires_in: 3000,
                token_type: 'jwt',
                'not-before-policy': 0,
                scope: 'bloqit',
            });

            jest.spyOn(mappedLockerRepository, 'retrieveBySharedId').mockResolvedValueOnce({
                bloqId: bloqitBloqId,
                sharedId: partnerLockerId,
                layout: [
                    {
                        column: 1,
                        row: 1,
                        lockerId: 'locker-id',
                        lockerTitle: 'locker-title',
                        partnerName: '1R1',
                        size: LockerType.M,
                    },
                ],
            });

            const handler = new InpostHandlerImpl({
                logger,
                inpostClient,
                bloqitClient,
                labelAssociationRepository,
                mappedLockerRepository,
            });

            await handler.maintenanceStatusUpdate({
                ...baseEventData,
                metadata: {
                    bloqExternalID: partnerBloqId,
                    lockerExternalID: 'locker-title',
                    previousState: 'verification',
                    newState: 'maintenance',
                    reason: 'damaged',
                    country: 'GB',
                },
            });

            expect(inpostClient.reportCompartmentStates).toHaveBeenCalledTimes(1);
            expect(inpostClient.reportCompartmentStates).toHaveBeenCalledWith(
                expect.objectContaining({ token: accessToken }),
                'GB',
                partnerBloqId,
                expect.objectContaining({
                    timestamp: expect.anything(),
                    sessionType: expect.any(String),
                    state: CompartmentState.Verified,
                    names: ['1R1'],
                }),
            );
        });

        it('should fail silently and log when an unknown state is received', async () => {
            const logger = new FakeLogger();
            const inpostClient = new FakeInPostApiClient();
            const bloqitClient = new FakeBloqitClient();

            jest.spyOn(inpostClient, 'getAccessToken').mockResolvedValueOnce({
                access_token: accessToken,
                expires_in: 10000,
                refresh_expires_in: 3000,
                token_type: 'jwt',
                'not-before-policy': 0,
                scope: 'bloqit',
            });

            jest.spyOn(logger, 'info').mockImplementation(() => {});

            jest.spyOn(mappedLockerRepository, 'retrieveBySharedId').mockResolvedValueOnce({
                bloqId: bloqitBloqId,
                sharedId: partnerLockerId,
                layout: [
                    {
                        column: 1,
                        row: 1,
                        lockerId: 'locker-id',
                        lockerTitle: 'locker-title',
                        partnerName: '1R1',
                        size: LockerType.M,
                    },
                ],
            });

            const handler = new InpostHandlerImpl({
                logger,
                inpostClient,
                bloqitClient,
                labelAssociationRepository,
                mappedLockerRepository,
            });

            await handler.maintenanceStatusUpdate({
                ...baseEventData,
                metadata: {
                    bloqExternalID: partnerBloqId,
                    lockerExternalID: partnerLockerId,
                    previousState: 'weird_state',
                    newState: 'strange_state',
                    reason: 'damaged',
                    country: 'GB',
                },
            });

            expect(inpostClient.reportCompartmentStates).not.toHaveBeenCalled();
            expect(logger.info).toHaveBeenCalledWith(
                expect.objectContaining({
                    message: 'Unknown locker state received: weird_state to strange_state',
                }),
            );
        });
    });

    describe('handleBloqCreatedEvent', () => {
        const testLayout: BloqLocker[] = [
            {
                type: LockerType.L,
                layout: { column: 1, row: 1, openDirection: 'left' },
                usageType: LockerUsageType.STORAGE,
                _id: 'locker-id-1',
                rent: null,
                isOpen: false,
                lockerTitle: 'locker-title-1',
                active: false,
                createdAt: '',
                updatedAt: '',
            },
            {
                type: LockerType.S,
                layout: { column: 1, row: 2, openDirection: 'left' },
                usageType: LockerUsageType.STORAGE,
                _id: 'locker-id-2',
                rent: null,
                isOpen: false,
                lockerTitle: 'locker-title-2',
                active: false,
                createdAt: '',
                updatedAt: '',
            },
            {
                type: LockerType.S,
                layout: { column: 1, row: 3, openDirection: 'left' },
                usageType: LockerUsageType.STORAGE,
                _id: 'locker-id-3',
                rent: null,
                isOpen: false,
                lockerTitle: 'locker-title-3',
                active: false,
                createdAt: '',
                updatedAt: '',
            },
            {
                type: LockerType.S,
                layout: { column: 1, row: 4, openDirection: 'left' },
                usageType: LockerUsageType.STORAGE,
                _id: 'locker-id-4',
                rent: null,
                isOpen: false,
                lockerTitle: 'locker-title-4',
                active: false,
                createdAt: '',
                updatedAt: '',
            },
            {
                type: LockerType.XL,
                layout: { column: 1, row: 5, openDirection: 'left' },
                usageType: LockerUsageType.ECU,
                _id: 'locker-id-5',
                rent: null,
                isOpen: false,
                lockerTitle: 'locker-title-5',
                active: false,
                createdAt: '',
                updatedAt: '',
            },
            {
                type: LockerType.XL,
                layout: { column: 1, row: 6, openDirection: 'left' },
                usageType: LockerUsageType.BATTERY,
                _id: 'locker-id-6',
                rent: null,
                isOpen: false,
                lockerTitle: 'locker-title-6',
                active: false,
                createdAt: '',
                updatedAt: '',
            },
            {
                type: LockerType.L,
                layout: { column: 2, row: 1, openDirection: 'left' },
                usageType: LockerUsageType.STORAGE,
                _id: 'locker-id-7',
                rent: null,
                isOpen: false,
                lockerTitle: 'locker-title-7',
                active: false,
                createdAt: '',
                updatedAt: '',
            },
            {
                type: LockerType.S,
                layout: { column: 2, row: 2, openDirection: 'left' },
                usageType: LockerUsageType.STORAGE,
                _id: 'locker-id-8',
                rent: null,
                isOpen: false,
                lockerTitle: 'locker-title-8',
                active: false,
                createdAt: '',
                updatedAt: '',
            },
            {
                type: LockerType.S,
                layout: { column: 2, row: 3, openDirection: 'left' },
                usageType: LockerUsageType.STORAGE,
                _id: 'locker-id-9',
                rent: null,
                isOpen: false,
                lockerTitle: 'locker-title-9',
                active: false,
                createdAt: '',
                updatedAt: '',
            },
            {
                type: LockerType.S,
                layout: { column: 2, row: 4, openDirection: 'left' },
                usageType: LockerUsageType.STORAGE,
                _id: 'locker-id-10',
                rent: null,
                isOpen: false,
                lockerTitle: 'locker-title-10',
                active: false,
                createdAt: '',
                updatedAt: '',
            },
            {
                type: LockerType.S,
                layout: { column: 2, row: 5, openDirection: 'left' },
                usageType: LockerUsageType.STORAGE,
                _id: 'locker-id-11',
                rent: null,
                isOpen: false,
                lockerTitle: 'locker-title-11',
                active: false,
                createdAt: '',
                updatedAt: '',
            },
            {
                type: LockerType.S,
                layout: { column: 2, row: 6, openDirection: 'left' },
                usageType: LockerUsageType.STORAGE,
                _id: 'locker-id-12',
                rent: null,
                isOpen: false,
                lockerTitle: 'locker-title-12',
                active: false,
                createdAt: '',
                updatedAt: '',
            },
            {
                type: LockerType.M,
                layout: { column: 2, row: 7, openDirection: 'left' },
                usageType: LockerUsageType.STORAGE,
                _id: 'locker-id-13',
                rent: null,
                isOpen: false,
                lockerTitle: 'locker-title-13',
                active: false,
                createdAt: '',
                updatedAt: '',
            },
            {
                type: LockerType.M,
                layout: { column: 2, row: 8, openDirection: 'left' },
                usageType: LockerUsageType.STORAGE,
                _id: 'locker-id-14',
                rent: null,
                isOpen: false,
                lockerTitle: 'locker-title-14',
                active: false,
                createdAt: '',
                updatedAt: '',
            },
            {
                type: LockerType.M,
                layout: { column: 2, row: 9, openDirection: 'left' },
                usageType: LockerUsageType.STORAGE,
                _id: 'locker-id-15',
                rent: null,
                isOpen: false,
                lockerTitle: 'locker-title-15',
                active: false,
                createdAt: '',
                updatedAt: '',
            },
            {
                type: LockerType.M,
                layout: { column: 2, row: 10, openDirection: 'left' },
                usageType: LockerUsageType.STORAGE,
                _id: 'locker-id-16',
                rent: null,
                isOpen: false,
                lockerTitle: 'locker-title-16',
                active: false,
                createdAt: '',
                updatedAt: '',
            },
            {
                type: LockerType.L,
                layout: { column: 3, row: 1, openDirection: 'left' },
                usageType: LockerUsageType.STORAGE,
                _id: 'locker-id-17',
                rent: null,
                isOpen: false,
                lockerTitle: 'locker-title-17',
                active: false,
                createdAt: '',
                updatedAt: '',
            },
            {
                type: LockerType.S,
                layout: { column: 3, row: 2, openDirection: 'left' },
                usageType: LockerUsageType.STORAGE,
                _id: 'locker-id-18',
                rent: null,
                isOpen: false,
                lockerTitle: 'locker-title-18',
                active: false,
                createdAt: '',
                updatedAt: '',
            },
            {
                type: LockerType.S,
                layout: { column: 3, row: 3, openDirection: 'left' },
                usageType: LockerUsageType.STORAGE,
                _id: 'locker-id-19',
                rent: null,
                isOpen: false,
                lockerTitle: 'locker-title-19',
                active: false,
                createdAt: '',
                updatedAt: '',
            },
            {
                type: LockerType.S,
                layout: { column: 3, row: 4, openDirection: 'left' },
                usageType: LockerUsageType.STORAGE,
                _id: 'locker-id-20',
                rent: null,
                isOpen: false,
                lockerTitle: 'locker-title-20',
                active: false,
                createdAt: '',
                updatedAt: '',
            },
            {
                type: LockerType.S,
                layout: { column: 3, row: 5, openDirection: 'left' },
                usageType: LockerUsageType.STORAGE,
                _id: 'locker-id-21',
                rent: null,
                isOpen: false,
                lockerTitle: 'locker-title-21',
                active: false,
                createdAt: '',
                updatedAt: '',
            },
            {
                type: LockerType.S,
                layout: { column: 3, row: 6, openDirection: 'left' },
                usageType: LockerUsageType.STORAGE,
                _id: 'locker-id-22',
                rent: null,
                isOpen: false,
                lockerTitle: 'locker-title-22',
                active: false,
                createdAt: '',
                updatedAt: '',
            },
            {
                type: LockerType.M,
                layout: { column: 3, row: 7, openDirection: 'left' },
                usageType: LockerUsageType.STORAGE,
                _id: 'locker-id-23',
                rent: null,
                isOpen: false,
                lockerTitle: 'locker-title-23',
                active: false,
                createdAt: '',
                updatedAt: '',
            },
            {
                type: LockerType.M,
                layout: { column: 3, row: 8, openDirection: 'left' },
                usageType: LockerUsageType.STORAGE,
                _id: 'locker-id-24',
                rent: null,
                isOpen: false,
                lockerTitle: 'locker-title-24',
                active: false,
                createdAt: '',
                updatedAt: '',
            },
            {
                type: LockerType.L,
                layout: { column: 3, row: 9, openDirection: 'left' },
                usageType: LockerUsageType.STORAGE,
                _id: 'locker-id-25',
                rent: null,
                isOpen: false,
                lockerTitle: 'locker-title-25',
                active: false,
                createdAt: '',
                updatedAt: '',
            },
            {
                type: LockerType.L,
                layout: { column: 4, row: 1, openDirection: 'left' },
                usageType: LockerUsageType.STORAGE,
                _id: 'locker-id-26',
                rent: null,
                isOpen: false,
                lockerTitle: 'locker-title-26',
                active: false,
                createdAt: '',
                updatedAt: '',
            },
            {
                type: LockerType.S,
                layout: { column: 4, row: 2, openDirection: 'left' },
                usageType: LockerUsageType.STORAGE,
                _id: 'locker-id-27',
                rent: null,
                isOpen: false,
                lockerTitle: 'locker-title-27',
                active: false,
                createdAt: '',
                updatedAt: '',
            },
            {
                type: LockerType.S,
                layout: { column: 4, row: 3, openDirection: 'left' },
                usageType: LockerUsageType.STORAGE,
                _id: 'locker-id-28',
                rent: null,
                isOpen: false,
                lockerTitle: 'locker-title-28',
                active: false,
                createdAt: '',
                updatedAt: '',
            },
            {
                type: LockerType.S,
                layout: { column: 4, row: 4, openDirection: 'left' },
                usageType: LockerUsageType.STORAGE,
                _id: 'locker-id-29',
                rent: null,
                isOpen: false,
                lockerTitle: 'locker-title-29',
                active: false,
                createdAt: '',
                updatedAt: '',
            },
            {
                type: LockerType.M,
                layout: { column: 4, row: 5, openDirection: 'left' },
                usageType: LockerUsageType.STORAGE,
                _id: 'locker-id-30',
                rent: null,
                isOpen: false,
                lockerTitle: 'locker-title-30',
                active: false,
                createdAt: '',
                updatedAt: '',
            },
            {
                type: LockerType.M,
                layout: { column: 4, row: 6, openDirection: 'left' },
                usageType: LockerUsageType.STORAGE,
                _id: 'locker-id-31',
                rent: null,
                isOpen: false,
                lockerTitle: 'locker-title-31',
                active: false,
                createdAt: '',
                updatedAt: '',
            },
            {
                type: LockerType.M,
                layout: { column: 4, row: 7, openDirection: 'left' },
                usageType: LockerUsageType.STORAGE,
                _id: 'locker-id-32',
                rent: null,
                isOpen: false,
                lockerTitle: 'locker-title-32',
                active: false,
                createdAt: '',
                updatedAt: '',
            },
            {
                type: LockerType.M,
                layout: { column: 4, row: 8, openDirection: 'left' },
                usageType: LockerUsageType.STORAGE,
                _id: 'locker-id-33',
                rent: null,
                isOpen: false,
                lockerTitle: 'locker-title-33',
                active: false,
                createdAt: '',
                updatedAt: '',
            },
            {
                type: LockerType.M,
                layout: { column: 4, row: 9, openDirection: 'left' },
                usageType: LockerUsageType.STORAGE,
                _id: 'locker-id-34',
                rent: null,
                isOpen: false,
                lockerTitle: 'locker-title-34',
                active: false,
                createdAt: '',
                updatedAt: '',
            },
        ];

        beforeEach(() => {
            jest.clearAllMocks();
        });

        it('should handle bloq created event', async () => {
            const logger = new FakeLogger();
            const inpostClient = new FakeInPostApiClient();
            const bloqitClient = new FakeBloqitClient();

            jest.spyOn(inpostClient, 'getAccessToken').mockResolvedValueOnce({
                access_token: accessToken,
                expires_in: 10000,
                refresh_expires_in: 3000,
                token_type: 'jwt',
                'not-before-policy': 0,
                scope: 'bloqit',
            });

            jest.spyOn(bloqitClient, 'retrieveBloqById').mockResolvedValueOnce({
                _id: bloqitBloqId,
                active: true,
                externalId: bloqitPartnerId,
                lockers: testLayout,
                details: {
                    country: 'FR',
                },
            });

            jest.spyOn(inpostClient, 'setBoxMachineLayout').mockResolvedValueOnce(undefined);

            jest.spyOn(mappedLockerRepository, 'create').mockResolvedValueOnce(undefined);

            const handler = new InpostHandlerImpl({
                logger,
                inpostClient,
                bloqitClient,
                labelAssociationRepository,
                mappedLockerRepository,
            });

            await handler.handleBloqCreatedEvent({
                bloq: partnerBloqId,
                timestamp: new Date(),
                metadata: { bloqExternalID: partnerBloqId },
                actionInitiatedBy: { role: Roles.CUSTOMER, id: 'customer-id-123' },
            });

            expect(inpostClient.getAccessToken).toHaveBeenCalledTimes(1);
            expect(mappedLockerRepository.create).toHaveBeenCalledTimes(1);
            expect(inpostClient.setBoxMachineLayout).toHaveBeenCalledTimes(1);
        });

        it('should handle bloq updated event', async () => {
            const logger = new FakeLogger();
            const inpostClient = new FakeInPostApiClient();
            const bloqitClient = new FakeBloqitClient();

            jest.spyOn(inpostClient, 'getAccessToken').mockResolvedValueOnce({
                access_token: accessToken,
                expires_in: 10000,
                refresh_expires_in: 3000,
                token_type: 'jwt',
                'not-before-policy': 0,
                scope: 'bloqit',
            });

            jest.spyOn(bloqitClient, 'retrieveBloqById').mockResolvedValueOnce({
                _id: bloqitBloqId,
                active: true,
                externalId: bloqitPartnerId,
                lockers: testLayout,
                details: {
                    country: 'FR',
                },
            });

            jest.spyOn(inpostClient, 'setBoxMachineLayout').mockResolvedValueOnce(undefined);

            jest.spyOn(mappedLockerRepository, 'remove').mockResolvedValueOnce(undefined);
            jest.spyOn(mappedLockerRepository, 'create').mockResolvedValueOnce(undefined);

            const handler = new InpostHandlerImpl({
                logger,
                inpostClient,
                bloqitClient,
                labelAssociationRepository,
                mappedLockerRepository,
            });

            await handler.handleBloqUpdatedEvent({
                bloq: partnerBloqId,
                timestamp: new Date(),
                metadata: { bloqExternalID: partnerBloqId },
                actionInitiatedBy: { role: Roles.CUSTOMER, id: 'customer-id-123' },
            });

            expect(inpostClient.getAccessToken).toHaveBeenCalledTimes(1);
            expect(mappedLockerRepository.remove).toHaveBeenCalledTimes(1);
            expect(mappedLockerRepository.create).toHaveBeenCalledTimes(1);
            expect(inpostClient.setBoxMachineLayout).toHaveBeenCalledTimes(1);
        });
    });
});
