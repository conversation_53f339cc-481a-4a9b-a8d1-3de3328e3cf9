export enum Status {
    OK = 'OK',
    ERROR = 'ERROR',
}

/**
 * @description Interface for DHL CZ data response
 * @param {string} sessionId - UUID string which is uniquely assigned to given request. It is used for end to end tracking in all systems.
 * @param {string} statusMessage - Text status description
 * @param {string} status - [OK|ERROR] providing status of request
 * @param {string} timestamp - Timestamp when request was processed by backend system. Date time format: yyyy-MM-dd’T’HH:mm:ss.SSSZ
 * @param {string} statusCode - Numeric status code
 * @interface DHLCZDataResponse
 * @exports DHLCZDataResponse
 */
export interface DHLCZDataResponse {
    sessionId: string;
    statusMessage: string;
    status: Status;
    timestamp: string;
    statusCode: string;
}
