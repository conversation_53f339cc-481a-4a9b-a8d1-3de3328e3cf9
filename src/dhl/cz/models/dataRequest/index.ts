/**
 * @description Identification of external partner
 * @const {string} 'BLOQIT'
 */
export const ExternalPartner = 'BLOQIT';

/**
 * @description Business type of user request
 * @enum {string} ['PPL', 'DHLE']
 */
export enum BusinessType {
    PPL = 'PPL',
    DHLE = 'DHLE',
}

/**
 * @description Operation type of user request
 * @enum {string} ['FM', 'LM']
 */
export enum OperationType {
    FIRST_MILE = 'FM',
    LAST_MILE = 'LM',
}

/**
 * @description Locale of user request, two letter representation
 * @enum {string} ['cz', 'en']
 */
export enum ApplicationLocale {
    cz = 'cz',
    en = 'en',
}

/**
 * @description Requested role by access point
 * @enum {string} ['courier']
 */
export enum AccessRole {
    courier = 'courier',
}

/**
 * @description data request command for DHL CZ
 * @param {string} externalPartner - Identification of external partner, like “DATASYS”
 * @param {BusinessType} businessType - [PPL|DHLE] PPL or DHL Express or other business type
 * @param {OperationType} operationType - [FM|LM] First or last mile operation
 * @param {string} accessPointId - Access Point (Parcel Box) identifier as defined in PPL EPS system
 * @param {string} slotId - Placement identifier withing the access point
 * @param {string} driverId - Driver unique id – only incase that driver/courier is logged to Access point
 * @param {string} parcelIdentifier - Package number
 * @param {string} sessionId - UUID string which is uniquely assigned to given request. It is used for end-to-end tracking in all systems.
 * @param {ApplicationLocale} applicationLocale - [cz|en] Locale of user request, two letter representation
 */
export interface DHLCZDataRequest {
    externalPartner: string;
    businessType: BusinessType;
    operationType?: OperationType;
    accessPointId: string;
    slotId?: string;
    driverId?: string;
    parcelIdentifier?: string;
    sessionId: string;
    applicationLocale?: ApplicationLocale;
    accessRole?: AccessRole.courier;
}
