/**
 * @description configuration command
 * @enum {string} ['activate', 'deactivate', 'update']
 */
export enum Command {
    activate = 'activate',
    deactivate = 'deactivate',
    update = 'update',
}

/**
 * @description GPS coordinates
 * @interface
 * @property {number} Lat - latitude
 * @property {number} Lon - longitude
 */
export interface GPS {
    Lat: number;
    Lon: number;
}

/**
 * @description POS terminal
 * @interface
 * @property {number} SN - serial number
 * @property {number} TID - terminal ID
 */
export interface PosTerminal {
    SN: number;
    TID: number;
}

export type ConfigureParameters = Record<string, unknown>;

/**
 * @description configuration command for DHL CZ
 */
export interface DHLCZDataConfig {
    command: Command;
    deviceId?: string;
    gps?: GPS;
    posTerminal?: PosTerminal;
    address?: string;
    parameters?: ConfigureParameters;
}
