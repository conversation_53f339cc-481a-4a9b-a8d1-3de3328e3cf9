export enum ProductType {
    parcelConnect = 'ParcelEurope.parcelconnect',
    europaket = 'ParcelEurope.europaket',
}

export enum DHLCZEventCodes {
    customerDelivery = 'EMA',
    driverPickUp = 'E1B',
    driverDelivery = 'E3L',
    customerPickUp = 'EMI',
    parcelExpired = 'EDX',
    parcelStuck = 'E3K',
    ping = 'E3K',
    violence = 'E3K',
}

export enum DHLCZCountryCodes {
    CZ = 'CZ',
}

export enum DHLCZLocationCodes {
    parcelStation = 'parcelStation',
}

export enum DHLCZReasons {
    stuck = 'R604',
    ping = 'R138',
    violence = 'R139',
    associateLabel = '10',
}
export interface DocumentElement {
    type: string;
    reference: string;
}

export interface ParcelEuropeSpecificData {
    eventCode: DHLCZEventCodes;
    country: DHLCZCountryCodes;
    location: DHLCZLocationCodes;
    document: DocumentElement[];
    reason?: DHLCZReasons;
    deliveryAddress?: {
        email: string;
        type?: DHLCZLocationCodes;
    };
}

export interface DataElementGeneral {
    product: ProductType;
    parcelIdentifier: string[];
    routingCode: string;
    timestamp: Date;
}

export interface DHLCZDataElementTemplate {
    version: string;
    dataElementType: string;
    parcelOriginOrganization: string;
    parcelDestinationOrganization: string;
    dataElementOriginOrganization: string;
    distributionTimestamp: Date;
    general: DataElementGeneral;
}

/**
 * @description data element for DHL CZ
 * @param {string} version - constant - “0200”
 * @param {string} dataElementType - indicating type of document, const always set to xPAN
 * @param {string} parcelOriginOrganization - constant – “CZ-7001”
 * @param {string} parcelDestinationOrganization - constant – “CZ-7001”
 * @param {string} dataElementOriginOrganization - constant - “CZ-7001”
 * @param {Date} distributionTimestamp - time when message was created
 * @param {string} parcelIdentifier - package ID
 * @param {string} routingCode - constant - "2LCZ35002+********"
 * @param {Date} timestamp - time when data was retrieved
 * @param {DataElementGeneral} - object describing the product type
 */
export interface DHLCZDataElement extends DHLCZDataElementTemplate {
    event: { parcelEuropeSpecificData: ParcelEuropeSpecificData };
}
