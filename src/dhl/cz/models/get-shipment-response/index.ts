import { type DHLCZDataResponse } from '../dataResponse';

export enum ProductType {
    ParcelConnect = 'ParcelEurope.parcelconnect',
    Europak = 'ParcelEurope.europak',
}

export enum PaymentStatus {
    paid = 'paid',
    toPay = 'toPay',
}
/**
 * @description Object containing response metadata
 * @interface
 * @property
 * @property xPAN Object containing information about the package
 * @property features Object containing information about the package features
 */
export interface DataElement {
    /** @property {string} version Version of the data element - Contant: "0200" */
    version: string;

    /** @property {string} dataElementType Type of the data element - Contant: "xPAN" */
    dataElementType: string;

    /** @property {string} parcelOriginOrganization Origin organization of the parcel - Contant: "CZ-7001" */
    parcelOriginOrganization: string;

    /** @property {string} parcelDestinationOrganization Destination organization of the parcel - Contant: "CZ-7001" */
    parcelDestinationOrganization: string;

    /** @property {string} dataElementOriginOrganization Origin organization of the data element - Contant: "CZ-7001" */
    dataElementOriginOrganization: string;

    /** @property {string} distributionTimestamp Date string containing the timestamp of when the message was created. format: yyyy-MM-dd’T’HH:mm:ss.SSSZ */
    distributionTimestamp: string;

    general: {
        /** @property {ProductType} product Object containing general information about the package */
        product: ProductType;

        /** @property {[string]} parcelIdentifier Package identifier */
        parcelIdentifier: string[];

        /** @property {string} routingCode Routing code of the package - Contant: "2LCZ35002+********" */
        routingCode: string;

        /** @property {string} timestamp timestamp Time when data was retrieved */
        timestamp: string;
    };

    xPAN: {
        features: {
            cod: {
                nonSepa: {
                    /** @property {string} amount Amount for COD package */
                    amount: string;

                    /** @property {string} currency Currency for COD package */
                    currency: string;
                };
            };

            identityCheck: {
                /** @property {string} trueOrFalse Indicator if identity check is required */
                trueOrFalse: 'true' | 'false';

                /** @property {string} typeOfDocument Type of identification */
                typeOfDocument: string; // [PIN] Type of identification

                /** @property {string} documentIdentifcationNr Identification number (like PIN) (note: typo in attribute name is part of PEU standard) */
                documentIdentifcationNr: string;
            };

            physical: {
                /** @property {string} grossWeight Gross weight of the package */
                grossWeight: string;

                /** @property {string} length Length of the package */
                length: string;

                /** @property {string} width Width of the package */
                width: string;

                /** @property {string} height Height of the package */
                height: string;
            };
        };
        addresses: {
            sender: [
                {
                    /** @property {string} type type of address - constant "sender" (Note: provided only for FM (first mile)) */
                    type: string;
                    name: string;
                    email: string;
                    // *: other detail attributes of address (Note: provided only for FM (first mile))
                    [x: string | number | symbol]: unknown;
                },
            ];
            recipient: [
                {
                    /** @property {string} type type of address - constant "recipient" (Note: provided only for LM (last mile)) */
                    type: string;
                    name: string;
                    email: string;
                    // *: other detail attributes of address (Note: provided only for LM (last mile))
                    [x: string | number | symbol]: unknown;
                },
            ];
        };
        featureCodes: {
            /** 
             * @property {Array} feature Array containing the business features of the parcel
             * @example
             *  feature: [
                    { name: ['SmartPIN'], textValue: ['ABC123'] },
                    { name: ['LabelLess'], textValue: ['true'] },
                    { name: ['BiggestPsSize'], textValue: ['XXL'] },
                ]
            */
            feature: Array<{ name: [string]; textValue: [string] }>;
        };
        hierarchy?: {
            operations: {
                referenceParent: {
                    /** @property {string} identifier Identifier of parent package. If this identifier is same like identifier in general section, then it is parent package */
                    identifier: string;
                };
                referenceChild: {
                    /** @property {Array} identifier Array of package identifiers. If one of these identifier is same like identifier in general section, then it is child package */
                    identifier: [];

                    /** @property {number} numberOfChildren Number of children */
                    numberOfChildren: number;
                };
                /** @property {null} packagingType Reserved for future use */
                packagingType: null;
            };
        };
    };
    payment: {
        /** @property {'paid' | 'toPay'} paymentStatus information about COD payment status. For non COD the attribute is missing */
        paymentStatus: 'paid' | 'toPay';
    };
}

/**
 * @description Payload containing the response from DHL to the GetShipment call
 */
export interface GetShipmentResponse {
    dataResponse: DHLCZDataResponse;
    dataElement: DataElement;
}
