import { type RequestHandler, type Router } from 'express';
import { DHLCZBloqsController } from './controller';
import { BloqitClientImpl } from '../../../bloqit/sdk/impl';
import { type Logger } from '../../../core/logger';
import { type IdMappingRepository } from '../../../core/identifier-mapping/repository';
import { type HTTPClient } from '../../../core/http-client';
import { type ApplicationEnvironment } from '../../../core/env';

export const setup = (deps: {
    router: Router;
    httpClient: HTTPClient;
    idMappingRepository: IdMappingRepository;
    logger: Logger;
    env: ApplicationEnvironment;
}): Router => {
    const { env, router, idMappingRepository, httpClient, logger } = deps;

    const apiKey = env.DHL_CZ_BLOQIT_API_KEY;
    const bloqitClient = new BloqitClientImpl({ httpClient, logger, apiKey });
    const ctrl = new DHLCZBloqsController({
        logger,
        idMappingRepository,
        bloqitClient,
        friendlyId: process.env.DHL_CZ_FRIENDLY_ID ?? 'dhl_cz',
    });

    router.post('/configure', ctrl.updateConfig as unknown as RequestHandler);
    router.post('/occupancy', ctrl.getOccupancy as unknown as RequestHandler);

    return router;
};
