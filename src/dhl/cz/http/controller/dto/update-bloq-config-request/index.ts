export enum UpdateBloqConfigCommand {
    ACTIVATE = 'activate',
    DEACTIVATE = 'deactivate',
    UPDATE = 'update',
}

export interface UpdateBloqConfigRequestBody {
    dataConfig: {
        /** @property {'activate' | 'deactivate'} Command Specifies the command to be applied to the bloq */
        command: string;

        /** @property {string} DeviceId Device identification on external partner site */
        deviceId: string;

        /** @property {object} GPS GPS coordinates of parcel box */
        gps: {
            Lat: string;
            Lon: string;
        };

        posTerminal: {
            /** @property {string} TID_POS TID of payment terminal of parcel box for which is action requested */
            TID: string;

            /** @property {string} SN_POS description: SN of payment terminal of parcel box for which is action requested */
            SN: string;
        };

        /** @property {Array<{ name: string; value?: string }>} generic structure to store configuration parameters */
        parameters?: Array<{ name: string; value?: string }>;

        /** @property {object} Address address of customer related to the parcel box */
        address?: {
            type?: string;
            firstName?: string;
            name?: string;
            additionalName?: string;
            mobileNr?: string;
            phoneNr?: string;
            email?: string;
            street1?: string;
            street1Nr?: string;
            street2?: string;
            street2Nr?: string;
            accessPointID?: string;
            postcode?: string;
            city?: string;
            country?: string;
        };
    };

    dataRequest: {
        /** @property {string} externalPartner Identification of external partner */
        externalPartner: string;

        /** @property {'PPL'|'DHLE'} businessType business type */
        businessType: string;

        /** @property {string} accessPointId Parcel Box identifier */
        accessPointId: string;

        /** @property {string} sessionId Session identifier UUID */
        sessionId: string;

        /** @property {string} slotId Placement identifier within the access point */
        slotId?: string;

        /** @property {string} applicationLocale locale for user request */
        applicationLocale?: string;
    };
}
