import { type Request } from 'express';

import * as httpCodes from '../../../../http/models/codes';
import { type BloqitClient } from '../../../../bloqit/sdk';
import { type HTTPRequest, type HTTPResponse } from '../../../../http/models/extensions/express';
import {
    IdMappingDirection,
    type IdMappingRepository,
} from '../../../../core/identifier-mapping/repository';
import { type Logger } from '../../../../core/logger';
import {
    UpdateBloqConfigCommand,
    type UpdateBloqConfigRequestBody,
} from './dto/update-bloq-config-request';
import type { LockerObjectResponse } from '../../../../bloqit/lockers/models';
import { type DHLCZDataResponse, Status } from '../../models/dataResponse';

export interface UpdateBloqConfigRequest extends HTTPRequest {
    body: UpdateBloqConfigRequestBody;
}

export interface OccupancyRequest extends Request {
    body: {
        dataRequest: {
            sessionId: string;
            accessPointId: string;
        };
    };
}

export interface Slots {
    SlotID: string;
    parcelIdentifier?: [string] | undefined;
}

export interface DataOccupancy {
    slots: Slots[] | [];
}
export interface OccupancyResult {
    dataResponse: DHLCZDataResponse;
    dataOccupancy: DataOccupancy;
}

export class DHLCZBloqsController {
    private readonly friendlyId: string;
    private readonly bloqitClient: BloqitClient;
    private readonly idMappingRepository: IdMappingRepository;
    private readonly logger: Logger;

    constructor(deps: {
        friendlyId: string;
        bloqitClient: BloqitClient;
        idMappingRepository: IdMappingRepository;
        logger: Logger;
    }) {
        this.friendlyId = deps.friendlyId;
        this.bloqitClient = deps.bloqitClient;
        this.idMappingRepository = deps.idMappingRepository;
        this.logger = deps.logger;

        this.updateConfig = this.updateConfig.bind(this);
        this.getOccupancy = this.getOccupancy.bind(this);
    }

    async updateConfig(req: UpdateBloqConfigRequest, res: HTTPResponse): Promise<HTTPResponse> {
        try {
            const { dataConfig, dataRequest } = req.body;
            const sharedId = dataConfig.deviceId;
            const sessionId = dataRequest?.sessionId;

            const dataResponse: DHLCZDataResponse = {
                sessionId: sessionId ?? '',
                statusMessage: Status.ERROR.toString(),
                status: Status.ERROR,
                timestamp: new Date().toISOString(),
                statusCode: httpCodes.INTERNAL_SERVER_ERROR.toString(),
            };

            const bloqitBloqId = await this.idMappingRepository.fetch({
                direction: IdMappingDirection.BLOQIT,
                matching: { sharedId },
            });

            if (bloqitBloqId === undefined) {
                this.logger.error({
                    message:
                        'Failed to process updateConfig request. No matching id was found on bloqit side',
                });
                dataResponse.statusMessage = 'No matching id was found';
                return res.status(httpCodes.INTERNAL_SERVER_ERROR).json({ dataResponse });
            }

            if (
                ![
                    UpdateBloqConfigCommand.ACTIVATE.toString(),
                    UpdateBloqConfigCommand.DEACTIVATE.toString(),
                    UpdateBloqConfigCommand.UPDATE.toString(),
                ].includes(dataConfig.command)
            ) {
                this.logger.error({
                    message: `Failed to process updateConfig request. An invalid command was provided. Expected: activate | deactivate, Received: ${dataConfig.command}`,
                });
                dataResponse.statusMessage = 'An invalid command was provided';
                dataResponse.statusCode = httpCodes.METHOD_NOT_ALLOWED.toString();
                return res.status(httpCodes.METHOD_NOT_ALLOWED).json({ dataResponse });
            }

            await this.idMappingRepository.patchBloqIdMapping({
                matching: { sharedId, bloqitId: bloqitBloqId, partnerFriendlyId: this.friendlyId },
                append: { partnerId: dataRequest.accessPointId },
            });

            switch (dataConfig.command) {
                case UpdateBloqConfigCommand.ACTIVATE:
                    await this.bloqitClient.activateBloq({ bloqId: bloqitBloqId });
                    break;
                case UpdateBloqConfigCommand.DEACTIVATE:
                    await this.bloqitClient.deactivateBloq({ bloqId: bloqitBloqId });
                    break;
                case UpdateBloqConfigCommand.UPDATE:
                    await this.bloqitClient.updateBloqMetadata({
                        bloqId: bloqitBloqId,
                        metadata: { PPL_CUST_ID: dataRequest.accessPointId },
                    });
                    break;
                default:
                    break;
            }

            dataResponse.statusMessage = Status.OK.toString();
            dataResponse.status = Status.OK;
            dataResponse.statusCode = httpCodes.OK.toString();
            return res.json({ dataResponse });
        } catch (ex) {
            this.logger.error({ message: (ex as Error).message });
            return res.status(httpCodes.INTERNAL_SERVER_ERROR).send();
        }
    }

    async getOccupancy(req: OccupancyRequest, res: HTTPResponse): Promise<HTTPResponse> {
        const { dataRequest } = req.body;
        const accessPointId = dataRequest?.accessPointId;
        const sessionId = dataRequest?.sessionId;
        const result: OccupancyResult = {
            dataResponse: {
                sessionId: sessionId ?? '',
                statusMessage: 'Invalid input (no accessPointId)',
                status: Status.ERROR,
                timestamp: new Date().toISOString(),
                statusCode: httpCodes.METHOD_NOT_ALLOWED.toString(),
            },
            dataOccupancy: {
                slots: [],
            },
        };

        if ([undefined, ''].includes(accessPointId)) {
            return res.status(httpCodes.METHOD_NOT_ALLOWED).json(result);
        }

        const bloqId = await this.idMappingRepository.fetch({
            direction: IdMappingDirection.BLOQIT,
            matching: { partnerId: accessPointId },
        });

        if (bloqId === null || bloqId === undefined) {
            return res.status(httpCodes.METHOD_NOT_ALLOWED).json(result);
        }

        const response: LockerObjectResponse = await this.bloqitClient.getOccupancy({ bloqId });
        if (response.status !== httpCodes.OK) {
            return res
                .status(httpCodes.INTERNAL_SERVER_ERROR)
                .json({ description: 'Internal Error' });
        }

        const slots: Slots[] = [];
        for (const locker of response.lockerObjects) {
            const lockerTitle = locker.lockerTitle;

            if (locker.rent === null || locker.rent === undefined) {
                slots.push({
                    SlotID: lockerTitle,
                    parcelIdentifier: undefined,
                });
                continue;
            }

            const parcelIdentifier = await this.idMappingRepository.fetch({
                direction: IdMappingDirection.PARTNER,
                matching: { bloqitId: locker.rent },
            });

            slots.push({
                SlotID: lockerTitle,
                parcelIdentifier: parcelIdentifier !== undefined ? [parcelIdentifier] : undefined,
            });
        }
        result.dataOccupancy.slots = slots;
        result.dataResponse.statusMessage = Status.OK.toString();
        result.dataResponse.status = Status.OK;
        result.dataResponse.statusCode = httpCodes.OK.toString();
        return res.json(result);
    }
}
