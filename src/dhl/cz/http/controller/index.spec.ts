import { DHLCZBloqsController } from '.';
import {
    UpdateBloqConfigCommand,
    type UpdateBloqConfigRequestBody,
} from './dto/update-bloq-config-request';

import * as httpCodes from '../../../../http/models/codes';
import { FakeBloqitClient } from '../../../../bloqit/sdk/fake';
import { FakeIdMappingRepository } from '../../../../core/identifier-mapping/repository/fake';
import { FakeLogger } from '../../../../core/logger/fake';
import FakeExpressRequestBuilder from '../../../../../test/mocks/http/express/request';
import FakeExpressResponseBuilder from '../../../../../test/mocks/http/express/response';
import { simplifyControllerResponse } from '../../../../../test/utils';
import { BusinessType, ExternalPartner } from '../../models/dataRequest';
import { Status } from '../../models/dataResponse';

const friendlyId = 'dhl_cz';

describe('DHLCZBloqsController', () => {
    describe('updateConfig', () => {
        const bloqitBloqId = 'bloqit-bloq-id-1234';
        const dhlCzBloqId = 'dhl-cz-bloq-id-1234';
        const sharedBloqId = 'shared-bloq-id-1234';

        let bloqitClient: FakeBloqitClient;
        let logger: FakeLogger;
        beforeEach(() => {
            logger = new FakeLogger();
            bloqitClient = new FakeBloqitClient();

            jest.spyOn(bloqitClient, 'activateBloq').mockResolvedValueOnce(undefined);
            jest.spyOn(bloqitClient, 'deactivateBloq').mockResolvedValueOnce(undefined);
            jest.spyOn(bloqitClient, 'updateBloqMetadata').mockResolvedValueOnce(undefined);

            jest.spyOn(logger, 'info').mockReturnValueOnce(undefined);
            jest.spyOn(logger, 'error').mockReturnValueOnce(undefined);
        });

        afterEach(() => {
            jest.clearAllMocks();
        });

        it('should reject any update command different than activate, deactivate or update', async () => {
            const idMappingRepository = new FakeIdMappingRepository();
            jest.spyOn(idMappingRepository, 'fetch').mockResolvedValueOnce(bloqitBloqId);

            const reqBody: UpdateBloqConfigRequestBody = {
                dataConfig: {
                    deviceId: sharedBloqId,
                    command: 'invalid-cmd',
                    posTerminal: { TID: 'pos-id-1234', SN: 'sn-12343' },
                    gps: { Lat: '-12.34243', Lon: '46.432432' },
                    address: {
                        street1: 'That st.',
                        street1Nr: '123',
                        postcode: '12345',
                        city: 'That City',
                        name: 'John',
                        firstName: 'John',
                        additionalName: 'Doe',
                    },
                },
                dataRequest: {
                    businessType: BusinessType.PPL,
                    externalPartner: ExternalPartner,
                    accessPointId: dhlCzBloqId,
                    sessionId: 'dummy-session-id',
                },
            };

            const res = new FakeExpressResponseBuilder().build();
            const req = new FakeExpressRequestBuilder()
                .withHeaders({ 'Content-Type': 'application/json' })
                .withBody(reqBody)
                .build();

            const ctrl = new DHLCZBloqsController({
                bloqitClient,
                idMappingRepository,
                logger,
                friendlyId,
            });
            const rawCtrlResp = await ctrl.updateConfig(req, res);
            const response = simplifyControllerResponse(rawCtrlResp);

            expect(response.status).toBe(httpCodes.METHOD_NOT_ALLOWED);
            expect(response.body).toEqual({
                dataResponse: {
                    sessionId: reqBody.dataRequest.sessionId,
                    statusMessage: 'An invalid command was provided',
                    status: Status.ERROR,
                    timestamp: response.body.dataResponse.timestamp,
                    statusCode: httpCodes.METHOD_NOT_ALLOWED.toString(),
                },
            });

            expect(logger.error).toHaveBeenCalledTimes(1);
            expect(logger.error).toHaveBeenCalledWith({
                message: `Failed to process updateConfig request. An invalid command was provided. Expected: activate | deactivate, Received: ${reqBody.dataConfig.command}`,
            });
        });

        it('should fail if no matching bloqit-baked bloq id was found in the id mapping repository', async () => {
            const idMappingRepository = new FakeIdMappingRepository();
            jest.spyOn(idMappingRepository, 'fetch').mockResolvedValueOnce(undefined);

            const reqBody: UpdateBloqConfigRequestBody = {
                dataConfig: {
                    deviceId: sharedBloqId,
                    command: 'invalid-cmd',
                    posTerminal: { TID: 'pos-id-1234', SN: 'sn-12343' },
                    gps: { Lat: '-12.34243', Lon: '46.432432' },
                    address: {
                        street1: 'That st.',
                        street1Nr: '123',
                        postcode: '12345',
                        city: 'That City',
                        name: 'John',
                        firstName: 'John',
                        additionalName: 'Doe',
                    },
                },
                dataRequest: {
                    businessType: BusinessType.PPL,
                    externalPartner: ExternalPartner,
                    accessPointId: dhlCzBloqId,
                    sessionId: 'dummy-session-id',
                },
            };

            const res = new FakeExpressResponseBuilder().build();
            const req = new FakeExpressRequestBuilder()
                .withHeaders({ 'Content-Type': 'application/json' })
                .withBody(reqBody)
                .build();

            const ctrl = new DHLCZBloqsController({
                bloqitClient,
                idMappingRepository,
                logger,
                friendlyId,
            });
            const rawCtrlResp = await ctrl.updateConfig(req, res);
            const response = simplifyControllerResponse(rawCtrlResp);

            expect(response.status).toBe(httpCodes.INTERNAL_SERVER_ERROR);
            expect(logger.error).toHaveBeenCalledTimes(1);
            expect(logger.error).toHaveBeenCalledWith({
                message:
                    'Failed to process updateConfig request. No matching id was found on bloqit side',
            });
            expect(response.body).toEqual({
                dataResponse: {
                    sessionId: reqBody.dataRequest.sessionId,
                    statusMessage: 'No matching id was found',
                    status: Status.ERROR,
                    timestamp: response.body.dataResponse.timestamp,
                    statusCode: httpCodes.INTERNAL_SERVER_ERROR.toString(),
                },
            });
        });

        it('should accept and forward a command to activate a bloq', async () => {
            const idMappingRepository = new FakeIdMappingRepository();
            jest.spyOn(idMappingRepository, 'fetch').mockResolvedValueOnce(bloqitBloqId);
            jest.spyOn(idMappingRepository, 'patchBloqIdMapping').mockResolvedValueOnce(undefined);

            const reqBody: UpdateBloqConfigRequestBody = {
                dataConfig: {
                    deviceId: sharedBloqId,
                    command: UpdateBloqConfigCommand.ACTIVATE,
                    posTerminal: { TID: 'pos-id-1234', SN: 'sn-12343' },
                    gps: { Lat: '-12.34243', Lon: '46.432432' },
                    address: {
                        street1: 'That st.',
                        street1Nr: '123',
                        postcode: '12345',
                        city: 'That City',
                        name: 'John',
                        firstName: 'John',
                        additionalName: 'Doe',
                    },
                },
                dataRequest: {
                    businessType: BusinessType.PPL,
                    externalPartner: ExternalPartner,
                    accessPointId: dhlCzBloqId,
                    sessionId: 'dummy-session-id',
                },
            };

            const res = new FakeExpressResponseBuilder().build();
            const req = new FakeExpressRequestBuilder()
                .withHeaders({ 'Content-Type': 'application/json' })
                .withBody(reqBody)
                .build();

            const ctrl = new DHLCZBloqsController({
                bloqitClient,
                idMappingRepository,
                logger,
                friendlyId,
            });
            const rawCtrlResp = await ctrl.updateConfig(req, res);
            const response = simplifyControllerResponse(rawCtrlResp);

            expect(response.status).toBe(httpCodes.OK);
            expect(response.body).toEqual({
                dataResponse: {
                    sessionId: reqBody.dataRequest.sessionId,
                    statusMessage: Status.OK.toString(),
                    status: Status.OK,
                    timestamp: response.body.dataResponse.timestamp,
                    statusCode: httpCodes.OK.toString(),
                },
            });

            expect(bloqitClient.activateBloq).toHaveBeenCalledTimes(1);
            expect(bloqitClient.activateBloq).toHaveBeenCalledWith({ bloqId: bloqitBloqId });

            expect(idMappingRepository.patchBloqIdMapping).toHaveBeenCalledWith({
                append: { partnerId: dhlCzBloqId },
                matching: {
                    bloqitId: bloqitBloqId,
                    sharedId: sharedBloqId,
                    partnerFriendlyId: friendlyId,
                },
            });
        });

        it('should accept and forward a command deactivate a bloq', async () => {
            const idMappingRepository = new FakeIdMappingRepository();
            jest.spyOn(idMappingRepository, 'fetch').mockResolvedValueOnce(bloqitBloqId);
            jest.spyOn(idMappingRepository, 'patchBloqIdMapping').mockResolvedValueOnce(undefined);

            const reqBody: UpdateBloqConfigRequestBody = {
                dataConfig: {
                    deviceId: sharedBloqId,
                    command: UpdateBloqConfigCommand.DEACTIVATE,
                    posTerminal: { TID: 'pos-id-1234', SN: 'sn-12343' },
                    gps: { Lat: '-12.34243', Lon: '46.432432' },
                    address: {
                        street1: 'That st.',
                        street1Nr: '123',
                        postcode: '12345',
                        city: 'That City',
                        name: 'John',
                        firstName: 'John',
                        additionalName: 'Doe',
                    },
                },
                dataRequest: {
                    businessType: BusinessType.PPL,
                    externalPartner: ExternalPartner,
                    accessPointId: dhlCzBloqId,
                    sessionId: 'dummy-session-id',
                },
            };

            const res = new FakeExpressResponseBuilder().build();
            const req = new FakeExpressRequestBuilder()
                .withHeaders({ 'Content-Type': 'application/json' })
                .withBody(reqBody)
                .build();

            const ctrl = new DHLCZBloqsController({
                bloqitClient,
                idMappingRepository,
                logger,
                friendlyId,
            });
            const rawCtrlResp = await ctrl.updateConfig(req, res);
            const response = simplifyControllerResponse(rawCtrlResp);

            expect(response.status).toBe(httpCodes.OK);
            expect(response.body).toEqual({
                dataResponse: {
                    sessionId: reqBody.dataRequest.sessionId,
                    statusMessage: Status.OK,
                    status: Status.OK,
                    timestamp: response.body.dataResponse.timestamp,
                    statusCode: httpCodes.OK.toString(),
                },
            });

            expect(bloqitClient.deactivateBloq).toHaveBeenCalledTimes(1);
            expect(bloqitClient.deactivateBloq).toHaveBeenCalledWith({ bloqId: bloqitBloqId });

            expect(idMappingRepository.patchBloqIdMapping).toHaveBeenCalledWith({
                append: { partnerId: dhlCzBloqId },
                matching: {
                    bloqitId: bloqitBloqId,
                    sharedId: sharedBloqId,
                    partnerFriendlyId: friendlyId,
                },
            });
        });

        it('should accept and forward command update a bloq metadata', async () => {
            const idMappingRepository = new FakeIdMappingRepository();
            jest.spyOn(idMappingRepository, 'fetch').mockResolvedValueOnce(bloqitBloqId);
            jest.spyOn(idMappingRepository, 'patchBloqIdMapping').mockResolvedValueOnce(undefined);

            const reqBody: UpdateBloqConfigRequestBody = {
                dataConfig: {
                    deviceId: sharedBloqId,
                    command: UpdateBloqConfigCommand.UPDATE,
                    posTerminal: { TID: 'pos-id-1234', SN: 'sn-12343' },
                    gps: { Lat: '-12.34243', Lon: '46.432432' },
                    address: {
                        street1: 'That st.',
                        street1Nr: '123',
                        postcode: '12345',
                        city: 'That City',
                        name: 'John',
                        firstName: 'John',
                        additionalName: 'Doe',
                    },
                },
                dataRequest: {
                    businessType: BusinessType.PPL,
                    externalPartner: ExternalPartner,
                    accessPointId: dhlCzBloqId,
                    sessionId: 'dummy-session-id',
                },
            };

            const res = new FakeExpressResponseBuilder().build();
            const req = new FakeExpressRequestBuilder()
                .withHeaders({ 'Content-Type': 'application/json' })
                .withBody(reqBody)
                .build();

            const ctrl = new DHLCZBloqsController({
                bloqitClient,
                idMappingRepository,
                logger,
                friendlyId,
            });
            const rawCtrlResp = await ctrl.updateConfig(req, res);
            const response = simplifyControllerResponse(rawCtrlResp);

            expect(response.status).toBe(httpCodes.OK);
            expect(response.body).toEqual({
                dataResponse: {
                    sessionId: reqBody.dataRequest.sessionId,
                    statusMessage: Status.OK,
                    status: Status.OK,
                    timestamp: response.body.dataResponse.timestamp,
                    statusCode: httpCodes.OK.toString(),
                },
            });

            expect(bloqitClient.activateBloq).toHaveBeenCalledTimes(0);
            expect(bloqitClient.deactivateBloq).toHaveBeenCalledTimes(0);
            expect(bloqitClient.updateBloqMetadata).toHaveBeenCalledTimes(1);
            expect(bloqitClient.updateBloqMetadata).toHaveBeenCalledWith({
                bloqId: bloqitBloqId,
                metadata: { PPL_CUST_ID: dhlCzBloqId },
            });

            expect(idMappingRepository.patchBloqIdMapping).toHaveBeenCalledWith({
                append: { partnerId: dhlCzBloqId },
                matching: {
                    bloqitId: bloqitBloqId,
                    sharedId: sharedBloqId,
                    partnerFriendlyId: friendlyId,
                },
            });
        });
    });

    describe('getOccupancy', () => {
        const bloqitClient = new FakeBloqitClient();
        const idMappingRepository = new FakeIdMappingRepository();
        const logger = new FakeLogger();

        afterEach(() => {
            jest.clearAllMocks();
        });

        it('should return 405 METHOD NOT ALLOWED if no accessPointId was provided', async () => {
            const res = new FakeExpressResponseBuilder().build();
            const req = new FakeExpressRequestBuilder().build();

            const ctrl = new DHLCZBloqsController({
                bloqitClient,
                idMappingRepository,
                logger,
                friendlyId,
            });
            const rawResponse = await ctrl.getOccupancy(req, res);
            const response = simplifyControllerResponse(rawResponse);

            expect(response.status).toBe(httpCodes.METHOD_NOT_ALLOWED);
            expect(response.body).toEqual({
                dataResponse: {
                    sessionId: '',
                    statusMessage: 'Invalid input (no accessPointId)',
                    status: Status.ERROR,
                    timestamp: response.body.dataResponse.timestamp,
                    statusCode: httpCodes.METHOD_NOT_ALLOWED.toString(),
                },
                dataOccupancy: {
                    slots: [],
                },
            });
        });

        it('should return 405 METHOD NOT ALLOWED if accessPointId is an empty string', async () => {
            const res = new FakeExpressResponseBuilder().build();
            const req = new FakeExpressRequestBuilder()
                .withBody({ dataRequest: { accessPointId: '' } })
                .build();

            const ctrl = new DHLCZBloqsController({
                bloqitClient,
                idMappingRepository,
                logger,
                friendlyId,
            });
            const rawResponse = await ctrl.getOccupancy(req, res);
            const response = simplifyControllerResponse(rawResponse);

            expect(response.status).toBe(httpCodes.METHOD_NOT_ALLOWED);
            expect(response.body).toEqual({
                dataResponse: {
                    sessionId: '',
                    statusMessage: 'Invalid input (no accessPointId)',
                    status: Status.ERROR,
                    timestamp: response.body.dataResponse.timestamp,
                    statusCode: httpCodes.METHOD_NOT_ALLOWED.toString(),
                },
                dataOccupancy: {
                    slots: [],
                },
            });
        });

        it('should return 500 INTERNAL SERVER ERROR if Bloqit client resolved to any other status code other than 200 OK', async () => {
            const bloqitBloqId = 'bloqit-bloq-id';
            const dhlCZBloqId = 'dhl-cz-bloq-id';

            jest.spyOn(idMappingRepository, 'fetch').mockResolvedValueOnce(bloqitBloqId);
            jest.spyOn(bloqitClient, 'getOccupancy').mockResolvedValueOnce({
                status: httpCodes.INTERNAL_SERVER_ERROR,
                lockerObjects: [],
            });

            const res = new FakeExpressResponseBuilder().build();
            const req = new FakeExpressRequestBuilder()
                .withBody({ dataRequest: { accessPointId: dhlCZBloqId } })
                .build();

            const ctrl = new DHLCZBloqsController({
                bloqitClient,
                idMappingRepository,
                logger,
                friendlyId,
            });
            const rawResponse = await ctrl.getOccupancy(req, res);
            const response = simplifyControllerResponse(rawResponse);

            expect(bloqitClient.getOccupancy).toHaveBeenCalledTimes(1);
            expect(bloqitClient.getOccupancy).toHaveBeenCalledWith({ bloqId: bloqitBloqId });

            expect(response.status).toBe(httpCodes.INTERNAL_SERVER_ERROR);
            expect(response.body).toEqual({ description: 'Internal Error' });
        });

        it('should map and return all slots with parcels attached', async () => {
            const bloqitBloqId = 'bloqit-bloq-id';
            const dhlCZBloqId = 'dhl-cz-bloq-id';

            const bloqitRentId1 = 'bloqit-rent-id-1';
            const bloqitRentId2 = 'bloqit-rent-id-2';
            const bloqitRentId3 = 'bloqit-rent-id-3';

            const dhlCZRentId1 = 'dhl-cz-rent-id-1';
            const dhlCZRentId2 = 'dhl-cz-rent-id-2';
            const dhlCZRentId3 = 'dhl-cz-rent-id-3';

            const bloqitLockerId1 = 'bloqit-locker-id-1';
            const bloqitLockerId2 = 'bloqit-locker-id-2';
            const bloqitLockerId3 = 'bloqit-locker-id-3';
            const bloqitLockerId4 = 'bloqit-locker-id-4';
            const bloqitLockerId5 = 'bloqit-locker-id-5';

            const lockerTitle1 = 'A1';
            const lockerTitle2 = 'A2';
            const lockerTitle3 = 'B1';
            const lockerTitle4 = 'B2';
            const lockerTitle5 = 'C1';

            const dhlCZLockerId1 = 'dhl-cz-locker-id-1';
            const dhlCZLockerId2 = 'dhl-cz-locker-id-2';
            const dhlCZLockerId3 = 'dhl-cz-locker-id-3';
            const dhlCZLockerId4 = 'dhl-cz-locker-id-4';
            const dhlCZLockerId5 = 'dhl-cz-locker-id-5';

            jest.spyOn(idMappingRepository, 'fetch').mockImplementation(
                async (props: { matching: any }): Promise<string | undefined> => {
                    const { matching } = props;

                    if (matching.partnerId === dhlCZBloqId) return bloqitBloqId;
                    if (matching.bloqitId === bloqitLockerId1) return dhlCZLockerId1;
                    if (matching.bloqitId === bloqitLockerId2) return dhlCZLockerId2;
                    if (matching.bloqitId === bloqitLockerId3) return dhlCZLockerId3;
                    if (matching.bloqitId === bloqitLockerId4) return dhlCZLockerId4;
                    if (matching.bloqitId === bloqitLockerId5) return dhlCZLockerId5;
                    if (matching.bloqitId === bloqitRentId1) return dhlCZRentId1;
                    if (matching.bloqitId === bloqitRentId2) return dhlCZRentId2;
                    if (matching.bloqitId === bloqitRentId3) return dhlCZRentId3;
                },
            );

            jest.spyOn(bloqitClient, 'getOccupancy').mockResolvedValueOnce({
                status: httpCodes.OK,
                lockerObjects: [
                    {
                        bloq: bloqitBloqId,
                        _id: bloqitLockerId1,
                        rent: bloqitRentId1,
                        lockerTitle: lockerTitle1,
                    },
                    {
                        bloq: bloqitBloqId,
                        _id: bloqitLockerId2,
                        rent: bloqitRentId2,
                        lockerTitle: lockerTitle2,
                    },
                    {
                        bloq: bloqitBloqId,
                        _id: bloqitLockerId3,
                        rent: null,
                        lockerTitle: lockerTitle3,
                    },
                    {
                        bloq: bloqitBloqId,
                        _id: bloqitLockerId4,
                        rent: null,
                        lockerTitle: lockerTitle4,
                    },
                    {
                        bloq: bloqitBloqId,
                        _id: bloqitLockerId5,
                        rent: bloqitRentId3,
                        lockerTitle: lockerTitle5,
                    },
                ],
            });

            const res = new FakeExpressResponseBuilder().build();
            const req = new FakeExpressRequestBuilder()
                .withBody({ dataRequest: { accessPointId: dhlCZBloqId } })
                .build();

            const ctrl = new DHLCZBloqsController({
                bloqitClient,
                idMappingRepository,
                logger,
                friendlyId,
            });
            const rawResponse = await ctrl.getOccupancy(req, res);
            const response = simplifyControllerResponse(rawResponse);

            expect(bloqitClient.getOccupancy).toHaveBeenCalledTimes(1);
            expect(bloqitClient.getOccupancy).toHaveBeenCalledWith({ bloqId: bloqitBloqId });

            expect(response.status).toBe(httpCodes.OK);
            expect(response.body).toEqual({
                dataResponse: {
                    sessionId: '',
                    statusMessage: Status.OK,
                    status: Status.OK,
                    timestamp: response.body.dataResponse.timestamp,
                    statusCode: httpCodes.OK.toString(),
                },
                dataOccupancy: {
                    slots: [
                        { SlotID: lockerTitle1, parcelIdentifier: [dhlCZRentId1] },
                        { SlotID: lockerTitle2, parcelIdentifier: [dhlCZRentId2] },
                        { SlotID: lockerTitle3 },
                        { SlotID: lockerTitle4 },
                        { SlotID: lockerTitle5, parcelIdentifier: [dhlCZRentId3] },
                    ],
                },
            });
        });
    });
});
