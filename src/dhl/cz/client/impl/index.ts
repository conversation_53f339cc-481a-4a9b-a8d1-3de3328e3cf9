import { type <PERSON>HLC<PERSON><PERSON>lient } from '..';
import { type HTTPClient } from '../../../../core/http-client';
import { type DHL<PERSON>ZOAuthToken, type AccessToken } from '../../models/access-token';
import { Status, type DHLCZDataResponse } from '../../models/dataResponse';
import {
    ExternalPartner,
    type DHLCZDataRequest,
    BusinessType,
    type OperationType,
} from '../../models/dataRequest';
import { type DHLCZDataConfig } from '../../models/dataConfig';
import { type DHLCZEvent } from '../../models/event';
import { type GetShipmentResponse } from '../../models/get-shipment-response';
import * as UUIDGenerator from '../../../../core/utils/uuid-generator';
import { PartnerResponseError } from '../../../../core/utils/error';
import { type AuthenticateResponse } from '../../models/authenticate-response';
import type { DHLCZDataElement } from '../../models/dataElement';
import * as httpCodes from '../../../../http/models/codes';
import { type NotifyEventResponse } from '../../models/notify-event-response';
import { type ApplicationEnvironment } from '../../../../core/env';

export const GENERIC_GET_SHIPMENT_ERROR_MSG = 'qrCodeDontMatchActiveRent';

export class DHL_CZClientImpl implements DHLCZClient {
    private readonly httpClient: HTTPClient;
    protected readonly env: ApplicationEnvironment;

    constructor(deps: { httpClient: HTTPClient; env: ApplicationEnvironment }) {
        this.httpClient = deps.httpClient;
        this.env = deps.env;

        this.getAccessToken = this.getAccessToken.bind(this);
        this.getShipment = this.getShipment.bind(this);
        this.setConfig = this.setConfig.bind(this);
        this.notifyEvent = this.notifyEvent.bind(this);
        this.associateLabel = this.associateLabel.bind(this);
        this.authenticateCourier = this.authenticateCourier.bind(this);
    }

    get oauthHost(): string {
        return this.env.DHL_CZ_OAUTH_HOST;
    }

    get baseURL(): string {
        return this.env.DHL_CZ_API_HOST;
    }

    get oauthClientID(): string {
        return this.env.DHL_CZ_OAUTH_CLIENT_ID;
    }

    get oauthClientSecret(): string {
        return this.env.DHL_CZ_OAUTH_CLIENT_SECRET;
    }

    get pplHost(): string {
        const env = this.env.NODE_ENV === 'prod' ? 'prod' : 'test';
        return `authservice-${env}.ppl.cz`;
    }

    async getAccessToken(): Promise<DHLCZOAuthToken> {
        const response = await this.httpClient.request<{ data: DHLCZOAuthToken }>({
            method: 'post',
            url: `${this.oauthHost}/auth/realms/HIPP_ext_auth/protocol/openid-connect/token`,
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                Connection: 'keep-alive',
                Host: this.pplHost,
            },
            httpsAgentOptions: { rejectUnauthorized: false },
            data: {
                client_id: this.oauthClientID,
                client_secret: this.oauthClientSecret,
                grant_type: 'client_credentials',
                scopes: '[HIPP.AP]',
            },
        });

        return response.data;
    }

    async setConfig(
        accessToken: AccessToken,
        dataRequest: DHLCZDataRequest,
        dataConfig: DHLCZDataConfig,
    ): Promise<DHLCZDataResponse> {
        const response = await this.httpClient.request<{ data: DHLCZDataResponse }>({
            method: 'post',
            url: `${this.baseURL}/AccessPointConfigure/v1/configure`,
            headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${accessToken.token}`,
                Connection: 'keep-alive',
            },
            httpsAgentOptions: { rejectUnauthorized: false },
            data: { dataRequest, dataConfig },
        });

        return response.data;
    }

    async authenticateCourier(
        accessToken: AccessToken,
        dataRequest: DHLCZDataRequest,
    ): Promise<AuthenticateResponse> {
        const response = await this.httpClient.request<{ data: AuthenticateResponse }>({
            method: 'post',
            url: `${this.baseURL}/AccessPoint/v1/authenticate`,
            headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${accessToken.token}`,
                Connection: 'keep-alive',
            },
            httpsAgentOptions: { rejectUnauthorized: false },
            data: {
                dataRequest,
            },
        });

        return response.data;
    }

    async notifyEvent(accessToken: AccessToken, event: DHLCZEvent): Promise<DHLCZDataResponse> {
        const response = await this.httpClient.request<{ data: NotifyEventResponse }>({
            method: 'post',
            url: `${this.baseURL}/AccessPoint/v1/event`,
            headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${accessToken.token}`,
                Connection: 'keep-alive',
            },
            httpsAgentOptions: { rejectUnauthorized: false },
            data: event,
            validateStatus: (status: number) => status === httpCodes.OK,
        });

        if (response?.data?.dataResponse?.status === Status.OK) return response.data.dataResponse;
        else
            throw new PartnerResponseError({
                code: response.data?.dataResponse?.statusCode,
                message: response.data?.dataResponse?.statusMessage,
            });
    }

    async getShipment(deps: {
        accessToken: AccessToken;
        accessPointId: string;
        parcelIdentifier: string;
        operationType: OperationType;
        locale: string;
        driverId?: string;
    }): Promise<GetShipmentResponse> {
        const { accessPointId, parcelIdentifier, operationType, locale, driverId } = deps;

        const response = await this.httpClient.request<{
            data: GetShipmentResponse;
            status: number;
        }>({
            method: 'post',
            url: `${this.baseURL}/AccessPoint/v1/shipment`,
            headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${deps.accessToken.token}`,
                Connection: 'keep-alive',
            },
            httpsAgentOptions: { rejectUnauthorized: false },
            data: {
                dataRequest: {
                    parcelIdentifier,
                    accessPointId,
                    operationType,
                    externalPartner: ExternalPartner,
                    businessType: BusinessType.PPL,
                    sessionId: UUIDGenerator.generate(),
                    applicationLocale: locale,
                    driverId,
                },
            },
            /*
                This is needed to prevent axios from throwing on non-2xx status codes,
                because we are checking and handling status codes explicitly below
            */
            validateStatus: () => true,
        });

        const { dataResponse } = response.data;

        /*
            DHL has a particular way of handling statusCodes, the rule is as follows:
            - all status codes below 600 are regular HTTP codes and are accompanied by
            a regular HTTP message matching it. We don't want to return these messages
            to the client, hence the GENERIC_GET_SHIPMENT_ERROR_MSG
            - all status codes equal or above 600 are considered to be DHL-baked,
            domain specific error codes. We want to return these messages to the client
            so they are displayed in the bloq
        */
        if (response.status === httpCodes.OK && dataResponse.status === Status.OK) {
            return response.data;
        } else if (
            response.status === httpCodes.OK &&
            dataResponse.status === Status.ERROR &&
            ![undefined, ''].includes(dataResponse.statusMessage)
        ) {
            throw new PartnerResponseError({
                code: dataResponse.statusCode,
                message: dataResponse.statusMessage,
            });
        } else {
            throw new PartnerResponseError({
                code: `dhl:cz:get-shipment:error`,
                message: GENERIC_GET_SHIPMENT_ERROR_MSG,
            });
        }
    }

    async associateLabel(
        accessToken: AccessToken,
        dataRequest: DHLCZDataRequest,
        dataElement: DHLCZDataElement,
    ): Promise<DHLCZDataResponse> {
        const response = await this.httpClient.request<{
            data: { dataResponse: DHLCZDataResponse };
        }>({
            method: 'post',
            url: `${this.baseURL}/AccessPoint/v1/event`,
            headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${accessToken.token}`,
                Connection: 'keep-alive',
            },
            httpsAgentOptions: { rejectUnauthorized: false },
            data: {
                dataRequest,
                dataElement,
            },
        });
        return response.data.dataResponse;
    }
}
