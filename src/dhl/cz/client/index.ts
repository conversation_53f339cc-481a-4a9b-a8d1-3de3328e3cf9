import { type DHLC<PERSON>OAuthToken, type AccessToken } from '../models/access-token';
import { type DHLCZDataConfig } from '../models/dataConfig';
import type { OperationType, DHLCZDataRequest } from '../models/dataRequest';
import { type DHLCZDataResponse } from '../models/dataResponse';
import { type DHLCZEvent } from '../models/event';
import { type GetShipmentResponse } from '../models/get-shipment-response';
import { type AuthenticateResponse } from '../models/authenticate-response';
import type { DHLCZDataElement } from '../models/dataElement';

export interface DHLCZClient {
    getAccessToken: () => Promise<DHLCZOAuthToken>;

    getShipment: (deps: {
        accessToken: AccessToken;
        accessPointId: string;
        parcelIdentifier: string;
        operationType: OperationType;
        locale: string;
        driverId?: string;
    }) => Promise<GetShipmentResponse>;

    setConfig: (
        accessToken: AccessToken,
        dataRequest: DHLCZDataRequest,
        dataConfig: DHLCZDataConfig,
    ) => Promise<DHLCZDataResponse>;

    authenticateCourier: (
        accessToken: AccessToken,
        dataRequest: DHLCZDataRequest,
    ) => Promise<AuthenticateResponse>;
    notifyEvent: (accessToken: AccessToken, event: DHLCZEvent) => Promise<DHLCZDataResponse>;

    associateLabel: (
        accessToken: AccessToken,
        dataRequest: DHLCZDataRequest,
        dataElement: DHLCZDataElement,
    ) => Promise<DHLCZDataResponse>;
}
