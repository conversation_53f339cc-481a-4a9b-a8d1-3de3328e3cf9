import { type ApplicationEnvironment } from '../../../../core/env';
import { type HTTPClient } from '../../../../core/http-client';
import { DHL_CZClientImpl } from '../impl';

export class DHL_CZRemotelyMockedClientImpl extends DHL_CZClientImpl {
    private readonly mockURL: string;
    constructor(deps: { httpClient: HTTPClient; env: ApplicationEnvironment }) {
        super(deps);
        this.mockURL = this.resolveMockURL();
    }

    get oauthHost(): string {
        return this.mockURL;
    }

    get baseURL(): string {
        return this.mockURL;
    }

    private resolveMockURL(): string {
        const mockURL = this.env.PARTNER_API_MOCK_SERVER_HOST;
        if (mockURL !== undefined) return mockURL;
        else throw new Error('PARTNER_API_MOCK_SERVER_HOST not found in environment variables');
    }
}
