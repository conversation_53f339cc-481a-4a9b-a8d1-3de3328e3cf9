import { type DHLCZ<PERSON>lient } from '..';
import { type DHLC<PERSON>OAuthToken, type AccessToken } from '../../models/access-token';
import { type DHLCZDataConfig } from '../../models/dataConfig';
import { type DHLCZDataRequest } from '../../models/dataRequest';
import { type DHLCZDataResponse } from '../../models/dataResponse';
import { type GetShipmentResponse } from '../../models/get-shipment-response';
import { type DHLCZEvent } from '../../models/event';
import { type AuthenticateResponse } from '../../models/authenticate-response';
import { type DHLCZDataElement } from '../../models/dataElement';

export class FakeDHL<PERSON>ZClient implements DHLCZClient {
    async getShipment(_deps: {
        accessToken: AccessToken;
        accessPointId: string;
        parcelIdentifier: string;
        driverId?: string;
    }): Promise<GetShipmentResponse> {
        throw new Error('Not implemented. Please override it for your test purposes.');
    }

    async getAccessToken(): Promise<DHLCZOAuthToken> {
        throw new Error('Not implemented. Please override it for your test purposes.');
    }

    async setConfig(
        _accessToken: AccessToken,
        _dataRequest: DHLCZDataRequest,
        _dataConfig: DHLCZDataConfig,
    ): Promise<DHLCZDataResponse> {
        throw new Error('Not implemented. Please override it for your test purposes.');
    }

    async authenticateCourier(
        _accessToken: AccessToken,
        _dataRequest: DHLCZDataRequest,
    ): Promise<AuthenticateResponse> {
        throw new Error('Not implemented. Please override it for your test purposes.');
    }

    async notifyEvent(_accessToken: AccessToken, _event: DHLCZEvent): Promise<DHLCZDataResponse> {
        throw new Error('Not implemented. Please override it for your test purposes.');
    }

    async associateLabel(
        _accessToken: AccessToken,
        _dataRequest: DHLCZDataRequest,
        dataElement: DHLCZDataElement,
    ): Promise<DHLCZDataResponse> {
        throw new Error('Not implemented. Please override it for your test purposes.');
    }
}
