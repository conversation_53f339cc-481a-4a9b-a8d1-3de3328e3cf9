/* eslint-disable @typescript-eslint/unbound-method */
import { DHLCZHandlerImpl } from '.';
import { FakeIdMappingRepository } from '../../../../core/identifier-mapping/repository/fake';
import { FakeDHLCZClient } from '../../client/fake';
import {
    ApplicationLocale,
    BusinessType,
    ExternalPartner,
    OperationType,
} from '../../models/dataRequest';
import { Status } from '../../models/dataResponse';
import { type GetShipmentResponse, ProductType } from '../../models/get-shipment-response';
import { FakeLogger } from '../../../../core/logger/fake';
import {
    Roles,
    type RentEvent,
    OperationType as bloqitOperationType,
} from '../../../../bloqit/rents/models/rent';
import * as DateTime from '../../../../core/utils/date-time';
import { randomUUID } from 'node:crypto';
import { DHLCZCountryCodes, DHLCZEventCodes, DHLCZLocationCodes } from '../../models/dataElement';

const partnerRentId = 'partner-rent-id-123';
const accessToken = 'dhl-cz-access-token';
const locale = 'pt-PT';
const courierId = 'courier-1234';
const partnerBloqId = 'dhl-cz-bloq-id-123';
const bloqitBloqId = 'bloqit-bloq-id-123';

const MOCKED_DHL_GET_SHIPMENT_RESPONSE: GetShipmentResponse = {
    dataResponse: {
        status: Status.OK,
        sessionId: 'ce2934dd-b0a0-41e0-a98e-d1ddf8060e3d',
        statusMessage: 'Request accepted',
        timestamp: '2019-10-18T14:04:05.185+02:0',
        statusCode: '200',
    },
    dataElement: {
        version: '0200',
        dataElementType: 'xPAN',
        parcelOriginOrganization: 'CZ-7001',
        parcelDestinationOrganization: 'CZ-7001',
        dataElementOriginOrganization: 'CZ-7001',
        distributionTimestamp: '2019-10-18T13:11:43',
        payment: { paymentStatus: 'paid' },
        general: {
            product: ProductType.ParcelConnect,
            parcelIdentifier: [partnerRentId],
            routingCode: '2LCZ35002+********',
            timestamp: '2019-10-18T13:11:43',
        },
        xPAN: {
            addresses: {
                sender: [{ type: 'sender', email: '<EMAIL>', name: 'John Doe' }],
                recipient: [{ type: 'parcelbox', email: '<EMAIL>', name: 'Jane Doe' }],
            },
            features: {
                cod: { nonSepa: { amount: '607.0', currency: 'CZK' } },
                physical: { grossWeight: '7.11', length: '10', width: '20', height: '30' },
                identityCheck: {
                    trueOrFalse: 'true',
                    typeOfDocument: 'PIN',
                    documentIdentifcationNr: '123456',
                },
            },
            featureCodes: {
                feature: [
                    { name: ['SmartPIN'], textValue: ['ABC123'] },
                    { name: ['LabelLess'], textValue: ['true'] },
                    { name: ['BiggestPsSize'], textValue: ['XXL'] },
                ],
            },
        },
    },
};

describe('DHL CZ - Handler', () => {
    describe('Private function test - parseCodeNumOnlyOrFull', () => {
        it('should return numbers 11 only', () => {
            expect(DHLCZHandlerImpl.parseCodeNumOnlyOrFull('12345678912')).toEqual('12345678912');
        });
        it('should return numbers 11 without dashes', () => {
            expect(DHLCZHandlerImpl.parseCodeNumOnlyOrFull('1234567-8912-345')).toEqual(
                '12345678912',
            );
        });
        it('should return alphanumerics full', () => {
            expect(DHLCZHandlerImpl.parseCodeNumOnlyOrFull('1234567DDD8912ABC')).toEqual(
                '1234567DDD8912ABC',
            );
        });
        it('should return alphanumerics full without dashes', () => {
            expect(DHLCZHandlerImpl.parseCodeNumOnlyOrFull('1234567-8912-345ABC')).toEqual(
                '1234567-8912-345ABC',
            );
        });
    });

    describe('getRentById', () => {
        describe('courier flow', () => {
            it('should fetch a rent by its identifier', async () => {
                const idMappingRepository = new FakeIdMappingRepository();
                jest.spyOn(idMappingRepository, 'fetch').mockResolvedValueOnce(partnerBloqId);
                const logger = new FakeLogger();

                const dhlCZClient = new FakeDHLCZClient();

                jest.spyOn(dhlCZClient, 'getAccessToken').mockResolvedValueOnce({
                    access_token: accessToken,
                    expires_in: 10000,
                    refresh_expires_in: 3000,
                    token_type: 'jwt',
                    'not-before-policy': 0,
                    scope: 'bloqit',
                });

                jest.spyOn(dhlCZClient, 'getShipment').mockResolvedValueOnce(
                    MOCKED_DHL_GET_SHIPMENT_RESPONSE,
                );

                const handler = new DHLCZHandlerImpl({ dhlCZClient, idMappingRepository, logger });
                const createdRent = await handler.getRentById({
                    partnerRentId,
                    bloqId: bloqitBloqId,
                    courier: courierId,
                    locale: 'pt-PT',
                });

                expect(createdRent[0].customer?.name).toBe('Jane Doe');
                expect(createdRent[0].customer?.email).toBe('<EMAIL>');

                expect(dhlCZClient.getShipment).toHaveBeenCalledTimes(1);
                expect(dhlCZClient.getShipment).toHaveBeenCalledWith({
                    accessToken: { token: accessToken, validity: expect.any(Date) },
                    parcelIdentifier: DHLCZHandlerImpl.parseCodeNumOnlyOrFull(partnerRentId),
                    locale,
                    accessPointId: partnerBloqId,
                    driverId: courierId,
                    operationType: OperationType.LAST_MILE,
                });
            });

            it('should set the expiryDate to 3 days from now', async () => {
                const threeDaysInTheFuture = new Date('2023-01-04');
                jest.spyOn(DateTime, 'daysInTheFuture').mockReturnValueOnce(threeDaysInTheFuture);

                const idMappingRepository = new FakeIdMappingRepository();
                jest.spyOn(idMappingRepository, 'fetch').mockResolvedValueOnce(partnerBloqId);
                const logger = new FakeLogger();

                const dhlCZClient = new FakeDHLCZClient();

                jest.spyOn(dhlCZClient, 'getAccessToken').mockResolvedValueOnce({
                    access_token: accessToken,
                    expires_in: 10000,
                    refresh_expires_in: 3000,
                    token_type: 'jwt',
                    'not-before-policy': 0,
                    scope: 'bloqit',
                });

                jest.spyOn(dhlCZClient, 'getShipment').mockResolvedValueOnce(
                    MOCKED_DHL_GET_SHIPMENT_RESPONSE,
                );

                const handler = new DHLCZHandlerImpl({ dhlCZClient, idMappingRepository, logger });
                const rents = await handler.getRentById({
                    partnerRentId,
                    bloqId: bloqitBloqId,
                    courier: courierId,
                    locale: 'pt-PT',
                });

                const rent = rents[0];
                expect(rent.expiryDate).toEqual(threeDaysInTheFuture);
                expect(DateTime.daysInTheFuture).toHaveBeenCalledWith(3);
            });
        });

        describe('customer flow', () => {
            it('should fetch a rent by its identifier', async () => {
                const idMappingRepository = new FakeIdMappingRepository();
                jest.spyOn(idMappingRepository, 'fetch').mockResolvedValueOnce(partnerBloqId);
                const logger = new FakeLogger();

                const dhlCZClient = new FakeDHLCZClient();

                jest.spyOn(dhlCZClient, 'getAccessToken').mockResolvedValueOnce({
                    access_token: accessToken,
                    expires_in: 10000,
                    refresh_expires_in: 3000,
                    token_type: 'jwt',
                    'not-before-policy': 0,
                    scope: 'bloqit',
                });

                jest.spyOn(dhlCZClient, 'getShipment').mockResolvedValueOnce(
                    MOCKED_DHL_GET_SHIPMENT_RESPONSE,
                );

                const handler = new DHLCZHandlerImpl({ dhlCZClient, idMappingRepository, logger });
                const createdRent = await handler.getRentById({
                    partnerRentId,
                    bloqId: bloqitBloqId,
                    locale: 'pt-PT',
                });

                expect(createdRent[0].customer?.name).toBe('John Doe');
                expect(createdRent[0].customer?.email).toBe('<EMAIL>');

                expect(dhlCZClient.getShipment).toHaveBeenCalledTimes(1);
                expect(dhlCZClient.getShipment).toHaveBeenCalledWith({
                    accessToken: { token: accessToken, validity: expect.any(Date) },
                    parcelIdentifier: DHLCZHandlerImpl.parseCodeNumOnlyOrFull(partnerRentId),
                    locale,
                    accessPointId: partnerBloqId,
                    operationType: OperationType.FIRST_MILE,
                });
            });

            it('should should set the expiryDate to 90 days from now', async () => {
                jest.spyOn(DateTime, 'now').mockReturnValueOnce(new Date('2023-01-01'));

                const idMappingRepository = new FakeIdMappingRepository();
                jest.spyOn(idMappingRepository, 'fetch').mockResolvedValueOnce(partnerBloqId);
                const logger = new FakeLogger();

                const dhlCZClient = new FakeDHLCZClient();

                jest.spyOn(dhlCZClient, 'getAccessToken').mockResolvedValueOnce({
                    access_token: accessToken,
                    expires_in: 10000,
                    refresh_expires_in: 3000,
                    token_type: 'jwt',
                    'not-before-policy': 0,
                    scope: 'bloqit',
                });

                jest.spyOn(dhlCZClient, 'getShipment').mockResolvedValueOnce(
                    MOCKED_DHL_GET_SHIPMENT_RESPONSE,
                );

                const handler = new DHLCZHandlerImpl({ dhlCZClient, idMappingRepository, logger });
                const rents = await handler.getRentById({
                    partnerRentId,
                    bloqId: bloqitBloqId,
                    locale: 'pt-PT',
                });

                const rent = rents[0];
                expect(rent.expiryDate).toEqual(new Date('2023-04-01'));
            });
        });
    });

    describe('associateLabel', () => {
        it('should fail pre validating the code', async () => {
            const idMappingRepository = new FakeIdMappingRepository();

            const logger = new FakeLogger();
            jest.spyOn(logger, 'info');

            const dhlCZClient = new FakeDHLCZClient();

            const code = 'bad-label-code';

            const handler = new DHLCZHandlerImpl({ dhlCZClient, idMappingRepository, logger });
            const response = await handler.associateLabel({
                partnerRentId,
                bloqId: bloqitBloqId,
                courier: courierId,
                locale,
                operationType: bloqitOperationType.FIRST_MILE,
                code,
            });

            expect(logger.info).toHaveBeenCalledWith({
                message: 'Label association failed in pre validation',
                code,
            });
            expect(response).toBe(false);
        });

        it('should return true using a valid code', async () => {
            const idMappingRepository = new FakeIdMappingRepository();
            jest.spyOn(idMappingRepository, 'fetch').mockResolvedValueOnce(partnerBloqId);

            const logger = new FakeLogger();

            const dhlCZClient = new FakeDHLCZClient();
            jest.spyOn(dhlCZClient, 'getAccessToken').mockResolvedValueOnce({
                access_token: accessToken,
                expires_in: 10000,
                refresh_expires_in: 3000,
                token_type: 'jwt',
                'not-before-policy': 0,
                scope: 'bloqit',
            });

            jest.spyOn(dhlCZClient, 'associateLabel').mockResolvedValueOnce({
                status: Status.OK,
                sessionId: 'session-id',
                statusMessage: 'status-message',
                timestamp: 'timestamp',
                statusCode: 'status-code',
            });

            const code = 'MI0123456789NI';

            const handler = new DHLCZHandlerImpl({ dhlCZClient, idMappingRepository, logger });
            const response = await handler.associateLabel({
                partnerRentId,
                bloqId: bloqitBloqId,
                courier: courierId,
                locale,
                operationType: bloqitOperationType.FIRST_MILE,
                code,
            });

            expect(response).toEqual(true);
        });

        it('should return false using a valid code if DHL returns an error', async () => {
            const idMappingRepository = new FakeIdMappingRepository();
            jest.spyOn(idMappingRepository, 'fetch').mockResolvedValueOnce(partnerBloqId);

            const logger = new FakeLogger();

            const dhlCZClient = new FakeDHLCZClient();
            jest.spyOn(dhlCZClient, 'getAccessToken').mockResolvedValueOnce({
                access_token: accessToken,
                expires_in: 10000,
                refresh_expires_in: 3000,
                token_type: 'jwt',
                'not-before-policy': 0,
                scope: 'bloqit',
            });

            jest.spyOn(dhlCZClient, 'associateLabel').mockResolvedValueOnce({
                status: Status.ERROR,
                sessionId: 'session-id',
                statusMessage: 'status-message',
                timestamp: 'timestamp',
                statusCode: 'status-code',
            });

            const code = 'MI0123456789NI';

            const handler = new DHLCZHandlerImpl({ dhlCZClient, idMappingRepository, logger });
            const response = await handler.associateLabel({
                partnerRentId,
                bloqId: bloqitBloqId,
                courier: courierId,
                locale,
                operationType: bloqitOperationType.FIRST_MILE,
                code,
            });

            expect(response).toEqual(false);
        });
    });

    describe('notifyExpiredRent', () => {
        it('should notify an expired rent', async () => {
            const idMappingRepository = new FakeIdMappingRepository();
            jest.spyOn(idMappingRepository, 'fetch').mockResolvedValueOnce(partnerBloqId);

            const logger = new FakeLogger();

            const dhlCZClient = new FakeDHLCZClient();
            jest.spyOn(dhlCZClient, 'getAccessToken').mockResolvedValueOnce({
                access_token: accessToken,
                expires_in: 10000,
                refresh_expires_in: 3000,
                token_type: 'jwt',
                'not-before-policy': 0,
                scope: 'bloqit',
            });

            jest.spyOn(dhlCZClient, 'notifyEvent').mockResolvedValueOnce({
                status: Status.OK,
                sessionId: 'session-id',
                statusMessage: 'status-message',
                timestamp: 'timestamp',
                statusCode: 'status-code',
            });

            const bloqitRentId = randomUUID();
            const dhlCZRentId = randomUUID();
            const event: RentEvent = {
                rent: bloqitRentId,
                bloq: bloqitBloqId,
                locker: 'locker-1234',
                timestamp: new Date(),
                actionInitiatedBy: { role: Roles.CUSTOMER, id: 'customer-1234' },
                customer: { email: '<EMAIL>' },
                metadata: {
                    externalID: dhlCZRentId,
                    bloqExternalID: partnerBloqId,
                    rentMetadata: { productType: randomUUID() },
                },
            };

            const handler = new DHLCZHandlerImpl({ dhlCZClient, idMappingRepository, logger });
            await handler.notifyExpiredRent(event);

            expect(dhlCZClient.notifyEvent).toHaveBeenCalledTimes(1);
            expect(dhlCZClient.notifyEvent).toHaveBeenCalledWith(
                { token: accessToken, validity: expect.any(Date) },
                {
                    dataRequest: {
                        externalPartner: 'BLOQIT',
                        businessType: 'PPL',
                        accessPointId: 'dhl-cz-bloq-id-123',
                        slotId: undefined,
                        sessionId: 'static-uuid',
                        parcelIdentifier: event.metadata.externalID,
                        applicationLocale: 'cz',
                        driverId: 'customer-1234',
                        operationType: 'LM',
                    },
                    dataElement: {
                        version: '0200',
                        dataElementType: 'event',
                        parcelOriginOrganization: 'CZ-7001',
                        parcelDestinationOrganization: 'CZ-7001',
                        dataElementOriginOrganization: 'CZ-7001',
                        distributionTimestamp: expect.any(Date),
                        general: {
                            product: 'ParcelEurope.parcelconnect',
                            parcelIdentifier: [event.metadata.externalID],
                            routingCode: '2LCZ35002+********',
                            timestamp: expect.any(Date),
                        },
                        event: {
                            parcelEuropeSpecificData: {
                                country: 'CZ',
                                eventCode: 'EDX',
                                location: 'parcelStation',
                                document: [
                                    { reference: 'Příjemce zásilku nevyzvedl', type: 'Note' },
                                    { reference: 'dhl-cz-bloq-id-123', type: 'Note2' },
                                ],
                            },
                        },
                    },
                },
            );
        });
    });

    describe('notifyStuckRent', () => {
        it('should notify a stuck rent', async () => {
            const idMappingRepository = new FakeIdMappingRepository();
            jest.spyOn(idMappingRepository, 'fetch').mockResolvedValueOnce(partnerBloqId);

            const logger = new FakeLogger();

            const dhlCZClient = new FakeDHLCZClient();
            jest.spyOn(dhlCZClient, 'getAccessToken').mockResolvedValueOnce({
                access_token: accessToken,
                expires_in: 10000,
                refresh_expires_in: 3000,
                token_type: 'jwt',
                'not-before-policy': 0,
                scope: 'bloqit',
            });

            jest.spyOn(dhlCZClient, 'notifyEvent').mockResolvedValueOnce({
                status: Status.OK,
                sessionId: 'session-id',
                statusMessage: 'status-message',
                timestamp: 'timestamp',
                statusCode: 'status-code',
            });

            const bloqitRentId = randomUUID();
            const dhlCZRentId = randomUUID();
            const event: RentEvent = {
                rent: bloqitRentId,
                bloq: bloqitBloqId,
                locker: 'locker-1234',
                timestamp: new Date(),
                actionInitiatedBy: { role: Roles.CUSTOMER, id: 'customer-1234' },
                customer: { email: '<EMAIL>' },
                metadata: {
                    externalID: dhlCZRentId,
                    bloqExternalID: partnerBloqId,
                    rentMetadata: { productType: randomUUID() },
                },
            };

            const handler = new DHLCZHandlerImpl({ dhlCZClient, idMappingRepository, logger });
            await handler.notifyStuckRent(event);

            expect(dhlCZClient.notifyEvent).toHaveBeenCalledTimes(1);
            expect(dhlCZClient.notifyEvent).toHaveBeenCalledWith(
                { token: accessToken, validity: expect.any(Date) },
                {
                    dataRequest: {
                        externalPartner: 'BLOQIT',
                        businessType: 'PPL',
                        accessPointId: 'dhl-cz-bloq-id-123',
                        slotId: undefined,
                        sessionId: 'static-uuid',
                        parcelIdentifier: event.metadata.externalID,
                        applicationLocale: 'cz',
                        driverId: 'customer-1234',
                        operationType: 'LM',
                    },
                    dataElement: {
                        version: '0200',
                        dataElementType: 'event',
                        parcelOriginOrganization: 'CZ-7001',
                        parcelDestinationOrganization: 'CZ-7001',
                        dataElementOriginOrganization: 'CZ-7001',
                        distributionTimestamp: expect.any(Date),
                        general: {
                            product: 'ParcelEurope.parcelconnect',
                            parcelIdentifier: [event.metadata.externalID],
                            routingCode: '2LCZ35002+********',
                            timestamp: expect.any(Date),
                        },
                        event: {
                            parcelEuropeSpecificData: {
                                country: 'CZ',
                                eventCode: 'E3K',
                                location: 'parcelStation',
                                reason: 'R604',
                                document: [
                                    { reference: dhlCZRentId, type: 'Note' },
                                    { reference: 'dhl-cz-bloq-id-123', type: 'Note2' },
                                ],
                            },
                        },
                    },
                },
            );
        });

        it('should not notify when event has no rent', async () => {
            const idMappingRepository = new FakeIdMappingRepository();
            jest.spyOn(idMappingRepository, 'fetch').mockResolvedValueOnce(partnerBloqId);

            const logger = new FakeLogger();

            const dhlCZClient = new FakeDHLCZClient();
            jest.spyOn(dhlCZClient, 'getAccessToken').mockResolvedValueOnce({
                access_token: accessToken,
                expires_in: 10000,
                refresh_expires_in: 3000,
                token_type: 'jwt',
                'not-before-policy': 0,
                scope: 'bloqit',
            });

            jest.spyOn(dhlCZClient, 'notifyEvent').mockResolvedValueOnce({
                status: Status.OK,
                sessionId: 'session-id',
                statusMessage: 'status-message',
                timestamp: 'timestamp',
                statusCode: 'status-code',
            });

            const dhlCZRentId = randomUUID();
            const event: Partial<RentEvent> = {
                bloq: bloqitBloqId,
                locker: 'locker-1234',
                timestamp: new Date(),
                actionInitiatedBy: { role: Roles.CUSTOMER, id: 'customer-1234' },
                customer: { email: '<EMAIL>' },
                metadata: {
                    externalID: dhlCZRentId,
                    bloqExternalID: partnerBloqId,
                    rentMetadata: { productType: randomUUID() },
                },
            };

            const handler = new DHLCZHandlerImpl({ dhlCZClient, idMappingRepository, logger });
            await handler.notifyStuckRent(event as RentEvent);

            expect(dhlCZClient.notifyEvent).not.toHaveBeenCalled();
        });
    });

    describe('notifyCollectItem', () => {
        const event = {
            createdAt: '2024-08-13T16:54:58.432Z',
            bloq: '6554824afd5b028564943133',
            metadata: {
                externalID: '70600901894',
                lockerExternalID: 'C6',
                offlinePickup: false,
                rentOperationType: 'lastMile',
                bloqExternalID: '2026',
            },
            actionInitiatedBy: {
                pickUpCode: { name: 'pin', value: '952282' },
                role: 'Carrier',
                pin: '952282',
                id: '66bb5babeec21032c1e02baf',
                source: 'locker',
            },
            codeName: 'rent.collect_item',
            description: 'Rent has transited to COLLECT_ITEM state',
            _id: '66bb8fe21c635f7ac04dd62b',
            rent: '66bb5babeec21032c1e02bb2',
            title: 'Rent Collect Item',
            locker: '6554824afd5b02856494314e',
            timestamp: '2024-08-13T16:54:57.941Z',
            customer: {
                phoneNumber: '',
                name: 'PBOX OKE tř.Tomáše Bati (Baťov)',
                _id: '66bb5babeec21032c1e02baf',
                email: '<EMAIL>',
            },
        } as unknown as RentEvent;

        const accessTokenPayload = {
            access_token: accessToken,
            expires_in: 10000,
            refresh_expires_in: 3000,
            token_type: 'jwt',
            'not-before-policy': 0,
            scope: 'bloqit',
        };

        const notifyEventPayload = {
            status: Status.OK,
            sessionId: 'session-id',
            statusMessage: 'status-message',
            timestamp: 'timestamp',
            statusCode: 'status-code',
        };

        const logger = new FakeLogger();
        const idMappingRepository = new FakeIdMappingRepository();
        const dhlCZClient = new FakeDHLCZClient();

        afterEach(() => jest.clearAllMocks());

        it('should throw an error if there is no role associated to the event', async () => {
            jest.spyOn(idMappingRepository, 'fetch').mockResolvedValueOnce(partnerBloqId);
            jest.spyOn(dhlCZClient, 'notifyEvent').mockResolvedValueOnce(notifyEventPayload);
            jest.spyOn(dhlCZClient, 'getAccessToken').mockResolvedValueOnce(accessTokenPayload);

            const handler = new DHLCZHandlerImpl({ dhlCZClient, idMappingRepository, logger });

            await expect(handler.notifyCollectItem(event)).rejects.toThrow(
                `Invalid role associated with the COLLECT_ITEM event with id ${event._id}`,
            );
        });

        it('should notify a customer pickup', async () => {
            jest.spyOn(idMappingRepository, 'fetch').mockResolvedValueOnce(partnerBloqId);
            jest.spyOn(dhlCZClient, 'notifyEvent').mockResolvedValueOnce(notifyEventPayload);
            jest.spyOn(dhlCZClient, 'getAccessToken').mockResolvedValueOnce(accessTokenPayload);

            const actionInitiatedBy = { role: Roles.CUSTOMER, id: '66bb5babeec21032c1e02baf' };
            const customerPickupEvent = { ...event, actionInitiatedBy };

            const handler = new DHLCZHandlerImpl({ dhlCZClient, idMappingRepository, logger });
            await handler.notifyCollectItem(customerPickupEvent);

            expect(dhlCZClient.notifyEvent).toHaveBeenCalledTimes(1);
            expect(dhlCZClient.notifyEvent).toHaveBeenCalledWith(
                { token: accessToken, validity: expect.any(Date) },
                {
                    dataRequest: {
                        sessionId: expect.any(String),
                        businessType: BusinessType.PPL,
                        applicationLocale: ApplicationLocale.cz,
                        operationType: OperationType.LAST_MILE,
                        externalPartner: ExternalPartner,
                        accessPointId: partnerBloqId,
                        slotId: event.metadata.lockerExternalID,
                        parcelIdentifier: event.metadata.externalID,
                        driverId: event.actionInitiatedBy.id,
                    },
                    dataElement: {
                        version: '0200',
                        dataElementType: 'event',
                        parcelOriginOrganization: 'CZ-7001',
                        parcelDestinationOrganization: 'CZ-7001',
                        dataElementOriginOrganization: 'CZ-7001',
                        distributionTimestamp: event.timestamp,
                        general: {
                            product: ProductType.ParcelConnect,
                            parcelIdentifier: [event.metadata.externalID],
                            routingCode: '2LCZ35002+********',
                            timestamp: event.timestamp,
                        },
                        event: {
                            parcelEuropeSpecificData: {
                                eventCode: DHLCZEventCodes.customerPickUp,
                                country: DHLCZCountryCodes.CZ,
                                location: DHLCZLocationCodes.parcelStation,
                                document: [{ type: 'Note2', reference: partnerBloqId }],
                                deliveryAddress: { email: event.customer.email },
                            },
                        },
                    },
                },
            );
        });

        it('should notify a default customer pickup', async () => {
            jest.spyOn(idMappingRepository, 'fetch').mockResolvedValueOnce(partnerBloqId);
            jest.spyOn(dhlCZClient, 'notifyEvent').mockResolvedValueOnce(notifyEventPayload);
            jest.spyOn(dhlCZClient, 'getAccessToken').mockResolvedValueOnce(accessTokenPayload);

            const actionInitiatedBy = { role: undefined, id: '66bb5babeec21032c1e02baf' };
            const customerPickupEvent = {
                ...event,
                customer: undefined,
                actionInitiatedBy,
            } as unknown as RentEvent;

            const handler = new DHLCZHandlerImpl({ dhlCZClient, idMappingRepository, logger });
            await handler.notifyCollectItem(customerPickupEvent);

            expect(dhlCZClient.notifyEvent).toHaveBeenCalledTimes(1);
            expect(dhlCZClient.notifyEvent).toHaveBeenCalledWith(
                { token: accessToken, validity: expect.any(Date) },
                {
                    dataRequest: {
                        sessionId: expect.any(String),
                        businessType: BusinessType.PPL,
                        applicationLocale: ApplicationLocale.cz,
                        operationType: OperationType.LAST_MILE,
                        externalPartner: ExternalPartner,
                        accessPointId: partnerBloqId,
                        slotId: event.metadata.lockerExternalID,
                        parcelIdentifier: event.metadata.externalID,
                        driverId: event.actionInitiatedBy.id,
                    },
                    dataElement: {
                        version: '0200',
                        dataElementType: 'event',
                        parcelOriginOrganization: 'CZ-7001',
                        parcelDestinationOrganization: 'CZ-7001',
                        dataElementOriginOrganization: 'CZ-7001',
                        distributionTimestamp: event.timestamp,
                        general: {
                            product: ProductType.ParcelConnect,
                            parcelIdentifier: [event.metadata.externalID],
                            routingCode: '2LCZ35002+********',
                            timestamp: event.timestamp,
                        },
                        event: {
                            parcelEuropeSpecificData: {
                                eventCode: DHLCZEventCodes.customerPickUp,
                                country: DHLCZCountryCodes.CZ,
                                location: DHLCZLocationCodes.parcelStation,
                                document: [{ type: 'Note2', reference: partnerBloqId }],
                            },
                        },
                    },
                },
            );
        });

        it('should notify a courier pickup', async () => {
            jest.spyOn(idMappingRepository, 'fetch').mockResolvedValueOnce(partnerBloqId);
            jest.spyOn(dhlCZClient, 'notifyEvent').mockResolvedValueOnce(notifyEventPayload);
            jest.spyOn(dhlCZClient, 'getAccessToken').mockResolvedValueOnce(accessTokenPayload);

            const actionInitiatedBy = { role: Roles.COURIER, id: '66bb5babeec21032c1e02baf' };
            const courierPickupEvent = { ...event, actionInitiatedBy };

            const handler = new DHLCZHandlerImpl({ dhlCZClient, idMappingRepository, logger });
            await handler.notifyCollectItem(courierPickupEvent);

            expect(dhlCZClient.notifyEvent).toHaveBeenCalledTimes(1);
            expect(dhlCZClient.notifyEvent).toHaveBeenCalledWith(
                { token: accessToken, validity: expect.any(Date) },
                {
                    dataRequest: {
                        sessionId: expect.any(String),
                        businessType: BusinessType.PPL,
                        applicationLocale: ApplicationLocale.cz,
                        operationType: OperationType.LAST_MILE,
                        externalPartner: ExternalPartner,
                        accessPointId: partnerBloqId,
                        slotId: event.metadata.lockerExternalID,
                        parcelIdentifier: event.metadata.externalID,
                        driverId: event.actionInitiatedBy.id,
                    },
                    dataElement: {
                        version: '0200',
                        dataElementType: 'event',
                        parcelOriginOrganization: 'CZ-7001',
                        parcelDestinationOrganization: 'CZ-7001',
                        dataElementOriginOrganization: 'CZ-7001',
                        distributionTimestamp: event.timestamp,
                        general: {
                            product: ProductType.ParcelConnect,
                            parcelIdentifier: [event.metadata.externalID],
                            routingCode: '2LCZ35002+********',
                            timestamp: event.timestamp,
                        },
                        event: {
                            parcelEuropeSpecificData: {
                                eventCode: DHLCZEventCodes.driverPickUp,
                                country: DHLCZCountryCodes.CZ,
                                location: DHLCZLocationCodes.parcelStation,
                                document: [{ type: 'Note2', reference: partnerBloqId }],
                            },
                        },
                    },
                },
            );
        });
    });
});
