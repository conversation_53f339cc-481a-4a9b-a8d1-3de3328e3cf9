import { type DHLCZDataResponse, Status } from '../../models/dataResponse';
import {
    type BloqChangeState,
    type BloqChangeStateResponse,
    CodeName as BloqCodeName,
} from '../../../../bloqit/bloqs/models';
import {
    AccessRole,
    ApplicationLocale,
    BusinessType,
    type DHLCZDataRequest,
    ExternalPartner,
    OperationType,
} from '../../models/dataRequest';
import { Command, type DHLCZDataConfig } from '../../models/dataConfig';
import {
    CodeName as LockerCodeName,
    type LockerChangeState,
    type LockerChangeStateResponse,
} from '../../../../bloqit/lockers/models';
import { COURIER_PERMISSIONS, type AuthenticationResult } from '../../../../bloqit/couriers/models/authentication-result';
import type { DHLCZClient } from '../../client';
import type { PrePickupAction, Rent, RentEvent } from '../../../../bloqit/rents/models/rent';
import {
    PrePickupActionType,
    Roles,
    OperationType as bloqitOperationType,
} from '../../../../bloqit/rents/models/rent';
import type { AccessToken } from '../../models/access-token';
import type { DHLCZEvent } from '../../models/event';
import {
    type DataElementGeneral,
    type DHLCZDataElementTemplate,
    type DHLCZDataElement,
    ProductType,
    DHLCZEventCodes,
    DHLCZLocationCodes,
    DHLCZCountryCodes,
    DHLCZReasons,
} from '../../models/dataElement';
import {
    IdMappingDirection,
    type IdMappingRepository,
} from '../../../../core/identifier-mapping/repository';
import { type DataElement, PaymentStatus } from '../../models/get-shipment-response';
import * as UUIDGenerator from '../../../../core/utils/uuid-generator';
import { type Logger } from '../../../../core/logger';
import { AbstractPartnerHandler } from '../../../../core/partner-registry/handler/abstract';
import * as DateTime from '../../../../core/utils/date-time';

export class DHLCZHandlerImpl extends AbstractPartnerHandler {
    private readonly dhlCZClient: DHLCZClient;
    private readonly idMappingRepository: IdMappingRepository;
    private accessToken: AccessToken | undefined;
    private readonly logger: Logger;

    constructor(deps: {
        dhlCZClient: DHLCZClient;
        idMappingRepository: IdMappingRepository;
        logger: Logger;
    }) {
        super({ partnerFriendlyId: 'dhl_cz' });
        this.accessToken = undefined;
        this.dhlCZClient = deps.dhlCZClient;
        this.idMappingRepository = deps.idMappingRepository;
        this.logger = deps.logger;
        this.getAccessToken = this.getAccessToken.bind(this);
        this.getRentById = this.getRentById.bind(this);
        this.canBePickedUp = this.canBePickedUp.bind(this);
        this.setConfig = this.setConfig.bind(this);
        this.authenticateCourier = this.authenticateCourier.bind(this);
        this.constructBaseEvent = this.constructBaseEvent.bind(this);
        this.notifyDropOffConfirmation = this.notifyDropOffConfirmation.bind(this);
        this.notifyCollectItem = this.notifyCollectItem.bind(this);
        this.notifyExpiredRent = this.notifyExpiredRent.bind(this);
        this.notifyStuckRent = this.notifyStuckRent.bind(this);
        this.notifyViolence = this.notifyViolence.bind(this);
        this.notifyPing = this.notifyPing.bind(this);
        this.associateLabel = this.associateLabel.bind(this);
        this.isCodeValid = this.isCodeValid.bind(this);
    }

    static parseCodeNumOnlyOrFull(code: string): string {
        const onlyNumbers: RegExp = /^\d+$/;
        const noDash = code.replace(/-/g, '');
        if (onlyNumbers.test(noDash)) {
            return noDash.substring(0, 11);
        }
        return code;
    }

    private async getAccessPointId(bloqId: string): Promise<string> {
        const accessPointId = await this.idMappingRepository.fetch({
            direction: IdMappingDirection.PARTNER,
            matching: { bloqitId: bloqId },
        });

        if (accessPointId === undefined) throw new Error('Missing accessPointId');
        return accessPointId;
    }

    private async getSlotId(lockerId: string): Promise<string> {
        const slotId = await this.idMappingRepository.fetch({
            direction: IdMappingDirection.PARTNER,
            matching: { bloqitId: lockerId },
        });
        if (slotId === undefined) throw new Error('Missing slotId');
        return slotId;
    }

    private constructBaseEvent(props: {
        event: RentEvent;
        accessPointId: string;
        slotId?: string;
    }): {
        dataRequest: DHLCZDataRequest;
        dataElementTemplate: DHLCZDataElementTemplate;
    } {
        const dataRequest: DHLCZDataRequest = {
            externalPartner: ExternalPartner,
            businessType: BusinessType.PPL,
            accessPointId: props.accessPointId,
            slotId: props.slotId,
            sessionId: UUIDGenerator.generate(),
            parcelIdentifier: props.event.metadata.externalID,
            applicationLocale: ApplicationLocale.cz,
        };

        const general: DataElementGeneral = {
            product: ProductType.parcelConnect,
            parcelIdentifier: [props.event.metadata.externalID],
            routingCode: '2LCZ35002+********',
            timestamp: props.event.timestamp,
        };

        const dataElementTemplate: DHLCZDataElementTemplate = {
            version: '0200',
            dataElementType: 'event',
            parcelOriginOrganization: 'CZ-7001',
            parcelDestinationOrganization: 'CZ-7001',
            dataElementOriginOrganization: 'CZ-7001',
            distributionTimestamp: props.event.timestamp,
            general,
        };

        return {
            dataRequest,
            dataElementTemplate,
        };
    }

    private async getAccessToken(): Promise<AccessToken> {
        if (this.accessToken?.token === undefined || this.accessToken.validity < new Date()) {
            const oauthToken = await this.dhlCZClient.getAccessToken();
            this.accessToken = {
                token: oauthToken.access_token,
                validity: new Date(new Date().getTime() + oauthToken.expires_in * 1000),
            };
        }
        return this.accessToken;
    }

    override async getRentById(props: {
        partnerRentId: string;
        bloqId: string;
        locale: string;
        courier?: string;
    }): Promise<Rent[]> {
        const { partnerRentId, bloqId, courier, locale } = props;
        const operationType =
            courier !== undefined ? OperationType.LAST_MILE : OperationType.FIRST_MILE;

        const accessToken = await this.getAccessToken();
        const accessPointId = await this.getAccessPointId(bloqId);

        const { dataElement: dhlRent } = await this.dhlCZClient.getShipment({
            accessToken,
            parcelIdentifier: DHLCZHandlerImpl.parseCodeNumOnlyOrFull(partnerRentId),
            accessPointId,
            operationType,
            locale,
            driverId: courier,
        });

        const shipments: DataElement[] = [dhlRent];
        const shipmentSetIds: string[] | undefined =
            dhlRent.xPAN.hierarchy?.operations.referenceChild?.identifier;
        const parentShipmentId = dhlRent.xPAN.hierarchy?.operations.referenceParent.identifier;
        let parentShipment: DataElement | undefined;
        if (shipmentSetIds !== undefined && parentShipmentId !== undefined) {
            shipmentSetIds.push(parentShipmentId);
            for (const id of shipmentSetIds) {
                if (shipments.find(s => s.general.parcelIdentifier[0] === id) === undefined) {
                    const { dataElement: dhlRent } = await this.dhlCZClient.getShipment({
                        accessToken,
                        parcelIdentifier: id,
                        accessPointId,
                        operationType:
                            courier !== undefined
                                ? OperationType.LAST_MILE
                                : OperationType.FIRST_MILE,
                        locale,
                        driverId: courier,
                    });
                    shipments.push(dhlRent);
                }
                parentShipment = shipments.find(
                    s => s.general.parcelIdentifier[0] === parentShipmentId,
                );
            }
        }

        const bloqitRents: Rent[] = [];

        shipments.forEach(shipment => {
            const parcelIdentifier = shipment.general.parcelIdentifier[0];
            const pickUpCodes = shipment.xPAN.featureCodes.feature.find(
                f => f.name.includes('SmartPin') || f.name.includes('PIN'),
            )?.textValue[0];
            const packageSize = shipment.xPAN.features.physical;
            const isLabelless =
                shipment.xPAN.featureCodes.feature.find(f => f.name.includes('Labelless'))
                    ?.textValue[0] === 'True';
            const prePickupActions: PrePickupAction[] = [];

            const parentHasPaymentStatus = Object.values(PaymentStatus).includes(
                parentShipment?.payment?.paymentStatus as PaymentStatus,
            );
            const hasPaymentStatus = Object.values(PaymentStatus).includes(
                shipment.payment?.paymentStatus as PaymentStatus,
            );

            if ((parentShipment !== undefined && parentHasPaymentStatus) || hasPaymentStatus) {
                prePickupActions.push({
                    order: 0,
                    action: PrePickupActionType.VERIFY_PICKUP,
                });
            }

            if (isLabelless) {
                prePickupActions.push({
                    order: 0,
                    action: PrePickupActionType.ASSOCIATE_LABEL,
                });
            }

            const customer =
                operationType === OperationType.FIRST_MILE
                    ? shipment.xPAN.addresses.sender[0]
                    : shipment.xPAN.addresses.recipient[0];

            const bloqItRent: Rent = {
                externalID: parcelIdentifier,
                dropOffCode: parcelIdentifier,
                dimensions: {
                    length: packageSize !== undefined ? Number(packageSize.length) / 10 : 0,
                    width: packageSize !== undefined ? Number(packageSize.width) / 10 : 0,
                    height: packageSize !== undefined ? Number(packageSize.height) / 10 : 0,
                },
                customer: {
                    name: customer.name,
                    email: customer.email,
                },
                pickUpCodes:
                    pickUpCodes !== undefined
                        ? {
                              pin: pickUpCodes,
                          }
                        : undefined,
                prePickupActions,
                expiryDate:
                    operationType === OperationType.FIRST_MILE
                        ? DateTime.daysInTheFuture(90)
                        : DateTime.daysInTheFuture(3),
                postPickupAction: [],
                setID:
                    shipmentSetIds !== undefined && shipmentSetIds.length > 0
                        ? shipment.xPAN.hierarchy?.operations.referenceParent.identifier
                        : undefined,
                priority: isLabelless ? 1 : 0,
            };
            bloqitRents.push(bloqItRent);
        });

        return bloqitRents;
    }

    override async canBePickedUp(props: {
        partnerRentId: string;
        bloqId: string;
        operationType: bloqitOperationType;
    }): Promise<boolean> {
        const { partnerRentId, bloqId, operationType } = props;
        const accessToken = await this.getAccessToken();

        const accessPointId = await this.getAccessPointId(bloqId);

        const { dataElement: dhlRent } = await this.dhlCZClient.getShipment({
            accessToken,
            parcelIdentifier: partnerRentId,
            accessPointId,
            operationType:
                operationType === bloqitOperationType.LAST_MILE
                    ? OperationType.LAST_MILE
                    : OperationType.FIRST_MILE,
            locale: 'en',
            driverId: '',
        });

        const parentShipmentId = dhlRent.xPAN.hierarchy?.operations.referenceParent.identifier;
        let parentShipment = dhlRent;
        if (
            parentShipmentId !== undefined &&
            parentShipmentId !== dhlRent.general.parcelIdentifier[0]
        ) {
            const { dataElement: parentDhlRent } = await this.dhlCZClient.getShipment({
                accessToken,
                parcelIdentifier: parentShipmentId,
                accessPointId,
                operationType:
                    operationType === bloqitOperationType.LAST_MILE
                        ? OperationType.LAST_MILE
                        : OperationType.FIRST_MILE,
                locale: 'en',
                driverId: '',
            });
            parentShipment = parentDhlRent;
        }

        const paymentStatus = parentShipment.payment.paymentStatus ?? '';

        return paymentStatus === PaymentStatus.paid;
    }

    override async setConfig(
        changeState: BloqChangeState | LockerChangeState,
    ): Promise<BloqChangeStateResponse | LockerChangeStateResponse> {
        const accessToken = await this.getAccessToken();

        const accessPointId = await this.getAccessPointId(changeState.bloq);

        let slotId: string | undefined;

        if ('locker' in changeState) {
            slotId = await this.getSlotId(changeState.locker);
        }

        const dataRequest: DHLCZDataRequest = {
            externalPartner: ExternalPartner,
            businessType: BusinessType.PPL,
            accessPointId,
            slotId,
            sessionId: UUIDGenerator.generate(),
        };

        let command: Command = Command.activate;
        if (
            changeState.codeName === BloqCodeName.deactivate ||
            changeState.codeName === LockerCodeName.deactivate
        ) {
            command = Command.deactivate;
        }

        const dataConfig: DHLCZDataConfig = {
            command,
        };

        const response: DHLCZDataResponse = await this.dhlCZClient.setConfig(
            accessToken,
            dataRequest,
            dataConfig,
        );

        return { success: response.status === Status.OK };
    }

    override async authenticateCourier(args: {
        pin: string;
        bloq?: string;
    }): Promise<AuthenticationResult> {
        const { pin, bloq } = args;

        if (bloq === undefined) throw new Error('bloq is required');

        const accessToken = await this.getAccessToken();

        const accessPointId = await this.getAccessPointId(bloq);

        const dataRequest: DHLCZDataRequest = {
            accessPointId,
            externalPartner: ExternalPartner,
            businessType: BusinessType.PPL,
            driverId: pin,
            accessRole: AccessRole.courier,
            sessionId: UUIDGenerator.generate(),
        };

        try {
            const { dataResponse, dataAuthenticate } = await this.dhlCZClient.authenticateCourier(
                accessToken,
                dataRequest,
            );
            this.logger.info({ message: 'dhl:cz:courier_auth_outcome', data: { dataResponse, dataAuthenticate } });
            const permissions: COURIER_PERMISSIONS[] = [];

            if (dataAuthenticate.dropOffAllowed) {
                permissions.push(COURIER_PERMISSIONS.DROP_OFF);
            };

            if (dataAuthenticate.pickUpAllowed) {
                permissions.push(COURIER_PERMISSIONS.PICK_UP);
            };

            return { success: dataResponse.status === Status.OK, permissions };
        } catch (ex) {
            const error = ex as Error;
            this.logger.error({
                message: 'dhl:cz:courier_auth_error',
                error: { message: error.message, stack: error.stack },
            });
            return { success: false, permissions: [] };
        }
    }

    override async notifyDropOffConfirmation(event: RentEvent): Promise<void> {
        const accessToken = await this.getAccessToken();
        const accessPointId = await this.getAccessPointId(event.bloq);

        const baseEvent = this.constructBaseEvent({
            event,
            accessPointId,
            slotId: event.metadata.lockerExternalID,
        });

        if (event.actionInitiatedBy.role === Roles.COURIER) {
            const courierDelivery: DHLCZEvent = {
                dataRequest: {
                    ...baseEvent.dataRequest,
                    driverId: event.actionInitiatedBy.id,
                    operationType: OperationType.LAST_MILE,
                },
                dataElement: {
                    ...baseEvent.dataElementTemplate,
                    event: {
                        parcelEuropeSpecificData: {
                            eventCode: DHLCZEventCodes.driverDelivery,
                            country: DHLCZCountryCodes.CZ,
                            location: DHLCZLocationCodes.parcelStation,
                            document: [
                                {
                                    type: 'Note2',
                                    reference: accessPointId,
                                },
                            ],
                        },
                    },
                },
            };
            await this.dhlCZClient.notifyEvent(accessToken, courierDelivery);
        } else if (event.actionInitiatedBy.role === Roles.CUSTOMER) {
            const customerDelivery: DHLCZEvent = {
                dataRequest: {
                    ...baseEvent.dataRequest,
                    driverId: event.actionInitiatedBy.id,
                    operationType: OperationType.FIRST_MILE,
                },
                dataElement: {
                    ...baseEvent.dataElementTemplate,
                    event: {
                        parcelEuropeSpecificData: {
                            eventCode: DHLCZEventCodes.customerDelivery,
                            country: DHLCZCountryCodes.CZ,
                            location: DHLCZLocationCodes.parcelStation,
                            deliveryAddress: {
                                email: event.customer.email,
                                type: DHLCZLocationCodes.parcelStation,
                            },
                            document: [
                                {
                                    type: 'Note2',
                                    reference: accessPointId,
                                },
                            ],
                        },
                    },
                },
            };
            await this.dhlCZClient.notifyEvent(accessToken, customerDelivery);
        }
    }

    override async notifyCollectItem(event: RentEvent): Promise<void> {
        const role: Roles = event.actionInitiatedBy?.role ?? Roles.CUSTOMER;

        if (!Object.values(Roles).includes(role)) {
            throw new Error(
                `Invalid role associated with the COLLECT_ITEM event with id ${event._id}`,
            );
        }

        const accessToken = await this.getAccessToken();
        const accessPointId = await this.getAccessPointId(event.bloq);

        const eventCreationOptions = { accessPointId, role };
        const pickupEvent = this.createPickupEventFrom(event, eventCreationOptions);
        await this.dhlCZClient.notifyEvent(accessToken, pickupEvent);
    }

    private createPickupEventFrom(
        rentEvent: RentEvent,
        options: { accessPointId: string; role: Roles },
    ): DHLCZEvent {
        const { accessPointId } = options;
        const event = rentEvent;
        const slotId = event.metadata.lockerExternalID;
        const isCustomer = options.role === Roles.CUSTOMER;

        const deliveryAddress =
            isCustomer && rentEvent.customer ? { email: rentEvent.customer.email } : undefined;
        const eventCode = isCustomer
            ? DHLCZEventCodes.customerPickUp
            : DHLCZEventCodes.driverPickUp;

        const baseEvent = this.constructBaseEvent({ event, accessPointId, slotId });
        return {
            dataRequest: {
                ...baseEvent.dataRequest,
                driverId: event.actionInitiatedBy.id,
                operationType: OperationType.LAST_MILE,
            },
            dataElement: {
                ...baseEvent.dataElementTemplate,
                event: {
                    parcelEuropeSpecificData: {
                        eventCode,
                        country: DHLCZCountryCodes.CZ,
                        location: DHLCZLocationCodes.parcelStation,
                        document: [{ type: 'Note2', reference: accessPointId }],
                        deliveryAddress,
                    },
                },
            },
        };
    }

    override async notifyExpiredRent(event: RentEvent): Promise<void> {
        const accessToken = await this.getAccessToken();
        const accessPointId = await this.getAccessPointId(event.bloq);

        const baseEvent = this.constructBaseEvent({
            event,
            accessPointId,
            slotId: event.metadata.lockerExternalID,
        });

        const parcelExpired: DHLCZEvent = {
            dataRequest: {
                ...baseEvent.dataRequest,
                driverId: event.actionInitiatedBy.id,
                operationType:
                    event.metadata.rentOperationType === bloqitOperationType.FIRST_MILE
                        ? OperationType.FIRST_MILE
                        : OperationType.LAST_MILE,
            },
            dataElement: {
                ...baseEvent.dataElementTemplate,
                event: {
                    parcelEuropeSpecificData: {
                        eventCode: DHLCZEventCodes.parcelExpired,
                        country: DHLCZCountryCodes.CZ,
                        location: DHLCZLocationCodes.parcelStation,
                        document: [
                            {
                                type: 'Note',
                                reference: 'Příjemce zásilku nevyzvedl',
                            },
                            {
                                type: 'Note2',
                                reference: accessPointId,
                            },
                        ],
                    },
                },
            },
        };
        await this.dhlCZClient.notifyEvent(accessToken, parcelExpired);
    }

    override async notifyStuckRent(event: RentEvent): Promise<void> {
        if (event.rent === undefined) {
            return;
        }

        const accessToken = await this.getAccessToken();
        const accessPointId = await this.getAccessPointId(event.bloq);

        const baseEvent = this.constructBaseEvent({
            event,
            accessPointId,
            slotId: event.metadata.lockerExternalID,
        });

        const parcelStuck: DHLCZEvent = {
            dataRequest: {
                ...baseEvent.dataRequest,
                driverId: event.actionInitiatedBy.id,
                operationType:
                    event.metadata.rentOperationType === bloqitOperationType.FIRST_MILE
                        ? OperationType.FIRST_MILE
                        : OperationType.LAST_MILE,
            },
            dataElement: {
                ...baseEvent.dataElementTemplate,
                event: {
                    parcelEuropeSpecificData: {
                        eventCode: DHLCZEventCodes.parcelStuck,
                        country: DHLCZCountryCodes.CZ,
                        location: DHLCZLocationCodes.parcelStation,
                        reason: DHLCZReasons.stuck,
                        document: [
                            {
                                type: 'Note',
                                reference: event.metadata.externalID,
                            },
                            {
                                type: 'Note2',
                                reference: accessPointId,
                            },
                        ],
                    },
                },
            },
        };

        await this.dhlCZClient.notifyEvent(accessToken, parcelStuck);
    }

    override async notifyViolence(event: RentEvent): Promise<void> {
        const accessToken = await this.getAccessToken();
        const accessPointId = await this.getAccessPointId(event.bloq);

        const baseEvent = this.constructBaseEvent({
            event,
            accessPointId,
            slotId: event.metadata.lockerExternalID,
        });
        const violence: DHLCZEvent = {
            dataRequest: {
                ...baseEvent.dataRequest,
                driverId: event.actionInitiatedBy.id,
                operationType:
                    event.metadata.rentOperationType === bloqitOperationType.FIRST_MILE
                        ? OperationType.FIRST_MILE
                        : OperationType.LAST_MILE,
            },
            dataElement: {
                ...baseEvent.dataElementTemplate,
                event: {
                    parcelEuropeSpecificData: {
                        eventCode: DHLCZEventCodes.violence,
                        country: DHLCZCountryCodes.CZ,
                        location: DHLCZLocationCodes.parcelStation,
                        reason: DHLCZReasons.violence,
                        document: [
                            {
                                type: 'Note2',
                                reference: accessPointId,
                            },
                        ],
                    },
                },
            },
        };

        await this.dhlCZClient.notifyEvent(accessToken, violence);
    }

    override async notifyPing(event: BloqChangeState): Promise<void> {
        /* const accessToken = await this.getAccessToken();
        const baseEvent = this.constructBaseEvent(event);

        const ping: DHLCZEvent = {
            ...baseEvent,
            parcelEuropeSpecificData: {
                eventCode: DHLCZEventCodes.ping,
                country: DHLCZCountryCodes.CZ,
                location: DHLCZLocationCodes.parcelStation,
                reason: DHLCZReasons.ping,
                document: [
                    {
                        type: 'Note2',
                        reference: accessPointId,
                    },
                ],
            },
        };
        await this.dhlCZClient.notifyEvent(accessToken, ping); */
    }
    // TODO - REFACTOR LOGIC FOR THE EVENT PING

    private isCodeValid(code: string): boolean {
        const label: RegExp = /^MI[0-9]{10,10}NI$/;
        return label.test(code);
    }

    override async associateLabel(props: {
        partnerRentId: string;
        bloqId: string;
        operationType: bloqitOperationType;
        courier: string;
        locale: string;
        code: string;
        bloqExternalId?: string;
        country?: string;
    }): Promise<boolean> {
        const { partnerRentId, bloqId, operationType, courier, locale, code } = props;

        if (!this.isCodeValid(code)) {
            this.logger.info({ message: 'Label association failed in pre validation', code });
            return false;
        }

        const accessToken = await this.getAccessToken();

        const accessPointId = await this.getAccessPointId(bloqId);

        const dataRequest: DHLCZDataRequest = {
            sessionId: UUIDGenerator.generate(),
            driverId: courier,
            externalPartner: ExternalPartner,
            operationType:
                operationType === bloqitOperationType.FIRST_MILE
                    ? OperationType.FIRST_MILE
                    : OperationType.LAST_MILE,
            businessType: BusinessType.PPL,
            accessPointId,
            parcelIdentifier: partnerRentId,
            applicationLocale: locale === '' ? (locale as ApplicationLocale) : ApplicationLocale.cz,
        };

        const now = new Date();
        const dataElement: DHLCZDataElement = {
            version: '0200',
            dataElementType: 'event',
            parcelOriginOrganization: 'CZ-7001',
            parcelDestinationOrganization: 'CZ-7001',
            dataElementOriginOrganization: 'CZ-7001',
            distributionTimestamp: now,
            general: {
                product: ProductType.parcelConnect,
                parcelIdentifier: [partnerRentId],
                routingCode: '2LCZ35002+********',
                timestamp: now,
            },
            event: {
                parcelEuropeSpecificData: {
                    eventCode: DHLCZEventCodes.customerDelivery,
                    country: DHLCZCountryCodes.CZ,
                    location: DHLCZLocationCodes.parcelStation,
                    reason: DHLCZReasons.associateLabel,
                    document: [
                        {
                            type: 'Minilabel',
                            reference: code,
                        },
                    ],
                },
            },
        };

        const response: DHLCZDataResponse = await this.dhlCZClient.associateLabel(
            accessToken,
            dataRequest,
            dataElement,
        );

        return response.status === Status.OK;
    }

    override async handleRentFinishedEvent(_event: RentEvent): Promise<void> {}
}
