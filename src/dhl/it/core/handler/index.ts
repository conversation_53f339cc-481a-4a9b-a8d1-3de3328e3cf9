import { randomUUID } from 'crypto';

import { type DHLITShipment, type DHLITCourierShipment, type DHLITClient } from '../../client';
import {
    type Rent,
    type RentEvent,
    type Customer,
    Roles,
} from '../../../../bloqit/rents/models/rent';
import { AbstractPartnerHandler } from '../../../../core/partner-registry/handler/abstract';
import { type AuthenticationResult } from '../../../../bloqit/couriers/models/authentication-result';
import { type Logger } from '../../../../core/logger';
import { type ApplicationEnvironment } from '../../../../core/env';
import * as DateTime from '../../../../core/utils/date-time';

import { type BloqEvent } from '../../../../bloqit/bloqs/models';
import { type CourierPickupSessionsRepository } from '../../data-access/repositories/courier-pickup-session';
import { type ExpiredRentsNotificationBatch, type ExpiredRent, type Product } from '../../models';
import * as pickUpErrorReasons from '../../../../bloqit/rents/models/pickup-error-reasons';
import { type ExpiredRentsRepository } from '../../data-access/repositories/expired-rents';
import { type ExpiredRentsNotificationBatchRepository } from '../../data-access/repositories/expired-rents-notification-batch';

const MAX_EXPIRED_RENT_NOTIFICATION_ATTEMPTS = 3;
const DEFAULT_PRIORITY = 1;

const padLeft = (n: number): string => n.toString().padStart(2, '0');

interface EnhanceExpiredRentNotificationBatch {
    id?: string;
    failureCount: number;
    rents: ExpiredRent[];
    isPreexistent: boolean;
    success: boolean;
}

export class DHLITHandlerImpl extends AbstractPartnerHandler {
    private readonly logger: Logger;
    private readonly env: ApplicationEnvironment;
    private readonly dhlITClient: DHLITClient;
    private readonly courierPickupSessionsRepository: CourierPickupSessionsRepository;
    private readonly expiredRentsRepository: ExpiredRentsRepository;
    private readonly expiredRentsNotificationBatchRepository: ExpiredRentsNotificationBatchRepository;

    constructor(deps: {
        dhlITClient: DHLITClient;
        courierPickupSessionsRepository: CourierPickupSessionsRepository;
        expiredRentsRepository: ExpiredRentsRepository;
        expiredRentsNotificationBatchRepository: ExpiredRentsNotificationBatchRepository;
        env: ApplicationEnvironment;
        logger: Logger;
    }) {
        super({ partnerFriendlyId: 'dhl_it' });

        this.dhlITClient = deps.dhlITClient;
        this.courierPickupSessionsRepository = deps.courierPickupSessionsRepository;
        this.expiredRentsRepository = deps.expiredRentsRepository;
        this.expiredRentsNotificationBatchRepository = deps.expiredRentsNotificationBatchRepository;
        this.logger = deps.logger;
        this.env = deps.env;

        this.notifyCollectItem = this.notifyCollectItem.bind(this);
        this.getRentById = this.getRentById.bind(this);
        this.canBePickedUp = this.canBePickedUp.bind(this);
        this.setConfig = this.setConfig.bind(this);
        this.authenticateCourier = this.authenticateCourier.bind(this);
        this.notifyDropOffConfirmation = this.notifyDropOffConfirmation.bind(this);
        this.notifyExpiredRent = this.notifyExpiredRent.bind(this);
        this.notifyStuckRent = this.notifyStuckRent.bind(this);
        this.notifyViolence = this.notifyViolence.bind(this);
        this.notifyPing = this.notifyPing.bind(this);
        this.associateLabel = this.associateLabel.bind(this);
        this.mapExpiredRentToProductInfo = this.mapExpiredRentToProductInfo.bind(this);
    }

    get batchExpiredRentsNotifiationWindow(): {
        start: { hour: number; minute: number };
        end: { hour: number; minute: number };
    } {
        const {
            DHL_IT_BATCH_EXPIRED_RENTS_NOTIFICATION_WINDOW_START: start,
            DHL_IT_BATCH_EXPIRED_RENTS_NOTIFICATION_WINDOW_END: end,
        } = this.env;

        const [startHour, startMinute] = DateTime.extractHourMinuteFromTimeStr(start);
        const [endHour, endMinute] = DateTime.extractHourMinuteFromTimeStr(end);

        return {
            start: { hour: startHour, minute: startMinute },
            end: { hour: endHour, minute: endMinute },
        };
    }

    private parseCode(code: string): string {
        if (code.length === 13) {
            return code;
        }

        const regex: RegExp =
            /^(JVGLOTC[0-9]{10}|J(JD|VG)(0|1|L)([0-9]){9,31}|JJH30([0-9]){8,30}|JJATA([0-9]){8,30}|JJFI0000([0-9]){5,27}|00[0-9]{18}|JJITAXP(1|R)8U[WYZ][0-9A-Z]{4}[0-9]{6}[A-Z]{2})$/;
        if (regex.test(code)) {
            return code;
        }

        if (code.length === 16) {
            return code.substring(3);
        }

        throw new Error(`Shipment code not acceptable: ${code}`);
    }

    override async getRentById(props: {
        partnerRentId: string;
        bloqId: string;
        courier?: string;
        locale: string;
        nodeId: string;
    }): Promise<Rent[]> {
        const { partnerRentId, bloqId, courier, nodeId } = props;
        const isCourier = courier !== undefined;

        const shipmentId = this.parseCode(partnerRentId);

        if (isCourier) {
            const response = await this.dhlITClient.fetchShipmentInfoForCourierDropoff({
                agentId: bloqId,
                shipmentId,
                nodeId,
            });

            return [
                this.createBloqitRentFromCourierShipmentInfo({
                    partnerRentId: shipmentId,
                    shipmentResponse: response,
                }),
            ];
        } else {
            const dhlITShipment = await this.dhlITClient.fetchShipmentInfoForCustomerDropoff({
                productId: shipmentId,
                agentId: bloqId,
                nodeId,
            });

            return [
                this.createBloqitRentFromCustomerShipmentInfo({
                    partnerRentId: shipmentId,
                    dhlITShipment,
                }),
            ];
        }
    }

    override async notifyDropOffConfirmation(event: RentEvent): Promise<void> {
        const role = event.actionInitiatedBy.role;

        if (role === Roles.CUSTOMER) await this.notifyDropOffConfirmationForCustomer(event);
        else if (role === Roles.COURIER) await this.notifyDropOffConfirmationForCourier(event);
        else throw new Error('Invalid role provided for DROP_OFF_CONFIRMATION event');
    }

    private async notifyDropOffConfirmationForCustomer(event: RentEvent): Promise<void> {
        const { bloq, metadata, customer, timestamp } = event;
        const [agentId, product, nodeId, customerEmail, time] = [
            bloq,
            {
                productId: metadata.externalID,
                product: metadata.rentMetadata.productType,
            },
            metadata.bloqExternalID,
            customer.email,
            timestamp,
        ];

        const args = { agentId, product, fromNode: nodeId, customerEmail, time };
        await this.dhlITClient.sendCustomerDropOffStatusUpdate(args);
    }

    private async notifyDropOffConfirmationForCourier(event: RentEvent): Promise<void> {
        const { bloq, timestamp, metadata } = event;
        const [agentId, product, nodeId] = [
            bloq,
            {
                productId: metadata.externalID,
                product: metadata.rentMetadata.productType,
            },
            metadata.bloqExternalID,
        ];

        const args = { nodeId, agentId, product, pin: metadata.pickUpCode, time: timestamp };
        await this.dhlITClient.notifyCourierShipmentDropOff(args);
    }

    override async notifyCollectItem(_event: RentEvent): Promise<void> {}

    override async authenticateCourier(args: {
        pin: string;
        bloq?: string;
        bloqExternalId?: string;
        courierSessionId?: string;
    }): Promise<AuthenticationResult> {
        const { pin, bloqExternalId, courierSessionId } = args;

        if (pin === this.env.BLOQIT_COURIER_BYPASS_PIN) return { success: true };

        if (bloqExternalId === undefined) throw new Error('bloqExternalId is required');
        if (courierSessionId === undefined) throw new Error('courierSessionId is required');

        try {
            const userIdHash = await this.dhlITClient.authenticateCourier({
                pin,
                courierSessionId,
                nodeId: bloqExternalId,
            });

            return { success: userIdHash !== undefined };
        } catch (ex) {
            const { message, stack } = ex as Error;
            this.logger.error({ message, stack });
            return { success: false };
        }
    }

    async handleCourierLogout(event: BloqEvent): Promise<void> {
        const { bloq, timestamp, actionInitiatedBy, metadata } = event;
        const { id: courierId, session: sessionId } = actionInitiatedBy;

        if (sessionId === undefined) throw new Error('No session id was present in courier logout');

        const sessionsExists = await this.courierPickupSessionsRepository.exists({
            courierId,
            sessionId,
        });

        if (!sessionsExists) return;

        const products = await this.courierPickupSessionsRepository.fetchProducts({
            courierId,
            sessionId,
        });

        if (products.length === 0) return;

        await this.dhlITClient.createProductsOnPartnerSide({
            agentId: bloq,
            nodeId: metadata.bloqExternalID,
            time: timestamp,
            products,
        });

        await this.courierPickupSessionsRepository.delete({ courierId, sessionId });
    }

    private async handleRentFinishedByCustomer(event: RentEvent): Promise<void> {
        const { bloq, timestamp, metadata } = event;
        const [agentId, time, nodeId, product] = [
            bloq,
            timestamp,
            metadata.bloqExternalID,
            {
                productId: metadata.externalID,
                product: metadata.rentMetadata.productType,
            },
        ];

        await this.dhlITClient.notifyCustomerPickUp({ agentId, time, nodeId, product });
    }

    private createBloqitRentFromCourierShipmentInfo(args: {
        partnerRentId: string;
        shipmentResponse: DHLITCourierShipment;
    }): Rent {
        const { shipmentResponse, partnerRentId } = args;
        const digest = shipmentResponse.digests?.[0];
        const shipment = shipmentResponse.containers[0].products[0];
        const { receiverEmail, receiverName } = shipment;
        const customer = this.mapCourierShipmentCustomerFrom({ receiverEmail, receiverName });
        const expiryDate = this.assignRandomHourToDate(
            shipment.expiry !== undefined
                ? new Date(shipment.expiry)
                : DateTime.daysInTheFuture(10),
        );

        return {
            setID: undefined,
            pickUpCodes: undefined,
            prePickupActions: [],
            postPickupAction: [],
            customer,
            expiryDate,
            externalID: partnerRentId,
            dropOffCode: partnerRentId,
            priority: this.mapShipmentPriorityToBloqitPriority(digest?.class),
            dimensions: { length: shipment.depth, width: shipment.width, height: shipment.height },
            metadata: { productType: shipment.product },
        };
    }

    private createBloqitRentFromCustomerShipmentInfo(props: {
        partnerRentId: string;
        dhlITShipment: DHLITShipment;
    }): Rent {
        const { partnerRentId, dhlITShipment } = props;
        return {
            setID: undefined,
            pickUpCodes: undefined,
            externalID: partnerRentId,
            dropOffCode: partnerRentId,
            expiryDate: this.assignRandomHourToDate(DateTime.daysInTheFuture(90)),
            customer: this.mapShipmentCustomerFrom(dhlITShipment),
            prePickupActions: [],
            postPickupAction: [],
            priority: this.mapShipmentPriorityToBloqitPriority(dhlITShipment.class),
            dimensions: {
                length: dhlITShipment.depth,
                width: dhlITShipment.width,
                height: dhlITShipment.height,
            },
            metadata: {
                productType: dhlITShipment.product,
            },
        };
    }

    /*
        DHL wants the rents to expire between 00h00 and 01h15 (UTC+1) (Italy time),
        so we'll have a cronjob running everyday an hour prior to this time to make sure we're
        sending them the rents around this target time. That's why we're expiring the rents
        around 22h GMT-0
    */
    private assignRandomHourToDate(date: Date): Date {
        const randomMinutes = Math.floor(Math.random() * 46);
        return new Date(date.getFullYear(), date.getMonth(), date.getDate(), 22, randomMinutes);
    }

    private isNonEmptyString(value: string | undefined): boolean {
        return value !== undefined && value !== '';
    }

    private mapCourierShipmentCustomerFrom(args: {
        receiverEmail?: string;
        receiverName?: string;
    }): Customer | undefined {
        const { receiverEmail, receiverName } = args;
        const hasName = this.isNonEmptyString(receiverName);
        const hasEmail = this.isNonEmptyString(receiverEmail);
        const name = hasName ? receiverName : undefined;
        const email = hasEmail ? receiverEmail : undefined;
        return hasName || hasEmail ? { name, email } : undefined;
    }

    private mapShipmentCustomerFrom({
        senderEmail,
        senderPhone,
    }: DHLITShipment): Customer | undefined {
        if (
            senderEmail !== undefined &&
            senderPhone !== undefined &&
            senderEmail !== '' &&
            senderPhone !== ''
        ) {
            return { email: senderEmail, phone: senderPhone };
        }
    }

    private mapShipmentPriorityToBloqitPriority(shipmentPriority?: string): number {
        if (shipmentPriority === undefined) return DEFAULT_PRIORITY;
        if (shipmentPriority === 'J1') return 2;
        if (shipmentPriority === 'J4') return 1;
        return 0;
    }

    override async notifyExpiredRent(event: RentEvent): Promise<void> {
        const { rent: rentId, metadata } = event;
        const { partnerFriendlyId } = this;
        const expiredRentInfo = { rentId, partnerFriendlyId, metadata };
        await this.expiredRentsRepository.create(expiredRentInfo);
    }

    private isBatchNotificationWindowOpen(): boolean {
        const { start, end } = this.batchExpiredRentsNotifiationWindow;
        const [romeHour, romeMinute] = DateTime.getLocalTimeAt(DateTime.WorldTimeZones.ROME);

        if (romeHour < start.hour || romeHour > end.hour) return false;
        if (romeHour === start.hour && romeMinute < start.minute) return false;
        if (romeHour === end.hour && romeMinute > end.minute) return false;
        return true;
    }

    /*
        The rationale for all the decisions made in the method below is documented on the following
        Notion page: https://www.notion.so/bloqit-dashboard/Technical-spec-Expired-rents-notification-batch-for-DHL-IT-3427b15c843a4de8bf8f740ee2319e33

        Please refer to it for more information and please update it whenever you make changes
        to the logic below
    */
    async notifyExpiredRents(args: { batchSize: number }): Promise<void> {
        const { batchSize } = args;

        if (!this.isBatchNotificationWindowOpen()) {
            this.logNotificationWindowClosed();
            return;
        }

        const previouslyFailedBatches = await this.expiredRentsNotificationBatchRepository.list();
        const rentsInFailedBatches = previouslyFailedBatches.flatMap(b => b.rentIds);
        const expiredRents = await this.expiredRentsRepository.list({});

        const batches = this.enhancePreexistentBatchesThatFailed(
            previouslyFailedBatches,
            expiredRents,
        );

        const remainingRents = expiredRents.filter(r => !rentsInFailedBatches.includes(r.rentId));
        batches.push(
            ...this.buildExpiredRentNotificationBatchesFromRents(remainingRents, batchSize),
        );

        if (batches.length === 0) {
            this.logger.info({
                message: 'Skipping expired rent notifications: no expired rents were found',
            });
            return;
        }

        const result = await this.dispatchExpiredRentNotificationBatches(batches);

        const {
            preexistentBatchesThatSucceded,
            preexistentBatchesThatFailed,
            newFailedBatches,
            preexistentBatchesThatExceededMaxAttempts,
            successfullyNotifiedRents,
            rentsThatExceededMaxAttempts,
        } = this.extractBatchNotificationResults(result);

        const rentsToBeDeleted = [...successfullyNotifiedRents, ...rentsThatExceededMaxAttempts];

        await Promise.all([
            ...preexistentBatchesThatSucceded.map(async b => {
                const id = this.fetchExistingBatchId(b);
                await this.expiredRentsNotificationBatchRepository.delete(id);
            }),
            ...newFailedBatches.map(async b => {
                const id = this.fetchExistingBatchId(b);
                const { rents } = b;
                const rentIds = rents.map(r => r.rentId);
                await this.expiredRentsNotificationBatchRepository.create({ id, rentIds });
            }),
            ...preexistentBatchesThatFailed.map(async b => {
                const { ...rest } = b;
                const id = this.fetchExistingBatchId(b);
                await this.expiredRentsNotificationBatchRepository.update(id, { ...rest });
            }),
            ...preexistentBatchesThatExceededMaxAttempts.map(async b => {
                const id = this.fetchExistingBatchId(b);
                await this.expiredRentsNotificationBatchRepository.delete(id);
            }),
            ...rentsToBeDeleted.map(async rentId => {
                await this.expiredRentsRepository.delete(rentId);
            }),
        ]);

        this.logger.info({
            message: 'Expired rents notification batches were dispatched',
            successfullyNotifiedRents,
        });

        if (preexistentBatchesThatExceededMaxAttempts.length > 0) {
            this.logger.info({
                message: `Expired rents notification batches [${preexistentBatchesThatExceededMaxAttempts.map(b => b.id).join(', ')}] exceeded maximum attempts and were deleted`,
                rents: preexistentBatchesThatExceededMaxAttempts.flatMap(r =>
                    r.rents.map(r => r.rentId),
                ),
            });
        }
    }

    private logNotificationWindowClosed(): void {
        const { start, end } = this.batchExpiredRentsNotifiationWindow;
        this.logger.info({
            message: `Skipping expired rent notifications: Current time in Italy (${DateTime.getLocalTimeAt(
                DateTime.WorldTimeZones.ROME,
            )
                .map(n => padLeft(n))
                .join(
                    ':',
                )}) is not within the allowed window (${padLeft(start.hour)}:${padLeft(start.minute)} to ${padLeft(end.hour)}:${padLeft(end.minute)})`,
        });
    }

    private fetchExistingBatchId({ id }: EnhanceExpiredRentNotificationBatch): string {
        if (id === undefined) throw new Error('Batch id is undefined');
        return id;
    }

    private enhancePreexistentBatchesThatFailed(
        previouslyFailedBatches: ExpiredRentsNotificationBatch[],
        expiredRents: ExpiredRent[],
    ): EnhanceExpiredRentNotificationBatch[] {
        const batches = previouslyFailedBatches.map(({ id, rentIds, failureCount }) => {
            const rents = expiredRents.filter(r => rentIds.includes(r.rentId));
            if (id === undefined) throw new Error('Batch id is undefined');
            return { id, failureCount, rents, isPreexistent: true, success: false };
        });

        return batches;
    }

    private buildExpiredRentNotificationBatchesFromRents(
        rents: ExpiredRent[],
        batchSize: number,
    ): EnhanceExpiredRentNotificationBatch[] {
        const batches: EnhanceExpiredRentNotificationBatch[] = [];

        for (let i = 0; i < rents.length; i += batchSize) {
            batches.push({
                id: randomUUID(),
                failureCount: 0,
                isPreexistent: false,
                success: false,
                rents: rents.slice(i, i + batchSize),
            });
        }

        return batches;
    }

    private async dispatchExpiredRentNotificationBatches(
        batches: EnhanceExpiredRentNotificationBatch[],
    ): Promise<EnhanceExpiredRentNotificationBatch[]> {
        return await Promise.all(
            batches.map(async ({ failureCount, rents, ...rest }) => {
                let success = true;
                let newFailureCount = failureCount;

                /*
                    There might be the case where not all rent ids present in the batch are present
                    in the expired rents list, that's because certain expired rents might have been 
                    picked up prior to the cronjob execution. We're happy to ignore those cases here
                    and mark the job execution status as successful
                */
                if (rents.length === 0) return { ...rest, success, rents, failureCount };

                try {
                    const parcels = rents.map(r => this.mapExpiredRentToProductInfo(r));
                    if (!this.env.DHL_IT_BATCH_EXPIRED_RENT_NOTIFICATIONS_DRY_RUN_ENABLED) {
                        await this.dhlITClient.notifyExpiredParcels({ parcels });
                    }
                } catch (ex) {
                    const { message, stack } = ex as Error;
                    this.logger.error({ message, stack });
                    newFailureCount += 1;
                    success = false;
                }

                return { ...rest, success, rents, failureCount: newFailureCount };
            }),
        );
    }

    private extractBatchNotificationResults(result: EnhanceExpiredRentNotificationBatch[]): {
        preexistentBatchesThatSucceded: EnhanceExpiredRentNotificationBatch[];
        preexistentBatchesThatFailed: EnhanceExpiredRentNotificationBatch[];
        newFailedBatches: EnhanceExpiredRentNotificationBatch[];
        preexistentBatchesThatExceededMaxAttempts: EnhanceExpiredRentNotificationBatch[];
        successfullyNotifiedRents: string[];
        rentsThatExceededMaxAttempts: string[];
    } {
        const newFailedBatches = result.filter(r => !r.success && !r.isPreexistent);
        const preexistentBatchesThatSucceded = result.filter(r => r.success && r.isPreexistent);
        const preexistentBatchesThatFailed = result.filter(
            r =>
                !r.success &&
                r.isPreexistent &&
                r.failureCount < MAX_EXPIRED_RENT_NOTIFICATION_ATTEMPTS,
        );
        const preexistentBatchesThatExceededMaxAttempts = result.filter(
            r =>
                !r.success &&
                r.isPreexistent &&
                r.failureCount >= MAX_EXPIRED_RENT_NOTIFICATION_ATTEMPTS,
        );

        const successfullyNotifiedRents = result
            .filter(r => r.success)
            .flatMap(r => r.rents.map(r => r.rentId));

        const rentsThatExceededMaxAttempts = preexistentBatchesThatExceededMaxAttempts.flatMap(r =>
            r.rents.map(r => r.rentId),
        );

        return {
            preexistentBatchesThatSucceded,
            preexistentBatchesThatFailed,
            newFailedBatches,
            preexistentBatchesThatExceededMaxAttempts,
            successfullyNotifiedRents,
            rentsThatExceededMaxAttempts,
        };
    }

    private mapExpiredRentToProductInfo(rent: ExpiredRent): {
        productId: string;
        nodeId: string;
        productType: string;
    } {
        const { metadata } = rent;
        const { externalID, bloqExternalID, rentMetadata } = metadata;
        return {
            productId: externalID,
            nodeId: bloqExternalID,
            productType: rentMetadata.productType,
        };
    }

    override async handleRentFinishedEvent(event: RentEvent): Promise<void> {
        const { rent: rentId, actionInitiatedBy } = event;
        const { role } = actionInitiatedBy;

        if (role === Roles.COURIER) await this.handleRentFinishedByCourier(event);
        else await this.handleRentFinishedByCustomer(event);

        if (await this.expiredRentsRepository.exists({ rentId })) {
            await this.expiredRentsRepository.delete(rentId);
        }
    }

    private async handleRentFinishedByCourier(event: RentEvent): Promise<void> {
        const { id: courierId, session: sessionId } = event.actionInitiatedBy;
        if (sessionId === undefined) throw new Error('No session id was present in rent finished');

        const sessionExists = await this.courierPickupSessionsRepository.exists({
            courierId,
            sessionId,
        });

        const products: Product[] = this.extractProdutsFromEventMetadata(event);

        const args = { courierId, sessionId, products };
        if (sessionExists) await this.courierPickupSessionsRepository.append(args);
        else await this.courierPickupSessionsRepository.create(args);
    }

    private extractRegularProductFromEventMetadata({ metadata }: RentEvent): Product {
        const { externalID: productId, rentMetadata, pickUpInfo = {} } = metadata;
        const { success, errorReasons } = pickUpInfo;

        const isRentMissing =
            success === false &&
            (Boolean(errorReasons.includes(pickUpErrorReasons.RENT_MISSING)) ||
                Boolean(errorReasons.includes(pickUpErrorReasons.DIFFERENT_CODES)));

        const isRentExpired =
            success === false && Boolean(errorReasons.includes(pickUpErrorReasons.RENT_EXPIRED));

        return {
            productId,
            isExpired: isRentExpired,
            isMissing: isRentMissing,
            product: rentMetadata.productType,
        };
    }

    private extractProdutsFromEventMetadata(event: RentEvent): Product[] {
        const { metadata } = event;
        const { rentMetadata, pickUpInfo = {} } = metadata;

        const { codeRead: unexpectedProductCode, success, errorReasons } = pickUpInfo;
        const products: Product[] = [this.extractRegularProductFromEventMetadata(event)];

        if (
            success === false &&
            Boolean(errorReasons.includes(pickUpErrorReasons.DIFFERENT_CODES))
        ) {
            const { productType } = rentMetadata;
            const unexpectedProduct = { productId: unexpectedProductCode, product: productType };
            products.push(unexpectedProduct);
        }

        return products;
    }
}
