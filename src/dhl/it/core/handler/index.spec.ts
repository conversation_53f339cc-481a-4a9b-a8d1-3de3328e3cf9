/* eslint-disable @typescript-eslint/unbound-method */
import { randomUUID } from 'crypto';
import { DHLITHandlerImpl } from '.';
import { type RentEvent, Roles } from '../../../../bloqit/rents/models/rent';
import { type ApplicationEnvironment } from '../../../../core/env';
import { FakeLogger } from '../../../../core/logger/fake';
import * as DateTime from '../../../../core/utils/date-time';
import {
    DHLITResponseStatus,
    type DHLITCourierShipment,
    type DHLITShipment,
    DHLITAcquireShipmentErrorCode,
} from '../../client';
import { FakeDHLITClient } from '../../client/fake';
import { FakeCourierPickupSessionsRepository } from '../../data-access/repositories/courier-pickup-session/fake';
import { type Product } from '../../models';
import * as pickUpErrorReasons from '../../../../bloqit/rents/models/pickup-error-reasons';
import { FakeExpiredRentsRepository } from '../../data-access/repositories/expired-rents/fake';
import { FakeExpiredRentsNotificationsBatchRepository } from '../../data-access/repositories/expired-rents-notification-batch/fake';

describe('DHLITHandler', () => {
    const bloqitRentId = 'bloqit-rent-33';
    const partnerRentId = 'partner-rent1';
    const rentProductType = 'rent-product-type1';
    const bloqitBloqId = 'bloq-id-22';
    const partnerBloqId = 'partner-bloq-id-22';
    const bloqitLockerId = 'bloqit-locker-id-11';
    const courierId = 'courier-id-123';
    const env = {
        BLOQIT_COURIER_BYPASS_PIN: '123456',
        DHL_IT_BATCH_EXPIRED_RENTS_NOTIFICATION_WINDOW_START: '22:00',
        DHL_IT_BATCH_EXPIRED_RENTS_NOTIFICATION_WINDOW_END: '23:00',
    } as unknown as ApplicationEnvironment;

    const logger = new FakeLogger();
    const dhlITClient = new FakeDHLITClient();
    const courierPickupSessionsRepository = new FakeCourierPickupSessionsRepository();
    const expiredRentsRepository = new FakeExpiredRentsRepository();
    const expiredRentsNotificationBatchRepository =
        new FakeExpiredRentsNotificationsBatchRepository();

    const handler = new DHLITHandlerImpl({
        env,
        logger,
        dhlITClient,
        courierPickupSessionsRepository,
        expiredRentsRepository,
        expiredRentsNotificationBatchRepository,
    });

    beforeEach(() => {
        jest.spyOn(logger, 'error').mockReturnValue();
        jest.spyOn(logger, 'info').mockReturnValue();
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    describe('getRentById', () => {
        afterEach(() => {
            jest.clearAllMocks();
        });

        describe('customer flow', () => {
            const dhlITShipment: DHLITShipment = {
                status: DHLITResponseStatus.SUCCESS,
                errorCode: 0,
                productId: partnerRentId,
                product: 'PKR',
                subProduct: 'subproduct-123',
                senderEmail: '<EMAIL>',
                senderPhone: '+1122334455',
                height: 10,
                width: 10,
                depth: 10,
                weight: 10,
            };

            it('should fetch a rent by its identifier', async () => {
                jest.spyOn(
                    dhlITClient,
                    'fetchShipmentInfoForCustomerDropoff',
                ).mockResolvedValueOnce(dhlITShipment);

                const response = await handler.getRentById({
                    partnerRentId,
                    bloqId: partnerBloqId,
                    locale: 'pt-PT',
                    nodeId: 'node-id-123',
                });

                const rent = response[0];

                expect(dhlITClient.fetchShipmentInfoForCustomerDropoff).toHaveBeenCalledTimes(1);
                expect(dhlITClient.fetchShipmentInfoForCustomerDropoff).toHaveBeenCalledWith({
                    productId: partnerRentId,
                    agentId: partnerBloqId,
                    nodeId: 'node-id-123',
                });

                expect(rent.externalID).toBe(partnerRentId);
                expect(rent.dropOffCode).toBe(partnerRentId);

                expect(rent.customer).toEqual({
                    email: dhlITShipment.senderEmail,
                    phone: dhlITShipment.senderPhone,
                });

                expect(rent.dimensions).toEqual({
                    length: dhlITShipment.depth,
                    width: dhlITShipment.width,
                    height: dhlITShipment.height,
                });
            });

            it('should match the shipment priority J1 to priority 2', async () => {
                const j1Shipment: DHLITShipment = { ...dhlITShipment, class: 'J1' };

                jest.spyOn(
                    dhlITClient,
                    'fetchShipmentInfoForCustomerDropoff',
                ).mockResolvedValueOnce(j1Shipment);

                const response = await handler.getRentById({
                    partnerRentId,
                    bloqId: partnerBloqId,
                    locale: 'pt-PT',
                    nodeId: 'node-id-123',
                });

                expect(dhlITClient.fetchShipmentInfoForCustomerDropoff).toHaveBeenCalledTimes(1);
                expect(dhlITClient.fetchShipmentInfoForCustomerDropoff).toHaveBeenCalledWith({
                    productId: partnerRentId,
                    agentId: partnerBloqId,
                    nodeId: 'node-id-123',
                });

                const rent = response[0];
                expect(rent.priority).toBe(2);
            });

            it('should match the shipment priority J4 to priority 1', async () => {
                const j4Shipment: DHLITShipment = { ...dhlITShipment, class: 'J4' };

                jest.spyOn(
                    dhlITClient,
                    'fetchShipmentInfoForCustomerDropoff',
                ).mockResolvedValueOnce(j4Shipment);

                const response = await handler.getRentById({
                    partnerRentId,
                    bloqId: partnerBloqId,
                    locale: 'pt-PT',
                    nodeId: 'node-id-123',
                });

                expect(dhlITClient.fetchShipmentInfoForCustomerDropoff).toHaveBeenCalledTimes(1);
                expect(dhlITClient.fetchShipmentInfoForCustomerDropoff).toHaveBeenCalledWith({
                    productId: partnerRentId,
                    agentId: partnerBloqId,
                    nodeId: 'node-id-123',
                });

                const rent = response[0];
                expect(rent.priority).toBe(1);
            });

            it('should set the expiry date to 90 days from now, with a random hour between 22h and 22h45', async () => {
                const ninetyDaysFromNow = new Date('2023-04-01');
                jest.spyOn(DateTime, 'daysInTheFuture').mockReturnValueOnce(ninetyDaysFromNow);
                jest.spyOn(
                    dhlITClient,
                    'fetchShipmentInfoForCustomerDropoff',
                ).mockResolvedValueOnce(dhlITShipment);

                const response = await handler.getRentById({
                    partnerRentId,
                    bloqId: partnerBloqId,
                    locale: 'pt-PT',
                    nodeId: 'node-id-123',
                });

                const rent = response[0];

                const expiryDate = rent.expiryDate;
                expect(expiryDate).toBeDefined();
                expect(expiryDate?.getFullYear()).toEqual(ninetyDaysFromNow.getFullYear());
                expect(expiryDate?.getMonth()).toEqual(ninetyDaysFromNow.getMonth());
                expect(expiryDate?.getDate()).toEqual(ninetyDaysFromNow.getDate());
                expect(expiryDate?.getHours()).toEqual(22);
                expect(expiryDate?.getMinutes()).toBeGreaterThanOrEqual(0);
                expect(expiryDate?.getMinutes()).toBeLessThanOrEqual(45);

                expect(DateTime.daysInTheFuture).toHaveBeenCalledWith(90);
            });
        });

        describe('courier flow', () => {
            const dhlITShipment = {
                productId: partnerRentId,
                expiry: new Date(),
                product: 'PKX',
                subProduct: 'HSPA',
                receiverName: 'Receiver',
                receiverEmail: '<EMAIL>',
                errorCode: 0,
                height: 10,
                width: 10,
                depth: 10,
                weight: 10,
            };

            const dhlITCourierShipmentResponse: DHLITCourierShipment = {
                status: DHLITResponseStatus.SUCCESS,
                errorCode: DHLITAcquireShipmentErrorCode.SUCCESS,
                digests: [{ product: 'PKX', quantity: 10, class: 'J1' }],
                containers: [{ products: [dhlITShipment] }],
            };

            it('should fetch a rent by its identifier', async () => {
                jest.spyOn(dhlITClient, 'fetchShipmentInfoForCourierDropoff').mockResolvedValueOnce(
                    dhlITCourierShipmentResponse,
                );

                const response = await handler.getRentById({
                    partnerRentId,
                    bloqId: partnerBloqId,
                    courier: courierId,
                    locale: 'pt-PT',
                    nodeId: 'node-id-123',
                });

                const rent = response[0];

                expect(dhlITClient.fetchShipmentInfoForCourierDropoff).toHaveBeenCalledTimes(1);
                expect(dhlITClient.fetchShipmentInfoForCourierDropoff).toHaveBeenCalledWith({
                    shipmentId: partnerRentId,
                    agentId: partnerBloqId,
                    nodeId: 'node-id-123',
                });

                expect(rent.externalID).toBe(partnerRentId);
                expect(rent.dropOffCode).toBe(partnerRentId);
                expect(rent.expiryDate).toBeInstanceOf(Date);
                expect(rent.customer?.email).toBe(dhlITShipment.receiverEmail);

                expect(rent.dimensions).toEqual({
                    length: dhlITShipment.depth,
                    width: dhlITShipment.width,
                    height: dhlITShipment.height,
                });
            });

            it('should match the shipment priority J1 to priority 2', async () => {
                const j1Shipment: DHLITCourierShipment = {
                    ...dhlITCourierShipmentResponse,
                    digests: [{ product: dhlITShipment.product, quantity: 10, class: 'J1' }],
                };

                jest.spyOn(dhlITClient, 'fetchShipmentInfoForCourierDropoff').mockResolvedValueOnce(
                    j1Shipment,
                );

                const response = await handler.getRentById({
                    partnerRentId,
                    bloqId: partnerBloqId,
                    courier: courierId,
                    locale: 'pt-PT',
                    nodeId: 'node-id-123',
                });

                expect(dhlITClient.fetchShipmentInfoForCourierDropoff).toHaveBeenCalledTimes(1);
                expect(dhlITClient.fetchShipmentInfoForCourierDropoff).toHaveBeenCalledWith({
                    shipmentId: partnerRentId,
                    agentId: partnerBloqId,
                    nodeId: 'node-id-123',
                });

                const rent = response[0];
                expect(rent.priority).toBe(2);
            });

            it('should match the shipment priority J4 to priority 1', async () => {
                const j4Shipment: DHLITCourierShipment = {
                    ...dhlITCourierShipmentResponse,
                    digests: [{ product: dhlITShipment.product, quantity: 10, class: 'J4' }],
                };

                jest.spyOn(dhlITClient, 'fetchShipmentInfoForCourierDropoff').mockResolvedValueOnce(
                    j4Shipment,
                );

                const response = await handler.getRentById({
                    partnerRentId,
                    bloqId: partnerBloqId,
                    courier: courierId,
                    locale: 'pt-PT',
                    nodeId: 'node-id-123',
                });

                expect(dhlITClient.fetchShipmentInfoForCourierDropoff).toHaveBeenCalledTimes(1);
                expect(dhlITClient.fetchShipmentInfoForCourierDropoff).toHaveBeenCalledWith({
                    shipmentId: partnerRentId,
                    agentId: partnerBloqId,
                    nodeId: 'node-id-123',
                });

                const rent = response[0];
                expect(rent.priority).toBe(1);
            });

            it('should default to J4 priority (1) when digests is missing', async () => {
                const shipmentWithoutDigests: DHLITCourierShipment = {
                    ...dhlITCourierShipmentResponse,
                    digests: undefined,
                };

                jest.spyOn(dhlITClient, 'fetchShipmentInfoForCourierDropoff').mockResolvedValueOnce(
                    shipmentWithoutDigests,
                );

                const response = await handler.getRentById({
                    partnerRentId,
                    bloqId: partnerBloqId,
                    courier: courierId,
                    locale: 'pt-PT',
                    nodeId: 'node-id-123',
                });

                expect(dhlITClient.fetchShipmentInfoForCourierDropoff).toHaveBeenCalledTimes(1);
                expect(dhlITClient.fetchShipmentInfoForCourierDropoff).toHaveBeenCalledWith({
                    shipmentId: partnerRentId,
                    agentId: partnerBloqId,
                    nodeId: 'node-id-123',
                });

                const rent = response[0];
                expect(rent.priority).toBe(1);
            });

            it('should default to J4 priority (1) when digests is empty array', async () => {
                const shipmentWithEmptyDigests: DHLITCourierShipment = {
                    ...dhlITCourierShipmentResponse,
                    digests: [],
                };

                jest.spyOn(dhlITClient, 'fetchShipmentInfoForCourierDropoff').mockResolvedValueOnce(
                    shipmentWithEmptyDigests,
                );

                const response = await handler.getRentById({
                    partnerRentId,
                    bloqId: partnerBloqId,
                    courier: courierId,
                    locale: 'pt-PT',
                    nodeId: 'node-id-123',
                });

                expect(dhlITClient.fetchShipmentInfoForCourierDropoff).toHaveBeenCalledTimes(1);
                expect(dhlITClient.fetchShipmentInfoForCourierDropoff).toHaveBeenCalledWith({
                    shipmentId: partnerRentId,
                    agentId: partnerBloqId,
                    nodeId: 'node-id-123',
                });

                const rent = response[0];
                expect(rent.priority).toBe(1);
            });

            it('should set the expiry date to 10 days from now in hours between 22h and 22h45 if shipment has no expiry info', async () => {
                const dhlITCourierShipmentResponse: DHLITCourierShipment = {
                    status: DHLITResponseStatus.SUCCESS,
                    errorCode: DHLITAcquireShipmentErrorCode.SUCCESS,
                    digests: [{ product: 'PKX', quantity: 10, class: 'J1' }],
                    containers: [{ products: [{ ...dhlITShipment, expiry: undefined }] }],
                };

                const tenDaysFromNow = new Date('2023-04-01');
                jest.spyOn(DateTime, 'daysInTheFuture').mockReturnValueOnce(tenDaysFromNow);

                jest.spyOn(dhlITClient, 'fetchShipmentInfoForCourierDropoff').mockResolvedValueOnce(
                    dhlITCourierShipmentResponse,
                );

                const response = await handler.getRentById({
                    partnerRentId,
                    bloqId: partnerBloqId,
                    courier: courierId,
                    locale: 'pt-PT',
                    nodeId: 'node-id-123',
                });

                expect(DateTime.daysInTheFuture).toHaveBeenCalledTimes(1);
                expect(DateTime.daysInTheFuture).toHaveBeenCalledWith(10);

                const rent = response[0];
                const expiryDate = rent.expiryDate;
                expect(expiryDate).toBeDefined();
                expect(expiryDate?.getFullYear()).toEqual(tenDaysFromNow.getFullYear());
                expect(expiryDate?.getMonth()).toEqual(tenDaysFromNow.getMonth());
                expect(expiryDate?.getDate()).toEqual(tenDaysFromNow.getDate());
                expect(expiryDate?.getHours()).toEqual(22);
                expect(expiryDate?.getMinutes()).toBeGreaterThanOrEqual(0);
                expect(expiryDate?.getMinutes()).toBeLessThanOrEqual(45);
            });

            it('should assign a random hour between 22h and 22h45 to the shipment expiry date', async () => {
                const shipmentExpiryDate = new Date('2023-04-01');
                const dhlITCourierShipmentResponse: DHLITCourierShipment = {
                    status: DHLITResponseStatus.SUCCESS,
                    errorCode: DHLITAcquireShipmentErrorCode.SUCCESS,
                    digests: [{ product: 'PKX', quantity: 10, class: 'J1' }],
                    containers: [{ products: [{ ...dhlITShipment, expiry: shipmentExpiryDate }] }],
                };

                jest.spyOn(dhlITClient, 'fetchShipmentInfoForCourierDropoff').mockResolvedValueOnce(
                    dhlITCourierShipmentResponse,
                );

                const response = await handler.getRentById({
                    partnerRentId,
                    bloqId: partnerBloqId,
                    courier: courierId,
                    locale: 'pt-PT',
                    nodeId: 'node-id-123',
                });

                const rent = response[0];
                const expiryDate = rent.expiryDate;
                expect(expiryDate).toBeDefined();
                expect(expiryDate?.getFullYear()).toEqual(shipmentExpiryDate.getFullYear());
                expect(expiryDate?.getMonth()).toEqual(shipmentExpiryDate.getMonth());
                expect(expiryDate?.getDate()).toEqual(shipmentExpiryDate.getDate());
                expect(expiryDate?.getHours()).toEqual(22);
                expect(expiryDate?.getMinutes()).toBeGreaterThanOrEqual(0);
                expect(expiryDate?.getMinutes()).toBeLessThanOrEqual(45);
            });

            it('should create a customer object in the rent if the receiverName is present', async () => {
                const shipmentWithReceiverName = {
                    ...dhlITShipment,
                    receiverEmail: undefined,
                    receiverName: 'Receiver',
                };

                const dhlITCourierShipmentResponse: DHLITCourierShipment = {
                    status: DHLITResponseStatus.SUCCESS,
                    errorCode: DHLITAcquireShipmentErrorCode.SUCCESS,
                    digests: [{ product: 'PKX', quantity: 10, class: 'J1' }],
                    containers: [{ products: [shipmentWithReceiverName] }],
                };

                jest.spyOn(dhlITClient, 'fetchShipmentInfoForCourierDropoff').mockResolvedValueOnce(
                    dhlITCourierShipmentResponse,
                );

                const response = await handler.getRentById({
                    partnerRentId,
                    bloqId: partnerBloqId,
                    courier: courierId,
                    locale: 'pt-PT',
                    nodeId: 'node-id-123',
                });

                const rent = response[0];
                expect(rent.customer?.name).toBe('Receiver');
            });

            it('should create a customer object in the rent if the receiverEmail is present', async () => {
                const shipmentWithReceiverName = {
                    ...dhlITShipment,
                    receiverEmail: '<EMAIL>',
                    receiverName: undefined,
                };

                const dhlITCourierShipmentResponse: DHLITCourierShipment = {
                    status: DHLITResponseStatus.SUCCESS,
                    errorCode: DHLITAcquireShipmentErrorCode.SUCCESS,
                    digests: [{ product: 'PKX', quantity: 10, class: 'J1' }],
                    containers: [{ products: [shipmentWithReceiverName] }],
                };

                jest.spyOn(dhlITClient, 'fetchShipmentInfoForCourierDropoff').mockResolvedValueOnce(
                    dhlITCourierShipmentResponse,
                );

                const response = await handler.getRentById({
                    partnerRentId,
                    bloqId: partnerBloqId,
                    courier: courierId,
                    locale: 'pt-PT',
                    nodeId: 'node-id-123',
                });

                const rent = response[0];
                expect(rent.customer?.email).toBe('<EMAIL>');
            });

            it('should not create a customer object in the rent if neither receiverEmail nor receiverName are present', async () => {
                const shipmentWithReceiverName = {
                    ...dhlITShipment,
                    receiverEmail: undefined,
                    receiverName: undefined,
                };

                const dhlITCourierShipmentResponse: DHLITCourierShipment = {
                    status: DHLITResponseStatus.SUCCESS,
                    errorCode: DHLITAcquireShipmentErrorCode.SUCCESS,
                    digests: [{ product: 'PKX', quantity: 10, class: 'J1' }],
                    containers: [{ products: [shipmentWithReceiverName] }],
                };

                jest.spyOn(dhlITClient, 'fetchShipmentInfoForCourierDropoff').mockResolvedValueOnce(
                    dhlITCourierShipmentResponse,
                );

                const response = await handler.getRentById({
                    partnerRentId,
                    bloqId: partnerBloqId,
                    courier: courierId,
                    locale: 'pt-PT',
                    nodeId: 'node-id-123',
                });

                const rent = response[0];
                expect(rent.customer).toBeUndefined();
            });
        });

        describe('parse code', () => {
            const dhlITShipment: DHLITShipment = {
                status: DHLITResponseStatus.SUCCESS,
                errorCode: 0,
                productId: 'productId',
                product: 'PKR',
                subProduct: 'subproduct-123',
                senderEmail: '<EMAIL>',
                senderPhone: '+1122334455',
                height: 10,
                width: 10,
                depth: 10,
                weight: 10,
            };

            it('should parse 13 char code as it is', async () => {
                jest.spyOn(
                    dhlITClient,
                    'fetchShipmentInfoForCustomerDropoff',
                ).mockResolvedValueOnce(dhlITShipment);

                const partnerRentId = '13-char-code1';
                await handler.getRentById({
                    partnerRentId,
                    bloqId: partnerBloqId,
                    locale: 'pt-PT',
                    nodeId: 'node-id-123',
                });

                expect(dhlITClient.fetchShipmentInfoForCustomerDropoff).toHaveBeenCalledTimes(1);
                expect(dhlITClient.fetchShipmentInfoForCustomerDropoff).toHaveBeenCalledWith({
                    productId: partnerRentId,
                    agentId: partnerBloqId,
                    nodeId: 'node-id-123',
                });
            });

            it('should parse 16 char code into a 13 char code', async () => {
                jest.spyOn(
                    dhlITClient,
                    'fetchShipmentInfoForCustomerDropoff',
                ).mockResolvedValueOnce(dhlITShipment);

                const partnerRentId = '16-char-code1234';
                await handler.getRentById({
                    partnerRentId,
                    bloqId: partnerBloqId,
                    locale: 'pt-PT',
                    nodeId: 'node-id-123',
                });

                expect(dhlITClient.fetchShipmentInfoForCustomerDropoff).toHaveBeenCalledTimes(1);
                expect(dhlITClient.fetchShipmentInfoForCustomerDropoff).toHaveBeenCalledWith({
                    productId: partnerRentId.substring(3),
                    agentId: partnerBloqId,
                    nodeId: 'node-id-123',
                });
            });

            it('should parse 16 char code into a 13 char code', async () => {
                jest.spyOn(
                    dhlITClient,
                    'fetchShipmentInfoForCustomerDropoff',
                ).mockResolvedValueOnce(dhlITShipment);

                const partnerRentId = '16-char-code1234';
                await handler.getRentById({
                    partnerRentId,
                    bloqId: partnerBloqId,
                    locale: 'pt-PT',
                    nodeId: 'node-id-123',
                });

                expect(dhlITClient.fetchShipmentInfoForCustomerDropoff).toHaveBeenCalledTimes(1);
                expect(dhlITClient.fetchShipmentInfoForCustomerDropoff).toHaveBeenCalledWith({
                    productId: partnerRentId.substring(3),
                    agentId: partnerBloqId,
                    nodeId: 'node-id-123',
                });
            });

            it('should parse a code that satisfy the first part of the regex', async () => {
                jest.spyOn(
                    dhlITClient,
                    'fetchShipmentInfoForCustomerDropoff',
                ).mockResolvedValueOnce(dhlITShipment);

                const partnerRentId = 'JVGLOTC1234567890';
                await handler.getRentById({
                    partnerRentId,
                    bloqId: partnerBloqId,
                    locale: 'pt-PT',
                    nodeId: 'node-id-123',
                });

                expect(dhlITClient.fetchShipmentInfoForCustomerDropoff).toHaveBeenCalledTimes(1);
                expect(dhlITClient.fetchShipmentInfoForCustomerDropoff).toHaveBeenCalledWith({
                    productId: partnerRentId,
                    agentId: partnerBloqId,
                    nodeId: 'node-id-123',
                });
            });

            it('should parse a code that satisfy the second part of the regex', async () => {
                jest.spyOn(
                    dhlITClient,
                    'fetchShipmentInfoForCustomerDropoff',
                ).mockResolvedValueOnce(dhlITShipment);

                const partnerRentId = 'JJD019876543210';
                await handler.getRentById({
                    partnerRentId,
                    bloqId: partnerBloqId,
                    locale: 'pt-PT',
                    nodeId: 'node-id-123',
                });

                expect(dhlITClient.fetchShipmentInfoForCustomerDropoff).toHaveBeenCalledTimes(1);
                expect(dhlITClient.fetchShipmentInfoForCustomerDropoff).toHaveBeenCalledWith({
                    productId: partnerRentId,
                    agentId: partnerBloqId,
                    nodeId: 'node-id-123',
                });
            });

            it('should parse a code that satisfy the third part of the regex', async () => {
                jest.spyOn(
                    dhlITClient,
                    'fetchShipmentInfoForCustomerDropoff',
                ).mockResolvedValueOnce(dhlITShipment);

                const partnerRentId = 'JJH30123456789012';
                await handler.getRentById({
                    partnerRentId,
                    bloqId: partnerBloqId,
                    locale: 'pt-PT',
                    nodeId: 'node-id-123',
                });

                expect(dhlITClient.fetchShipmentInfoForCustomerDropoff).toHaveBeenCalledTimes(1);
                expect(dhlITClient.fetchShipmentInfoForCustomerDropoff).toHaveBeenCalledWith({
                    productId: partnerRentId,
                    agentId: partnerBloqId,
                    nodeId: 'node-id-123',
                });
            });

            it('should parse a code that satisfy the fourth part of the regex', async () => {
                jest.spyOn(
                    dhlITClient,
                    'fetchShipmentInfoForCustomerDropoff',
                ).mockResolvedValueOnce(dhlITShipment);

                const partnerRentId = 'JJATA123456789012';
                await handler.getRentById({
                    partnerRentId,
                    bloqId: partnerBloqId,
                    locale: 'pt-PT',
                    nodeId: 'node-id-123',
                });

                expect(dhlITClient.fetchShipmentInfoForCustomerDropoff).toHaveBeenCalledTimes(1);
                expect(dhlITClient.fetchShipmentInfoForCustomerDropoff).toHaveBeenCalledWith({
                    productId: partnerRentId,
                    agentId: partnerBloqId,
                    nodeId: 'node-id-123',
                });
            });

            it('should parse a code that satisfy the fifth part of the regex', async () => {
                jest.spyOn(
                    dhlITClient,
                    'fetchShipmentInfoForCustomerDropoff',
                ).mockResolvedValueOnce(dhlITShipment);

                const partnerRentId = 'JJFI0000123456789';
                await handler.getRentById({
                    partnerRentId,
                    bloqId: partnerBloqId,
                    locale: 'pt-PT',
                    nodeId: 'node-id-123',
                });

                expect(dhlITClient.fetchShipmentInfoForCustomerDropoff).toHaveBeenCalledTimes(1);
                expect(dhlITClient.fetchShipmentInfoForCustomerDropoff).toHaveBeenCalledWith({
                    productId: partnerRentId,
                    agentId: partnerBloqId,
                    nodeId: 'node-id-123',
                });
            });

            it('should parse a code that satisfy the sixth part of the regex', async () => {
                jest.spyOn(
                    dhlITClient,
                    'fetchShipmentInfoForCustomerDropoff',
                ).mockResolvedValueOnce(dhlITShipment);

                const partnerRentId = '00123456789012345678';
                await handler.getRentById({
                    partnerRentId,
                    bloqId: partnerBloqId,
                    locale: 'pt-PT',
                    nodeId: 'node-id-123',
                });

                expect(dhlITClient.fetchShipmentInfoForCustomerDropoff).toHaveBeenCalledTimes(1);
                expect(dhlITClient.fetchShipmentInfoForCustomerDropoff).toHaveBeenCalledWith({
                    productId: partnerRentId,
                    agentId: partnerBloqId,
                    nodeId: 'node-id-123',
                });
            });

            it('should parse a code that satisfy the seventh part of the regex', async () => {
                jest.spyOn(
                    dhlITClient,
                    'fetchShipmentInfoForCustomerDropoff',
                ).mockResolvedValueOnce(dhlITShipment);

                const partnerRentId = 'JJITAXP18UZ123A098765EA';
                await handler.getRentById({
                    partnerRentId,
                    bloqId: partnerBloqId,
                    locale: 'pt-PT',
                    nodeId: 'node-id-123',
                });

                expect(dhlITClient.fetchShipmentInfoForCustomerDropoff).toHaveBeenCalledTimes(1);
                expect(dhlITClient.fetchShipmentInfoForCustomerDropoff).toHaveBeenCalledWith({
                    productId: partnerRentId,
                    agentId: partnerBloqId,
                    nodeId: 'node-id-123',
                });
            });

            it('should throw an error if code is not parsable', async () => {
                jest.spyOn(
                    dhlITClient,
                    'fetchShipmentInfoForCustomerDropoff',
                ).mockResolvedValueOnce(dhlITShipment);

                const partnerRentId = 'InvalidString42';

                await expect(
                    handler.getRentById({
                        partnerRentId,
                        bloqId: partnerBloqId,
                        locale: 'pt-PT',
                        nodeId: 'node-id-123',
                    }),
                ).rejects.toThrow(`Shipment code not acceptable: ${partnerRentId}`);
            });
        });
    });

    describe('notifyDropoffConfirmation', () => {
        const baseEventData = {
            rent: bloqitRentId,
            bloq: bloqitBloqId,
            locker: bloqitLockerId,
            customer: { email: '<EMAIL>' }, // We don't have a customer for this event at the core
            message: 'Successful drop off for rent bloqit-rent-1234',
            timestamp: new Date(),
            metadata: {
                externalID: partnerRentId,
                bloqExternalID: partnerBloqId,
                rentMetadata: { productType: rentProductType },
            },
        };

        it('should notify DHL IT about a drop off confirmation performed by a customer', async () => {
            jest.spyOn(dhlITClient, 'sendCustomerDropOffStatusUpdate').mockResolvedValueOnce();

            await handler.notifyDropOffConfirmation({
                ...baseEventData,
                actionInitiatedBy: { role: Roles.CUSTOMER, id: 'customer-id-123' },
            });

            expect(dhlITClient.sendCustomerDropOffStatusUpdate).toHaveBeenCalledTimes(1);
            expect(dhlITClient.sendCustomerDropOffStatusUpdate).toHaveBeenCalledWith({
                product: {
                    productId: baseEventData.metadata.externalID,
                    product: baseEventData.metadata.rentMetadata.productType,
                },
                agentId: bloqitBloqId,
                fromNode: partnerBloqId,
                customerEmail: baseEventData.customer.email,
                time: baseEventData.timestamp,
            });
        });

        it('should notify DHL IT about a drop off confirmation performed by a courier', async () => {
            jest.spyOn(dhlITClient, 'notifyCourierShipmentDropOff').mockResolvedValueOnce();

            const event = {
                ...baseEventData,
                actionInitiatedBy: { role: Roles.COURIER, id: 'customer-id-123' },
                metadata: { ...baseEventData.metadata, pickUpCode: '1234' },
            };

            await handler.notifyDropOffConfirmation(event);

            expect(dhlITClient.notifyCourierShipmentDropOff).toHaveBeenCalledTimes(1);
            expect(dhlITClient.notifyCourierShipmentDropOff).toHaveBeenCalledWith({
                agentId: event.bloq,
                time: event.timestamp,
                nodeId: event.metadata.bloqExternalID,
                product: {
                    productId: event.metadata.externalID,
                    product: event.metadata.rentMetadata.productType,
                },
                pin: event.metadata.pickUpCode,
            });
        });

        it('should throw an error if role is not recognized', async () => {
            jest.spyOn(dhlITClient, 'notifyCourierShipmentDropOff').mockResolvedValueOnce(
                undefined,
            );

            const event = {
                ...baseEventData,
                actionInitiatedBy: { role: 'hacker' as unknown as Roles, id: 'customer-id-123' },
            };

            await expect(handler.notifyDropOffConfirmation(event)).rejects.toThrow(
                'Invalid role provided for DROP_OFF_CONFIRMATION event',
            );
        });
    });

    describe('authenticateCourier', () => {
        const pin = '1234';
        const bloqExternalId = 'partner-bloq-id-1234';
        const courierSessionId = 'courier-session-id-1234';

        it('should throw an error if bloqExternalId is not provided', async () => {
            const args = { pin, courierSessionId, bloqExternalId: undefined };
            await expect(handler.authenticateCourier(args)).rejects.toThrow(
                'bloqExternalId is required',
            );
        });

        it('should throw an error if courierSessionId is not provided', async () => {
            const args = { pin, bloqExternalId, courierSessionId: undefined };
            await expect(handler.authenticateCourier(args)).rejects.toThrow(
                'courierSessionId is required',
            );
        });

        it("should bypass courier authentication if the PIN provided is Bloqit's bypass PIN", async () => {
            const args = { pin: env.BLOQIT_COURIER_BYPASS_PIN, bloqExternalId, courierSessionId };
            const result = await handler.authenticateCourier(args);
            expect(result.success).toBe(true);
        });

        it('should authenticate a courier', async () => {
            jest.spyOn(dhlITClient, 'authenticateCourier').mockResolvedValueOnce('user_id_hash_1');

            const args = { pin, bloqExternalId, courierSessionId };
            const result = await handler.authenticateCourier(args);

            expect(result.success).toBe(true);
        });

        it('should return a failure result if client throws an error', async () => {
            jest.spyOn(dhlITClient, 'authenticateCourier').mockRejectedValueOnce(
                'Failed to authenticate courier',
            );

            const args = { pin, bloqExternalId, courierSessionId };
            const result = await handler.authenticateCourier(args);

            expect(result.success).toBe(false);
        });

        it('should handle unexpected errors from client', async () => {
            jest.spyOn(dhlITClient, 'authenticateCourier').mockRejectedValueOnce(
                new Error('Network error'),
            );

            const args = { pin, bloqExternalId, courierSessionId };
            const result = await handler.authenticateCourier(args);

            expect(result.success).toBe(false);
            expect(logger.error).toHaveBeenCalledTimes(1);
            expect(logger.error).toHaveBeenCalledWith({
                message: 'Network error',
                stack: expect.any(String),
            });
        });
    });

    describe('handleCourierLogout', () => {
        const sessionId = randomUUID();
        const courierId = randomUUID();
        const product1 = {
            productId: randomUUID(),
            product: randomUUID(),
        };
        const product2 = {
            productId: randomUUID(),
            product: randomUUID(),
        };
        const product3 = {
            productId: randomUUID(),
            product: randomUUID(),
        };
        const products = [product1, product2, product3];
        const courierLogoutEvent = {
            code: 120012,
            bloq: bloqitBloqId,
            timestamp: new Date(),
            codeName: 'courier_logout',
            title: 'Courier logout from Bloq',
            description: 'Courier has logout from the bloq',
            metadata: { bloqExternalID: partnerBloqId },
            actionInitiatedBy: { id: courierId, session: sessionId, role: Roles.COURIER },
        };

        it('should throw an error if sessionId is not present in the event object', async () => {
            const eventWithoutSessionId = {
                ...courierLogoutEvent,
                actionInitiatedBy: { ...courierLogoutEvent.actionInitiatedBy, session: undefined },
            };

            await expect(handler.handleCourierLogout(eventWithoutSessionId)).rejects.toThrow(
                'No session id was present in courier logout',
            );
        });

        it('should early return if there are no products registered in the session', async () => {
            jest.spyOn(dhlITClient, 'createProductsOnPartnerSide').mockResolvedValueOnce();
            jest.spyOn(courierPickupSessionsRepository, 'exists').mockResolvedValueOnce(true);
            jest.spyOn(courierPickupSessionsRepository, 'fetchProducts').mockResolvedValueOnce([]);

            await handler.handleCourierLogout(courierLogoutEvent);

            expect(dhlITClient.createProductsOnPartnerSide).not.toHaveBeenCalled();
        });

        it('should create the shipment on DHL IT side with all data stored from a pickup session', async () => {
            jest.spyOn(dhlITClient, 'createProductsOnPartnerSide').mockResolvedValueOnce();
            jest.spyOn(courierPickupSessionsRepository, 'exists').mockResolvedValueOnce(true);
            jest.spyOn(courierPickupSessionsRepository, 'delete').mockResolvedValueOnce();
            jest.spyOn(courierPickupSessionsRepository, 'fetchProducts').mockResolvedValueOnce(
                products,
            );

            await handler.handleCourierLogout(courierLogoutEvent);

            expect(dhlITClient.createProductsOnPartnerSide).toHaveBeenCalledTimes(1);
            expect(dhlITClient.createProductsOnPartnerSide).toHaveBeenCalledWith({
                agentId: bloqitBloqId,
                products,
                nodeId: courierLogoutEvent.metadata.bloqExternalID,
                time: courierLogoutEvent.timestamp,
            });
        });

        it('should delete the pickup session after confirmation', async () => {
            jest.spyOn(dhlITClient, 'createProductsOnPartnerSide').mockResolvedValueOnce();
            jest.spyOn(courierPickupSessionsRepository, 'exists').mockResolvedValueOnce(true);
            jest.spyOn(courierPickupSessionsRepository, 'delete').mockResolvedValueOnce();
            jest.spyOn(courierPickupSessionsRepository, 'fetchProducts').mockResolvedValueOnce(
                products,
            );

            await handler.handleCourierLogout(courierLogoutEvent);

            expect(courierPickupSessionsRepository.delete).toHaveBeenCalledTimes(1);
            expect(courierPickupSessionsRepository.delete).toHaveBeenCalledWith({
                courierId,
                sessionId,
            });
        });
    });

    describe('notifyExpiredRent', () => {
        it('should store expired rents in the database', async () => {
            const rentMetadata = { productType: rentProductType };
            const metadata = {
                externalID: partnerRentId,
                bloqExternalID: partnerBloqId,
                rentMetadata,
            };

            const expiredRentEvent = {
                rent: bloqitRentId,
                bloq: bloqitBloqId,
                locker: bloqitLockerId,
                message: 'Rent expired',
                timestamp: new Date(),
                customer: { email: '<EMAIL>' },
                actionInitiatedBy: { role: Roles.CUSTOMER, id: 'customer-id-123' },
                metadata,
            };

            jest.spyOn(expiredRentsRepository, 'create').mockResolvedValueOnce();

            await handler.notifyExpiredRent(expiredRentEvent);

            expect(expiredRentsRepository.create).toHaveBeenCalledTimes(1);
            expect(expiredRentsRepository.create).toHaveBeenCalledWith({
                rentId: bloqitRentId,
                partnerFriendlyId: 'dhl_it',
                metadata,
            });
        });
    });

    describe('notifyExpiredRents', () => {
        const partnerFriendlyId = 'dhl_it';

        const rentId1 = randomUUID();
        const rentExternalId1 = randomUUID();

        const rentId2 = randomUUID();
        const rentExternalId2 = randomUUID();

        const bloqExternalId1 = randomUUID();
        const bloqExternalId2 = randomUUID();

        const productType = 'product-type-1';

        const expiredRent1 = {
            partnerFriendlyId,
            rentId: rentId1,
            metadata: {
                externalID: rentExternalId1,
                bloqExternalID: bloqExternalId1,
                rentMetadata: { productType },
            },
        };

        const expiredRent2 = {
            partnerFriendlyId,
            rentId: rentId2,
            metadata: {
                externalID: rentExternalId2,
                bloqExternalID: bloqExternalId2,
                rentMetadata: { productType },
            },
        };

        let getLocalTimeSpy: jest.SpyInstance;
        beforeEach(() => {
            getLocalTimeSpy = jest.spyOn(DateTime, 'getLocalTimeAt').mockReturnValue([22, 30]);
        });

        afterEach(() => {
            jest.clearAllMocks();
            getLocalTimeSpy.mockClear();
        });

        it('should do nothing if there are no expired rents and no expired batches', async () => {
            jest.spyOn(expiredRentsNotificationBatchRepository, 'list').mockResolvedValueOnce([]);
            jest.spyOn(expiredRentsRepository, 'list').mockResolvedValueOnce([]);
            const spyOnNotify = jest.spyOn(dhlITClient, 'notifyExpiredParcels');

            await handler.notifyExpiredRents({ batchSize: 1 });

            expect(spyOnNotify).not.toHaveBeenCalled();
        });

        it('should do nothing if local time in Italy is not in a pre-defined window', async () => {
            jest.spyOn(DateTime, 'getLocalTimeAt').mockReturnValueOnce([16, 30]);
            const spyOnNotify = jest.spyOn(dhlITClient, 'notifyExpiredParcels');

            const handler = new DHLITHandlerImpl({
                logger,
                dhlITClient,
                courierPickupSessionsRepository,
                expiredRentsRepository,
                expiredRentsNotificationBatchRepository,
                env: {
                    ...env,
                    DHL_IT_BATCH_EXPIRED_RENTS_NOTIFICATION_WINDOW_START: '17:00',
                    DHL_IT_BATCH_EXPIRED_RENTS_NOTIFICATION_WINDOW_END: '18:00',
                },
            });

            await handler.notifyExpiredRents({ batchSize: 1 });

            expect(spyOnNotify).not.toHaveBeenCalled();
        });

        it('should notify DHL IT about an expired rents if time is within pre-defined notification window', async () => {
            jest.spyOn(expiredRentsNotificationBatchRepository, 'list').mockResolvedValueOnce([]);

            jest.spyOn(expiredRentsRepository, 'delete')
                .mockResolvedValueOnce()
                .mockResolvedValueOnce();

            jest.spyOn(expiredRentsRepository, 'list').mockResolvedValueOnce([
                expiredRent1,
                expiredRent2,
            ]);

            jest.spyOn(dhlITClient, 'notifyExpiredParcels')
                .mockResolvedValueOnce()
                .mockResolvedValueOnce();

            getLocalTimeSpy.mockClear();
            getLocalTimeSpy = jest.spyOn(DateTime, 'getLocalTimeAt').mockReturnValue([17, 30]);

            const handler = new DHLITHandlerImpl({
                logger,
                dhlITClient,
                courierPickupSessionsRepository,
                expiredRentsRepository,
                expiredRentsNotificationBatchRepository,
                env: {
                    ...env,
                    DHL_IT_BATCH_EXPIRED_RENTS_NOTIFICATION_WINDOW_START: '17:00',
                    DHL_IT_BATCH_EXPIRED_RENTS_NOTIFICATION_WINDOW_END: '18:00',
                },
            });

            const formattedExpiredRent1 = {
                rentExternalId: rentExternalId1,
                bloqExternalId: bloqExternalId1,
                productType,
            };

            const formattedExpiredRent2 = {
                rentExternalId: rentExternalId2,
                bloqExternalId: bloqExternalId2,
                productType,
            };

            await handler.notifyExpiredRents({ batchSize: 1 });

            expect(dhlITClient.notifyExpiredParcels).toHaveBeenCalledTimes(2);

            expect(dhlITClient.notifyExpiredParcels).toHaveBeenCalledWith({
                parcels: [
                    {
                        productId: formattedExpiredRent1.rentExternalId,
                        nodeId: formattedExpiredRent1.bloqExternalId,
                        productType: formattedExpiredRent1.productType,
                    },
                ],
            });

            expect(dhlITClient.notifyExpiredParcels).toHaveBeenCalledWith({
                parcels: [
                    {
                        productId: formattedExpiredRent2.rentExternalId,
                        nodeId: formattedExpiredRent2.bloqExternalId,
                        productType: formattedExpiredRent2.productType,
                    },
                ],
            });
        });

        it('should not call DHL IT if dry run is enabled', async () => {
            jest.spyOn(expiredRentsNotificationBatchRepository, 'list').mockResolvedValueOnce([]);

            jest.spyOn(expiredRentsRepository, 'delete')
                .mockResolvedValueOnce()
                .mockResolvedValueOnce();

            jest.spyOn(expiredRentsRepository, 'list').mockResolvedValueOnce([
                expiredRent1,
                expiredRent2,
            ]);

            const handler = new DHLITHandlerImpl({
                logger,
                dhlITClient,
                courierPickupSessionsRepository,
                expiredRentsRepository,
                expiredRentsNotificationBatchRepository,
                env: { ...env, DHL_IT_BATCH_EXPIRED_RENT_NOTIFICATIONS_DRY_RUN_ENABLED: true },
            });

            await handler.notifyExpiredRents({ batchSize: 1 });

            expect(dhlITClient.notifyExpiredParcels).not.toHaveBeenCalled();
        });

        it('should mark batch as failed and save it to db for future retries', async () => {
            jest.spyOn(expiredRentsNotificationBatchRepository, 'list').mockResolvedValueOnce([]);

            jest.spyOn(expiredRentsRepository, 'delete').mockResolvedValueOnce();
            jest.spyOn(expiredRentsRepository, 'list').mockResolvedValueOnce([
                expiredRent1,
                expiredRent2,
            ]);

            jest.spyOn(dhlITClient, 'notifyExpiredParcels')
                .mockResolvedValueOnce()
                .mockRejectedValueOnce('Failed to notify');

            const spyOnSaveBatch = jest
                .spyOn(expiredRentsNotificationBatchRepository, 'create')
                .mockResolvedValueOnce();

            await handler.notifyExpiredRents({ batchSize: 1 });

            expect(spyOnSaveBatch).toHaveBeenCalledTimes(1);
            expect(spyOnSaveBatch).toHaveBeenCalledWith({
                id: expect.any(String),
                rentIds: [rentId2],
            });
        });

        it('should retry failed batches', async () => {
            const batchId = randomUUID();
            jest.spyOn(dhlITClient, 'notifyExpiredParcels')
                .mockResolvedValueOnce()
                .mockResolvedValueOnce();

            jest.spyOn(expiredRentsNotificationBatchRepository, 'delete').mockResolvedValueOnce();
            jest.spyOn(expiredRentsNotificationBatchRepository, 'list').mockResolvedValueOnce([
                { id: batchId, failureCount: 1, rentIds: [rentId1] },
            ]);

            jest.spyOn(expiredRentsRepository, 'delete')
                .mockResolvedValueOnce()
                .mockResolvedValueOnce();

            jest.spyOn(expiredRentsRepository, 'list').mockResolvedValueOnce([
                expiredRent1,
                expiredRent2,
            ]);

            await handler.notifyExpiredRents({ batchSize: 1 });

            expect(dhlITClient.notifyExpiredParcels).toHaveBeenCalledTimes(2);

            expect(dhlITClient.notifyExpiredParcels).toHaveBeenCalledWith({
                parcels: [
                    {
                        productId: rentExternalId1,
                        nodeId: bloqExternalId1,
                        productType,
                    },
                ],
            });

            expect(dhlITClient.notifyExpiredParcels).toHaveBeenCalledWith({
                parcels: [
                    {
                        productId: rentExternalId2,
                        nodeId: bloqExternalId2,
                        productType,
                    },
                ],
            });
        });

        it('should not retry failed batches that does not contain any existing rent', async () => {
            const batchId = randomUUID();

            jest.spyOn(expiredRentsNotificationBatchRepository, 'delete').mockResolvedValueOnce();
            jest.spyOn(expiredRentsNotificationBatchRepository, 'list').mockResolvedValueOnce([
                { id: batchId, failureCount: 1, rentIds: [rentId1] },
            ]);

            jest.spyOn(expiredRentsRepository, 'list').mockResolvedValueOnce([]);

            await handler.notifyExpiredRents({ batchSize: 1 });

            expect(dhlITClient.notifyExpiredParcels).not.toHaveBeenCalled();
            expect(expiredRentsNotificationBatchRepository.delete).toHaveBeenCalledTimes(1);
            expect(expiredRentsNotificationBatchRepository.delete).toHaveBeenCalledWith(batchId);
        });

        it('should remove failed batches and associated rents from the db if they succeed', async () => {
            const batchId = randomUUID();
            jest.spyOn(dhlITClient, 'notifyExpiredParcels').mockResolvedValueOnce();

            jest.spyOn(expiredRentsRepository, 'list').mockResolvedValueOnce([expiredRent1]);
            jest.spyOn(expiredRentsRepository, 'delete').mockResolvedValueOnce();

            jest.spyOn(expiredRentsNotificationBatchRepository, 'delete').mockResolvedValueOnce();
            jest.spyOn(expiredRentsNotificationBatchRepository, 'list').mockResolvedValueOnce([
                { id: batchId, failureCount: 1, rentIds: [rentId1] },
            ]);

            await handler.notifyExpiredRents({ batchSize: 1 });

            expect(expiredRentsNotificationBatchRepository.delete).toHaveBeenCalledTimes(1);
            expect(expiredRentsNotificationBatchRepository.delete).toHaveBeenCalledWith(batchId);
        });

        it('should delete the rents that were successfully notified', async () => {
            jest.spyOn(dhlITClient, 'notifyExpiredParcels')
                .mockResolvedValueOnce()
                .mockResolvedValueOnce();

            jest.spyOn(expiredRentsNotificationBatchRepository, 'list').mockResolvedValueOnce([]);

            jest.spyOn(expiredRentsRepository, 'delete')
                .mockResolvedValueOnce()
                .mockResolvedValueOnce();

            jest.spyOn(expiredRentsRepository, 'list').mockResolvedValueOnce([
                expiredRent1,
                expiredRent2,
            ]);

            const spyOnDeleteRent = jest
                .spyOn(expiredRentsRepository, 'delete')
                .mockResolvedValueOnce();

            await handler.notifyExpiredRents({ batchSize: 1 });

            expect(spyOnDeleteRent).toHaveBeenCalledTimes(2);
            expect(spyOnDeleteRent).toHaveBeenCalledWith(rentId1);
            expect(spyOnDeleteRent).toHaveBeenCalledWith(rentId2);
        });

        it('should increment the failure count of a batch if it fails again', async () => {
            const batchId = randomUUID();

            jest.spyOn(dhlITClient, 'notifyExpiredParcels')
                .mockRejectedValueOnce('Failed to notify')
                .mockRejectedValueOnce('Failed to notify');

            jest.spyOn(expiredRentsNotificationBatchRepository, 'create').mockResolvedValueOnce();
            jest.spyOn(expiredRentsNotificationBatchRepository, 'delete').mockResolvedValueOnce();
            jest.spyOn(expiredRentsNotificationBatchRepository, 'update').mockResolvedValueOnce();
            jest.spyOn(expiredRentsNotificationBatchRepository, 'list').mockResolvedValueOnce([
                { id: batchId, failureCount: 1, rentIds: [rentId1] },
            ]);

            jest.spyOn(expiredRentsRepository, 'list').mockResolvedValueOnce([
                expiredRent1,
                expiredRent2,
            ]);

            await handler.notifyExpiredRents({ batchSize: 1 });

            expect(expiredRentsNotificationBatchRepository.update).toHaveBeenCalledTimes(1);
            expect(expiredRentsNotificationBatchRepository.update).toHaveBeenCalledWith(
                batchId,
                expect.objectContaining({ failureCount: 2 }),
            );
        });

        it('should delete the batch and the rents from the db if the failure count exceeds the limit', async () => {
            const batchId = randomUUID();

            jest.spyOn(dhlITClient, 'notifyExpiredParcels').mockRejectedValueOnce(
                'Failed to notify',
            );

            jest.spyOn(expiredRentsNotificationBatchRepository, 'delete').mockResolvedValueOnce();
            jest.spyOn(expiredRentsNotificationBatchRepository, 'update').mockResolvedValueOnce();
            jest.spyOn(expiredRentsNotificationBatchRepository, 'list').mockResolvedValueOnce([
                { id: batchId, failureCount: 2, rentIds: [rentId1] },
            ]);

            jest.spyOn(expiredRentsRepository, 'delete').mockResolvedValueOnce();
            jest.spyOn(expiredRentsRepository, 'list').mockResolvedValueOnce([expiredRent1]);

            await handler.notifyExpiredRents({ batchSize: 1 });

            expect(expiredRentsNotificationBatchRepository.delete).toHaveBeenCalledTimes(1);
            expect(expiredRentsNotificationBatchRepository.delete).toHaveBeenCalledWith(batchId);

            expect(expiredRentsRepository.delete).toHaveBeenCalledTimes(1);
            expect(expiredRentsRepository.delete).toHaveBeenCalledWith(rentId1);
        });
    });

    describe('handleRentFinishedEvent', () => {
        const sessionId = randomUUID();
        const courierId = randomUUID();
        const rentMetadata = { productType: rentProductType };
        const baseEventMetadata = {
            rentMetadata,
            externalID: partnerRentId,
            bloqExternalID: partnerBloqId,
        };

        it('should delete the rent from the db if it is expired', async () => {
            jest.spyOn(courierPickupSessionsRepository, 'create').mockResolvedValueOnce();
            jest.spyOn(courierPickupSessionsRepository, 'exists').mockResolvedValueOnce(false);

            const partnerRentId = randomUUID();
            const rentFinishedEvent: RentEvent = {
                rent: bloqitRentId,
                bloq: bloqitBloqId,
                locker: bloqitLockerId,
                timestamp: new Date(),
                customer: { email: '<EMAIL>' },
                actionInitiatedBy: {
                    role: Roles.COURIER,
                    id: 'courier-1234',
                    session: 'session-1234',
                },
                metadata: {
                    externalID: partnerRentId,
                    offlinePickup: true,
                    lockerExternalID: 'A1',
                    rentMetadata: { productType: 'product-type-1' },
                },
            };

            jest.spyOn(expiredRentsRepository, 'exists').mockResolvedValueOnce(true);
            jest.spyOn(expiredRentsRepository, 'delete').mockResolvedValueOnce();

            await handler.handleRentFinishedEvent(rentFinishedEvent);

            expect(expiredRentsRepository.delete).toHaveBeenCalledTimes(1);
        });

        describe('Courier flow', () => {
            const baseEvent = {
                rent: bloqitRentId,
                bloq: bloqitBloqId,
                locker: bloqitLockerId,
                message: 'Rent finished',
                timestamp: new Date(),
                customer: { email: '<EMAIL>' },
                actionInitiatedBy: { id: courierId, session: sessionId, role: Roles.COURIER },
                metadata: baseEventMetadata,
            };

            describe('new session', () => {
                beforeEach(() => {
                    jest.spyOn(expiredRentsRepository, 'exists').mockResolvedValueOnce(false);
                    jest.spyOn(courierPickupSessionsRepository, 'create').mockResolvedValueOnce();
                    jest.spyOn(courierPickupSessionsRepository, 'exists').mockResolvedValueOnce(
                        false,
                    );
                });

                afterEach(() => jest.clearAllMocks());

                it('should create a new session and append the product if the session does not exist', async () => {
                    await handler.handleRentFinishedEvent(baseEvent);

                    const expectedProduct = {
                        productId: partnerRentId,
                        product: rentMetadata.productType,
                        isMissing: false,
                        isExpired: false,
                    };

                    expect(courierPickupSessionsRepository.create).toHaveBeenCalledTimes(1);
                    expect(courierPickupSessionsRepository.create).toHaveBeenCalledWith({
                        courierId,
                        sessionId,
                        products: [expectedProduct],
                    });
                });

                it('should mark the product as missing if this reason was provided', async () => {
                    const metadata = {
                        ...baseEventMetadata,
                        pickUpInfo: {
                            success: false,
                            errorReasons: [pickUpErrorReasons.RENT_MISSING],
                        },
                    };

                    const event = { ...baseEvent, metadata };
                    await handler.handleRentFinishedEvent(event);

                    const expectedProduct = {
                        productId: partnerRentId,
                        product: rentMetadata.productType,
                        isMissing: true,
                        isExpired: false,
                    };

                    expect(courierPickupSessionsRepository.create).toHaveBeenCalledTimes(1);
                    expect(courierPickupSessionsRepository.create).toHaveBeenCalledWith({
                        courierId,
                        sessionId,
                        products: [expectedProduct],
                    });
                });

                it('should mark the product as expired if this reason was provided', async () => {
                    const metadata = {
                        ...baseEventMetadata,
                        pickUpInfo: {
                            success: false,
                            errorReasons: [pickUpErrorReasons.RENT_EXPIRED],
                        },
                    };

                    const event = { ...baseEvent, metadata };
                    await handler.handleRentFinishedEvent(event);

                    const expectedProduct = {
                        productId: partnerRentId,
                        product: rentMetadata.productType,
                        isExpired: true,
                        isMissing: false,
                    };

                    expect(courierPickupSessionsRepository.create).toHaveBeenCalledTimes(1);
                    expect(courierPickupSessionsRepository.create).toHaveBeenCalledWith({
                        courierId,
                        sessionId,
                        products: [expectedProduct],
                    });
                });

                it('should register a new product and mark the existing one as missing if a new code was read during pick up', async () => {
                    jest.spyOn(courierPickupSessionsRepository, 'create').mockResolvedValueOnce();

                    const unexpectedProductCode = 'new-product-code-1234';
                    const metadata = {
                        ...baseEventMetadata,
                        pickUpInfo: {
                            success: false,
                            codeRead: unexpectedProductCode,
                            errorReasons: [pickUpErrorReasons.DIFFERENT_CODES],
                        },
                    };

                    const event = { ...baseEvent, metadata };
                    await handler.handleRentFinishedEvent(event);

                    const missingProduct = {
                        productId: partnerRentId,
                        product: rentMetadata.productType,
                        isMissing: true,
                        isExpired: false,
                    };

                    const unexpectedProduct = {
                        productId: unexpectedProductCode,
                        product: rentMetadata.productType,
                    };

                    expect(courierPickupSessionsRepository.create).toHaveBeenCalledTimes(1);
                    expect(courierPickupSessionsRepository.create).toHaveBeenCalledWith({
                        courierId,
                        sessionId,
                        products: [missingProduct, unexpectedProduct],
                    });
                });
            });

            describe('existing session', () => {
                beforeEach(() => {
                    jest.spyOn(expiredRentsRepository, 'exists').mockResolvedValueOnce(false);
                    jest.spyOn(courierPickupSessionsRepository, 'append').mockResolvedValueOnce();
                    jest.spyOn(courierPickupSessionsRepository, 'exists').mockResolvedValueOnce(
                        true,
                    );
                });

                afterEach(() => jest.clearAllMocks());

                it('should append a product to an existing pickup session', async () => {
                    await handler.handleRentFinishedEvent(baseEvent);

                    const expectedProduct: Product = {
                        productId: partnerRentId,
                        product: rentMetadata.productType,
                        isMissing: false,
                        isExpired: false,
                    };

                    expect(courierPickupSessionsRepository.append).toHaveBeenCalledTimes(1);
                    expect(courierPickupSessionsRepository.append).toHaveBeenCalledWith({
                        courierId,
                        sessionId,
                        products: [expectedProduct],
                    });
                });

                it('should mark the product as missing if this reason was provided', async () => {
                    const metadata = {
                        ...baseEventMetadata,
                        pickUpInfo: {
                            success: false,
                            errorReasons: [pickUpErrorReasons.RENT_MISSING],
                        },
                    };

                    const event = { ...baseEvent, metadata };
                    await handler.handleRentFinishedEvent(event);

                    const expectedProduct = {
                        productId: partnerRentId,
                        product: rentMetadata.productType,
                        isMissing: true,
                        isExpired: false,
                    };

                    expect(courierPickupSessionsRepository.append).toHaveBeenCalledTimes(1);
                    expect(courierPickupSessionsRepository.append).toHaveBeenCalledWith({
                        courierId,
                        sessionId,
                        products: [expectedProduct],
                    });
                });

                it('should mark the product as expired if this reason was provided', async () => {
                    const metadata = {
                        ...baseEventMetadata,
                        pickUpInfo: {
                            success: false,
                            errorReasons: [pickUpErrorReasons.RENT_EXPIRED],
                        },
                    };

                    const event = { ...baseEvent, metadata };
                    await handler.handleRentFinishedEvent(event);

                    const expectedProduct = {
                        productId: partnerRentId,
                        product: rentMetadata.productType,
                        isExpired: true,
                        isMissing: false,
                    };

                    expect(courierPickupSessionsRepository.append).toHaveBeenCalledTimes(1);
                    expect(courierPickupSessionsRepository.append).toHaveBeenCalledWith({
                        courierId,
                        sessionId,
                        products: [expectedProduct],
                    });
                });

                it('should register a new product and mark the existing one as missing if a new code was read during pick up', async () => {
                    jest.spyOn(courierPickupSessionsRepository, 'create').mockResolvedValueOnce();

                    const unexpectedProductCode = 'new-product-code-1234';
                    const metadata = {
                        ...baseEventMetadata,
                        pickUpInfo: {
                            success: false,
                            errorReasons: [pickUpErrorReasons.DIFFERENT_CODES],
                            codeRead: unexpectedProductCode,
                        },
                    };

                    const event = { ...baseEvent, metadata };
                    await handler.handleRentFinishedEvent(event);

                    const missingProduct = {
                        productId: partnerRentId,
                        product: rentMetadata.productType,
                        isMissing: true,
                        isExpired: false,
                    };

                    const unexpectedProduct = {
                        productId: unexpectedProductCode,
                        product: rentMetadata.productType,
                    };

                    expect(courierPickupSessionsRepository.append).toHaveBeenCalledTimes(1);
                    expect(courierPickupSessionsRepository.append).toHaveBeenCalledWith({
                        courierId,
                        sessionId,
                        products: [missingProduct, unexpectedProduct],
                    });
                });
            });
        });

        describe('Customer flow', () => {
            const event = {
                rent: bloqitRentId,
                bloq: bloqitBloqId,
                locker: bloqitLockerId,
                customer: { email: '<EMAIL>' },
                message: 'Successful collection for rent bloqit-rent-id-123',
                timestamp: new Date(),
                actionInitiatedBy: { role: Roles.CUSTOMER, id: courierId },
                metadata: {
                    externalID: partnerRentId,
                    bloqExternalID: partnerBloqId,
                    rentMetadata: { productType: rentProductType },
                },
            };

            it('should notify an item collection performed by a courier', async () => {
                jest.spyOn(expiredRentsRepository, 'exists').mockResolvedValueOnce(false);
                jest.spyOn(dhlITClient, 'notifyCustomerPickUp').mockResolvedValueOnce(undefined);

                await handler.handleRentFinishedEvent(event);

                expect(dhlITClient.notifyCustomerPickUp).toHaveBeenCalledTimes(1);
                expect(dhlITClient.notifyCustomerPickUp).toHaveBeenCalledWith({
                    agentId: event.bloq,
                    time: event.timestamp,
                    nodeId: event.metadata.bloqExternalID,
                    product: {
                        productId: event.metadata.externalID,
                        product: event.metadata.rentMetadata.productType,
                    },
                });
            });
        });
    });
});
