import { type ExpiredRentsNotificationBatchRepository } from '..';
import { type ExpiredRentsNotificationBatch } from '../../../../models';

export class FakeExpiredRentsNotificationsBatchRepository
    implements ExpiredRentsNotificationBatchRepository
{
    async list(_criteria?: any): Promise<ExpiredRentsNotificationBatch[]> {
        throw new Error('Method not implemented.');
    }

    async create(_batch: { id: string; rentIds: string[] }): Promise<void> {
        throw new Error('Method not implemented.');
    }

    async delete(_id: string): Promise<void> {
        throw new Error('Method not implemented.');
    }

    async update(_id: string, _set: any): Promise<void> {
        throw new Error('Method not implemented.');
    }
}
