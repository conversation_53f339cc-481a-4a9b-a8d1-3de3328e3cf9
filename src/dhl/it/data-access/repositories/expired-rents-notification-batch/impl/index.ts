import { type ExpiredRentsNotificationBatchRepository } from '..';
import { type DocumentDatabaseClient } from '../../../../../../core/data-access/document';
import { type ExpiredRentsNotificationBatch } from '../../../../models';

export class ExpiredRentsNotificationBatchRepositoryImpl
    implements ExpiredRentsNotificationBatchRepository
{
    private readonly collection: DocumentDatabaseClient<ExpiredRentsNotificationBatch>;

    constructor(props: { collection: DocumentDatabaseClient<ExpiredRentsNotificationBatch> }) {
        this.collection = props.collection;
    }

    async list(): Promise<ExpiredRentsNotificationBatch[]> {
        return await this.collection.list({});
    }

    async create({ id, rentIds }: { id: string; rentIds: string[] }): Promise<void> {
        await this.collection.save({ id, rentIds, failureCount: 0 });
    }

    async update(id: string, set: any): Promise<void> {
        await this.collection.findByIdAndUpdate(id, set);
    }

    async delete(id: string): Promise<void> {
        await this.collection.findByIdAndDelete(id);
    }
}
