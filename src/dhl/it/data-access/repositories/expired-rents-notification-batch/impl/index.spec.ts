import { randomUUID } from 'crypto';
import { type ExpiredRentsNotificationBatch } from '../../../../models';
import { FakeDocumentDatabaseClient } from '../../../../../../core/data-access/document/fake';
import { ExpiredRentsNotificationBatchRepositoryImpl } from '.';

describe('ExpiredRentsNotificationBatchRepositoryImpl', () => {
    const rentId1 = randomUUID();
    const rentId2 = randomUUID();

    describe('create', () => {
        it('should create a new batch', async () => {
            const batchId = randomUUID();
            const collection = new FakeDocumentDatabaseClient<ExpiredRentsNotificationBatch>();
            const spyOnSave = jest.spyOn(collection, 'save').mockResolvedValueOnce();

            const repo = new ExpiredRentsNotificationBatchRepositoryImpl({ collection });

            await repo.create({ id: batchId, rentIds: [rentId1, rentId2] });

            expect(spyOnSave).toHaveBeenCalledWith({
                id: batchId,
                failureCount: 0,
                rentIds: [rentId1, rentId2],
            });
        });
    });

    describe('list', () => {
        it('should list all batches', async () => {
            const batch = {
                id: randomUUID(),
                failureCount: 0,
                rentIds: [rentId1, rentId2],
                createdAt: new Date(),
            };

            const collection = new FakeDocumentDatabaseClient<ExpiredRentsNotificationBatch>();
            jest.spyOn(collection, 'list').mockResolvedValueOnce([batch]);

            const repo = new ExpiredRentsNotificationBatchRepositoryImpl({ collection });
            const result = await repo.list();

            expect(result).toEqual([batch]);
        });
    });

    describe('delete', () => {
        it('should delete a batch', async () => {
            const batchId = randomUUID();
            const collection = new FakeDocumentDatabaseClient<ExpiredRentsNotificationBatch>();
            const spyOnRemoveOne = jest
                .spyOn(collection, 'findByIdAndDelete')
                .mockResolvedValueOnce();

            const repo = new ExpiredRentsNotificationBatchRepositoryImpl({ collection });
            await repo.delete(batchId);

            expect(spyOnRemoveOne).toHaveBeenCalled();
        });
    });

    describe('update', () => {
        it('should update a batch', async () => {
            const batchId = randomUUID();
            const collection = new FakeDocumentDatabaseClient<ExpiredRentsNotificationBatch>();
            const spyOnUpdateOne = jest
                .spyOn(collection, 'findByIdAndUpdate')
                .mockResolvedValueOnce();

            const repo = new ExpiredRentsNotificationBatchRepositoryImpl({ collection });
            await repo.update(batchId, { failureCount: 2 });

            expect(spyOnUpdateOne).toHaveBeenCalledWith(batchId, { failureCount: 2 });
        });
    });
});
