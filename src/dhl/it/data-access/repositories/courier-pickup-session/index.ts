import { type Product } from '../../../models';

export interface CourierPickupSessionsRepository {
    create: (args: { courierId: string; sessionId: string; products: Product[] }) => Promise<void>;

    append: (args: { courierId: string; sessionId: string; products: Product[] }) => Promise<void>;

    delete: (args: { courierId: string; sessionId: string }) => Promise<void>;

    exists: (args: { courierId: string; sessionId: string }) => Promise<boolean>;

    fetchProducts: (args: { courierId: string; sessionId: string }) => Promise<Product[]>;

    markProductAsMissing: (_args: {
        courierId: string;
        sessionId: string;
        productId: string;
    }) => Promise<void>;

    markProductAsExpired: (_args: {
        courierId: string;
        sessionId: string;
        productId: string;
    }) => Promise<void>;
}
