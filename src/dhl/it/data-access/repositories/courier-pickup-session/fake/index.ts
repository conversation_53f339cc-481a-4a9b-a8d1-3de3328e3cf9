import { type CourierPickupSessionsRepository } from '..';
import { type Product } from '../../../../models';

export class FakeCourierPickupSessionsRepository implements CourierPickupSessionsRepository {
    async create(_args: {
        courierId: string;
        sessionId: string;
        products: Product[];
    }): Promise<void> {
        throw new Error('Not implemented. Please override it in your test.');
    }

    async append(_args: {
        courierId: string;
        sessionId: string;
        products: Product[];
    }): Promise<void> {
        throw new Error('Not implemented. Please override it in your test.');
    }

    async delete(_args: { courierId: string; sessionId: string }): Promise<void> {
        throw new Error('Not implemented. Please override it in your test.');
    }

    async exists(_args: { courierId: string; sessionId: string }): Promise<boolean> {
        throw new Error('Not implemented. Please override it in your test.');
    }

    async fetchProducts(_args: { courierId: string; sessionId: string }): Promise<Product[]> {
        throw new Error('Not implemented. Please override it in your test.');
    }

    async markProductAsMissing(_args: {
        courierId: string;
        sessionId: string;
        productId: string;
    }): Promise<void> {
        throw new Error('Not implemented. Please override it in your test.');
    }

    async markProductAsExpired(_args: {
        courierId: string;
        sessionId: string;
        productId: string;
    }): Promise<void> {
        throw new Error('Not implemented. Please override it in your test.');
    }
}
