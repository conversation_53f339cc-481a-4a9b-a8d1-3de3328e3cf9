import { type CourierPickupSessionsRepository } from '..';
import { type CourierPickupSession, type Product } from '../../../../models';
import { type DocumentDatabaseClient } from '../../../../../../core/data-access/document';

class ProductNotFoundError extends Error {
    constructor(args: { productId: string; sessionId: string; courierId: string }) {
        const { productId, sessionId, courierId } = args;
        super(
            `No product '${productId}' was found for courier session '${sessionId}' and courier id '${courierId}'`,
        );
    }
}

export class CourierPickupSessionsRepositoryImpl implements CourierPickupSessionsRepository {
    private readonly collection: DocumentDatabaseClient<CourierPickupSession>;

    constructor(deps: { collection: DocumentDatabaseClient<CourierPickupSession> }) {
        this.collection = deps.collection;
    }

    async create(args: {
        courierId: string;
        sessionId: string;
        products: Product[];
    }): Promise<void> {
        const { courierId, sessionId, products } = args;
        await this.collection.save({ courierId, sessionId, products });
    }

    async append(args: {
        courierId: string;
        sessionId: string;
        products: Product[];
    }): Promise<void> {
        const { courierId, sessionId, products } = args;
        await this.collection.updateOne({
            filter: { courierId, sessionId },
            set: { $push: { products: { $each: products } } },
        });
    }

    async delete(args: { courierId: string; sessionId: string }): Promise<void> {
        const { courierId, sessionId } = args;
        await this.collection.removeOne({ courierId, sessionId });
    }

    async exists(args: { courierId: string; sessionId: string }): Promise<boolean> {
        const { courierId, sessionId } = args;
        const result = await this.collection.findBy({ courierId, sessionId });
        return result !== undefined;
    }

    async fetchProducts(args: { courierId: string; sessionId: string }): Promise<Product[]> {
        const { courierId, sessionId } = args;
        const result = await this.collection.findBy({ courierId, sessionId });
        return result?.products ?? [];
    }

    async markProductAsMissing(args: {
        courierId: string;
        sessionId: string;
        productId: string;
    }): Promise<void> {
        await this.transformExistingProductInPlace({
            ...args,
            transformFn: p => ({ ...p, isMissing: true }),
        });
    }

    async markProductAsExpired(args: {
        courierId: string;
        sessionId: string;
        productId: string;
    }): Promise<void> {
        await this.transformExistingProductInPlace({
            ...args,
            transformFn: p => ({ ...p, isExpired: true }),
        });
    }

    private transformProductInPlace(args: {
        targetProduct: Product;
        allProducts: Product[];
        transformFn: (product: Product) => Product;
    }): Product[] {
        const { targetProduct, allProducts, transformFn } = args;
        return allProducts.map((p: Product) =>
            p.productId === targetProduct.productId ? transformFn(p) : p,
        );
    }

    private async transformExistingProductInPlace(args: {
        courierId: string;
        sessionId: string;
        productId: string;
        transformFn: (product: Product) => Product;
    }): Promise<void> {
        const { courierId, sessionId, productId, transformFn } = args;
        const products = await this.fetchProducts({ courierId, sessionId });
        const targetProduct = products.find(p => p.productId === productId);

        if (targetProduct === undefined)
            throw new ProductNotFoundError({ productId, sessionId, courierId });

        const updatedProducts = this.transformProductInPlace({
            targetProduct,
            allProducts: products,
            transformFn,
        });

        await this.collection.updateOne({
            filter: { courierId, sessionId },
            set: { products: updatedProducts },
        });
    }
}
