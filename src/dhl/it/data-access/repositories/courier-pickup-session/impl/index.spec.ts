import { randomUUID } from 'crypto';

import { type CourierPickupSession } from '../../../../models';
import { FakeDocumentDatabaseClient } from '../../../../../../core/data-access/document/fake';
import { CourierPickupSessionsRepositoryImpl } from '.';

describe('CourierPickupSessionsRepositoryImpl', () => {
    const courierId = randomUUID();
    const sessionId = randomUUID();
    const product1 = { productId: randomUUID(), product: randomUUID() };
    const product2 = { productId: randomUUID(), product: randomUUID() };
    const product3 = { productId: randomUUID(), product: randomUUID() };

    const collection = new FakeDocumentDatabaseClient<CourierPickupSession>();
    const repo = new CourierPickupSessionsRepositoryImpl({ collection });

    afterEach(() => {
        jest.clearAllMocks();
    });

    describe('create', () => {
        it('should create a new record for a pickup session', async () => {
            jest.spyOn(collection, 'save').mockResolvedValueOnce();

            await repo.create({ courierId, sessionId, products: [product1] });

            expect(collection.save).toHaveBeenCalledTimes(1);
            expect(collection.save).toHaveBeenCalledWith({
                courierId,
                sessionId,
                products: [product1],
            });
        });
    });

    describe('append', () => {
        it('should append a rent id to an existing pickup session', async () => {
            jest.spyOn(collection, 'updateOne').mockResolvedValueOnce();

            await repo.append({ courierId, sessionId, products: [product1] });

            expect(collection.updateOne).toHaveBeenCalledTimes(1);
            expect(collection.updateOne).toHaveBeenCalledWith({
                filter: { courierId, sessionId },
                set: { $push: { products: { $each: [product1] } } },
            });
        });
    });

    describe('delete', () => {
        it('should delete an existing pickup session', async () => {
            jest.spyOn(collection, 'removeOne').mockResolvedValueOnce();

            await repo.delete({ courierId, sessionId });

            expect(collection.removeOne).toHaveBeenCalledTimes(1);
            expect(collection.removeOne).toHaveBeenCalledWith({ courierId, sessionId });
        });
    });

    describe('exists', () => {
        it('should check whether a pickup session exists or not', async () => {
            jest.spyOn(collection, 'findBy').mockResolvedValueOnce({ courierId, sessionId });

            const result = await repo.exists({ courierId, sessionId });

            expect(collection.findBy).toHaveBeenCalledTimes(1);
            expect(collection.findBy).toHaveBeenCalledWith({ courierId, sessionId });
            expect(result).toBe(true);
        });
    });

    describe('fetchProducts', () => {
        it('should return an empty array if no pickup session is found', async () => {
            jest.spyOn(collection, 'findBy').mockResolvedValueOnce(undefined);

            const products = await repo.fetchProducts({ courierId, sessionId });
            expect(collection.findBy).toHaveBeenCalledTimes(1);
            expect(collection.findBy).toHaveBeenCalledWith({ courierId, sessionId });
            expect(products).toEqual([]);
        });

        it('should retrieve all product stored in a courier pickup session', async () => {
            const products = [product1, product2, product3];
            jest.spyOn(collection, 'findBy').mockResolvedValueOnce({
                courierId,
                sessionId,
                products,
            });

            const productsStored = await repo.fetchProducts({ courierId, sessionId });

            expect(collection.findBy).toHaveBeenCalledTimes(1);
            expect(collection.findBy).toHaveBeenCalledWith({ courierId, sessionId });
            expect(productsStored).toEqual(products);
        });
    });

    describe('markProductAsMissing', () => {
        it('should throw an error if product was not found', async () => {
            const productId = randomUUID();
            const products = [product1, product2, product3];

            jest.spyOn(collection, 'updateOne').mockResolvedValueOnce();
            jest.spyOn(collection, 'findBy').mockResolvedValueOnce({
                courierId,
                sessionId,
                products,
            });

            await expect(
                repo.markProductAsMissing({ courierId, sessionId, productId }),
            ).rejects.toThrow(
                `No product '${productId}' was found for courier session '${sessionId}' and courier id '${courierId}'`,
            );
        });

        it('should update a product to highlight it is missing', async () => {
            const productId = product2.productId;
            const products = [product1, product2, product3];

            jest.spyOn(collection, 'updateOne').mockResolvedValueOnce();
            jest.spyOn(collection, 'findBy').mockResolvedValueOnce({
                courierId,
                sessionId,
                products,
            });

            await repo.markProductAsMissing({ courierId, sessionId, productId });

            expect(collection.updateOne).toHaveBeenCalledTimes(1);
            expect(collection.updateOne).toHaveBeenCalledWith({
                filter: { courierId, sessionId },
                set: { products: [product1, { ...product2, isMissing: true }, product3] },
            });
        });
    });

    describe('markProductAsExpired', () => {
        it('should throw an error if product was not found', async () => {
            const productId = randomUUID();
            const products = [product1, product2, product3];

            jest.spyOn(collection, 'updateOne').mockResolvedValueOnce();
            jest.spyOn(collection, 'findBy').mockResolvedValueOnce({
                courierId,
                sessionId,
                products,
            });

            await expect(
                repo.markProductAsExpired({ courierId, sessionId, productId }),
            ).rejects.toThrow(
                `No product '${productId}' was found for courier session '${sessionId}' and courier id '${courierId}'`,
            );
        });

        it('should update a product to highlight it is missing', async () => {
            const productId = product2.productId;
            const products = [product1, product2, product3];

            jest.spyOn(collection, 'updateOne').mockResolvedValueOnce();
            jest.spyOn(collection, 'findBy').mockResolvedValueOnce({
                courierId,
                sessionId,
                products,
            });

            await repo.markProductAsExpired({ courierId, sessionId, productId });

            expect(collection.updateOne).toHaveBeenCalledTimes(1);
            expect(collection.updateOne).toHaveBeenCalledWith({
                filter: { courierId, sessionId },
                set: { products: [product1, { ...product2, isExpired: true }, product3] },
            });
        });
    });
});
