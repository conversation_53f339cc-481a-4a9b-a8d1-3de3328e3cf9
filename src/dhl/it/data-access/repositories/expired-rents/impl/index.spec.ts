import { randomUUID } from 'crypto';
import { ExpiredRentsRepositoryImpl } from '.';
import { FakeDocumentDatabaseClient } from '../../../../../../core/data-access/document/fake';
import { type ExpiredRent } from '../../../../models';

describe('ExpiredRentsRepository', () => {
    describe('create', () => {
        it('should create a new expired rent', async () => {
            const collection = new FakeDocumentDatabaseClient<ExpiredRent>();

            const spyOnCreate = jest.spyOn(collection, 'save').mockResolvedValueOnce();

            const repo = new ExpiredRentsRepositoryImpl({ collection });
            const metadata = { key: 'value' };
            const rentId = randomUUID();
            await repo.create({ metadata, rentId, partnerFriendlyId: 'dhl_it' });

            expect(spyOnCreate).toHaveBeenCalledTimes(1);
            expect(spyOnCreate).toHaveBeenCalledWith({
                metadata,
                rentId,
                partnerFriendlyId: 'dhl_it',
            });
        });
    });

    describe('list', () => {
        it('should list expired rents', async () => {
            const collection = new FakeDocumentDatabaseClient<ExpiredRent>();

            const spyOnList = jest.spyOn(collection, 'list').mockResolvedValueOnce([]);

            const repo = new ExpiredRentsRepositoryImpl({ collection });
            const criteria = { key: 'value' };
            await repo.list(criteria);

            expect(spyOnList).toHaveBeenCalledTimes(1);
            expect(spyOnList).toHaveBeenCalledWith(criteria);
        });
    });

    describe('delete', () => {
        it('should delete an expired rent', async () => {
            const collection = new FakeDocumentDatabaseClient<ExpiredRent>();

            const spyOnDelete = jest.spyOn(collection, 'removeOne').mockResolvedValueOnce();

            const repo = new ExpiredRentsRepositoryImpl({ collection });
            const id = randomUUID();
            await repo.delete(id);

            expect(spyOnDelete).toHaveBeenCalledTimes(1);
            expect(spyOnDelete).toHaveBeenCalledWith({ rentId: id });
        });
    });

    describe('exists', () => {
        const collection = new FakeDocumentDatabaseClient<ExpiredRent>();
        const repo = new ExpiredRentsRepositoryImpl({ collection });
        const criteria = { key: 'value' };

        afterEach(() => jest.clearAllMocks());

        it('should return true if the expired rent exists', async () => {
            const spyOnFindBy = jest.spyOn(collection, 'findBy').mockResolvedValueOnce({});

            const result = await repo.exists(criteria);

            expect(spyOnFindBy).toHaveBeenCalledTimes(1);
            expect(spyOnFindBy).toHaveBeenCalledWith(criteria);
            expect(result).toBe(true);
        });

        it('should return false if the expired rent does not exist', async () => {
            const spyOnFindBy = jest.spyOn(collection, 'findBy').mockResolvedValueOnce(undefined);

            const result = await repo.exists(criteria);

            expect(spyOnFindBy).toHaveBeenCalledTimes(1);
            expect(spyOnFindBy).toHaveBeenCalledWith(criteria);
            expect(result).toBe(false);
        });
    });
});
