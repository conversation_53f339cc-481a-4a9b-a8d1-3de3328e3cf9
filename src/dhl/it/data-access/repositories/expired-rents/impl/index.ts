import { type ExpiredRentsRepository } from '..';
import { type ExpiredRent } from '../../../../models';
import { type DocumentDatabaseClient } from '../../../../../../core/data-access/document';

export class ExpiredRentsRepositoryImpl implements ExpiredRentsRepository {
    private readonly collection: DocumentDatabaseClient<ExpiredRent>;

    constructor(deps: { collection: DocumentDatabaseClient<ExpiredRent> }) {
        this.collection = deps.collection;
    }

    async list(criteria: any): Promise<ExpiredRent[]> {
        return await this.collection.list(criteria);
    }

    async create(expiredRent: ExpiredRent): Promise<void> {
        await this.collection.save(expiredRent);
    }

    async delete(rentId: string): Promise<void> {
        await this.collection.removeOne({ rentId });
    }

    async exists(criteria: any): Promise<boolean> {
        const result = await this.collection.findBy(criteria);
        return result !== undefined;
    }
}
