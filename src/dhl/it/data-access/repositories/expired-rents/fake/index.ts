import { type ExpiredRentsRepository } from '..';
import { type ExpiredRent } from '../../../../models';

export class FakeExpiredRentsRepository implements ExpiredRentsRepository {
    async create(_args: ExpiredRent): Promise<void> {
        throw new Error('Not implemented. Please override this method in your test.');
    }

    async list(_criteria: any): Promise<ExpiredRent[]> {
        throw new Error('Not implemented. Please override this method in your test.');
    }

    async delete(_id: string): Promise<void> {
        throw new Error('Not implemented. Please override this method in your test.');
    }

    async exists(_criteria: any): Promise<boolean> {
        throw new Error('Not implemented. Please override this method in your test.');
    }
}
