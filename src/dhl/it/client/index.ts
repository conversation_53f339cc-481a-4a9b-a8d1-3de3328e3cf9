import { type Product } from '../models';

export interface DHLITDataResponse {
    status: string;
    errorCode: number;
    errorDesc?: string;
}

export enum DHLITResponseStatus {
    SUCCESS = 'Success',
    FAIL = 'Fail',
    SYSTEM_ERROR = 'SystemError',
}

/**
 * @description Structure to be sent to DHL IT in at `fetchShipmentInfoForDropoff` the method to
 * retrieve shipment information
 */
export interface FetchShipmentForDropoffRequest {
    /**
     * @property {string} productId Parcel ID code to be checked
     *
     */
    productId: string;

    /**
     * @property {string} channel Presumably our channel within DHL IT's API. Constant value APT
     */
    channel: 'APT';

    /**
     * @property {string} agentId Locker identification code in the Partner network
     */
    agentId: string;

    /**
     * @property {string} fromNode Locker identification code in the
     * Poste Italiane network (the “Frazionario”  code)
     */
    fromNode?: string;

    /**
     * @property {string} deviceId Second optional locker identification code
     */
    deviceId?: string;
}

export interface DHLITShipment {
    // - [Success/Fail/SystemError] Call outcome
    status: DHLITResponseStatus;

    // - <SEE ERROR CODES SECTION>
    errorCode: number;

    // - Checked parcel ID code
    productId?: string;

    // - PKR, PKX, PKE, HS4, HB4, HBX, etcc… It represents the key which uniquely identify a product in Poste Italiane.
    product?: string;

    // - Represents the unique identifier of the by-product in Poste Italiane.
    subProduct?: string;

    // - To be ignored.
    productName?: string;

    // - Indicate the priority of the shipment J1, J4 etc. (useful for cell opening prioritization algorithm for courier pickup)
    class?: string;

    // - Height in cm. It is returned 0 in case of missing data or error. The separator character is the dot.
    height: number;

    // - Width in cm. It is returned 0 in case of missing data or error. The separator character is the dot.
    width: number;

    // - Depth in cm. It is returned 0 in case of missing data or error. The separator character is the dot.
    depth: number;

    // - Weight in g. It is returned 0 in case of missing data or error. The separator character is the dot.
    weight: number;

    // - Sender email
    senderEmail?: string;

    // - Sender phone
    senderPhone?: string;

    /**
     * @property {string} receiverEmail Not in use at the moment
     */
    receiverEmail?: string;

    /**
     * @property {string} receiverName Not in use at the moment
     */
    receiverName?: string;

    /**
     * @property {string} receiverPhone Not in use at the moment
     */
    receiverPhone?: string;

    /**
     * @property {string} downloadUrl Not in use at the moment
     */
    downloadUrl?: string;
}

export enum DHLITAcquireShipmentErrorCode {
    SUCCESS = 0,
    INTERNAL_ERROR = 1,
    INVALID_CODE = 2,
    NON_EXISTENT_CODE = 3,
    IMPOSSIBLE_OPERATION = 4,
    CHANNEL_NOT_ENABLED_FOR_REQUEST = 5,
    INVALID_FRAZIONARIO_CODE = 6,
    PRODUCT_NOT_ENABLED_FOR_SERVICE = 7,
    UNKNOWN_PARCEL = 8,
}

export interface DHLITCourierShipment {
    status: DHLITResponseStatus;
    errorCode: DHLITAcquireShipmentErrorCode;
    errorDesc?: string;
    /**
     * Summary of the products contained in the shipment.
     */
    digests?: [
        {
            /**
             * PKR, PKX, PKE, HS4, HB4, HBX, etc… It represents the key which uniquely identify a product in Poste Italiane.
             */
            product: string;
            quantity: number;
            /**
             * Indicate the priority of the shipment: J1, J4 etc.
             */
            class: string;
        }?,
    ];
    /**
     * It represents the list of the various containers belonging to the same shipment.
     */
    containers: [
        {
            products: [
                {
                    /**
                     * Checked parcel ID code.
                     */
                    productId: string;
                    expiry?: Date;
                    /**
                     * PKR, PKX, PKE, HS4, HB4, HBX, etc… Represents the key which uniquely identify a product in Poste Italiane.
                     */
                    product: string;
                    /**
                     * HSP_, HSPA, etc Represents the unique identifier of the by-product in Poste Italiane
                     */
                    subProduct: string;
                    /**
                     * 0 (okay), >0 (error) Code to identify if an error has occurred and what type it is.
                     */
                    errorCode: number;
                    height: number;
                    width: number;
                    depth: number;
                    weight: number;
                    receiverName?: string;
                    receiverEmail?: string;
                },
            ];
        },
    ];
}

export interface DHLITNotifyCourierDropOffRequest {
    fromNode: string; // bloq external id
    agentId: string; // bloqit bloq id
    time: Date;
    channel: 'APT';
    containers: [
        products: [
            {
                productId: string; // Parcel ID code to be registered as deposited in the locker.
                product: string; // partnerRentId
                traceInfo: 'CP0';
                pin: string; // Unique code for subsequent parcel pickup by the end customer (the customer pickup PIN).
                qrcode: string; // Optional unique QR code for subsequent parcel pickup by the end customer (the customer pickup QR code)
            },
        ],
    ];
}

export interface DHLITShipmentProduct {
    /**
     * Parcel ID code to be registered as deposited in the locker.
     */
    productId: string;

    /**
     * Unique code for subsequent parcel pickup by the end customer (the customer pickup PIN).
     */
    pin: string;

    qrcode: string;

    product: string;

    traceInfo: 'CP0';
}

export interface DHLITClient {
    fetchShipmentInfoForCustomerDropoff: (props: {
        productId: string;
        agentId: string;
        nodeId: string;
    }) => Promise<DHLITShipment>;

    createProductsOnPartnerSide: (args: {
        nodeId: string;
        agentId: string;
        time: Date;
        products: Product[];
    }) => Promise<void>;

    sendCustomerDropOffStatusUpdate: (props: {
        product: Product;
        agentId: string;
        fromNode: string;
        deviceId?: string;
        customerEmail: string;
        time: Date;
    }) => Promise<void>;

    fetchShipmentInfoForCourierDropoff: (args: {
        agentId: string;
        shipmentId: string;
        nodeId?: string;
    }) => Promise<DHLITCourierShipment>;

    notifyCustomerPickUp: (args: {
        product: Product;
        nodeId: string;
        agentId: string;
        time: Date;
    }) => Promise<void>;

    notifyCourierShipmentDropOff: (args: {
        nodeId: string;
        agentId: string;
        product: Product;
        pin: string;
        time: Date;
    }) => Promise<void>;

    authenticateCourier: (args: {
        nodeId: string;
        pin: string;
        courierSessionId: string;
    }) => Promise<string>;

    notifyExpiredParcels: (args: {
        parcels: Array<{ productId: string; nodeId: string; productType: string }>;
    }) => Promise<void>;
}
