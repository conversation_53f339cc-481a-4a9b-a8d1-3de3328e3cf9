import { type DHLITClient, type DHLITShipment, type DHLITCourierShipment } from '..';
import { type Product } from '../../models';

export class FakeDHLITClient implements DHLITClient {
    async notifyCourierShipmentDropOff(_args: {
        nodeId: string;
        agentId: string;
        product: Product;
        pin: string;
        time: Date;
    }): Promise<void> {
        throw new Error('Not implemented. Please override it for your test purposes.');
    }

    async notifyCustomerPickUp(_args: {
        product: Product;
        nodeId: string;
        agentId: string;
        time: Date;
    }): Promise<void> {
        throw new Error('Not implemented. Please override it for your test purposes.');
    }

    async fetchShipmentInfoForCourierDropoff(_args: {
        agentId: string;
        shipmentId: string;
        nodeId?: string;
    }): Promise<DHLITCourierShipment> {
        throw new Error('Not implemented. Please override it for your test purposes.');
    }

    async sendCustomerDropOffStatusUpdate(_props: {
        product: Product;
        agentId: string;
        fromNode: string;
        deviceId?: string;
        customerEmail: string;
        time: Date;
    }): Promise<void> {
        throw new Error('Not implemented. Please override it for your test purposes.');
    }

    async createProductsOnPartnerSide(_args: {
        nodeId: string;
        agentId: string;
        time: Date;
        products: Product[];
    }): Promise<void> {
        throw new Error('Not implemented. Please override it for your test purposes.');
    }

    async fetchShipmentInfoForCustomerDropoff(_props: {
        productId: string;
        agentId: string;
    }): Promise<DHLITShipment> {
        throw new Error('Not implemented. Please override it for your test purposes.');
    }

    async authenticateCourier(_args: {
        nodeId: string;
        pin: string;
        courierSessionId: string;
    }): Promise<string> {
        throw new Error('Not implemented. Please override it for your test purposes.');
    }

    async notifyExpiredParcels(_args: {
        parcels: Array<{ productId: string; nodeId: string; productType: string }>;
    }): Promise<void> {
        throw new Error('Not implemented. Please override it for your test purposes.');
    }
}
