/* eslint-disable @typescript-eslint/unbound-method */
import { randomUUID } from 'node:crypto';
import { DHL_ITClientImpl } from '.';
import { DHLITAcquireShipmentErrorCode, DHLITResponseStatus } from '..';
import { type ApplicationEnvironment } from '../../../../core/env';
import { FakeHTTPClient } from '../../../../core/http-client/fake';
import { FakeLogger } from '../../../../core/logger/fake';

describe('DHL_ITClient', () => {
    const env = {
        DHL_IT_API_HOST: 'https://test.dhl.it',
        DHL_IT_AUTH_CLIENT_ID: 'client-id-123',
        DHL_IT_AUTH_CLIENT_SECRET: 'client-secret-123',
    } as unknown as ApplicationEnvironment;

    const baseURL = env.DHL_IT_API_HOST;
    const agentId = 'agent-id-123';
    const nodeId = 'partner-bloq-id-123';
    const productId = 'product-123456789';
    const productType = 'product-type-123456789';
    const deviceId = 'bloqit-bloq-id-11';
    const time = new Date();
    const customerEmail = 'customer-email-123';
    const logger = new FakeLogger();

    const headers = {
        'X-IBM-Client-Id': env.DHL_IT_AUTH_CLIENT_ID,
        'X-IBM-Client-Secret': env.DHL_IT_AUTH_CLIENT_SECRET,
    };

    describe('createRentOnPartnerSide', () => {
        const product1 = { productId: randomUUID(), product: randomUUID() };
        const product2 = { productId: randomUUID(), product: randomUUID() };

        const httpClient = new FakeHTTPClient();
        const client = new DHL_ITClientImpl({ httpClient, logger, env });

        afterEach(() => jest.clearAllMocks());

        it('should create a shipment at DHL ITs API', async () => {
            jest.spyOn(httpClient, 'post').mockResolvedValueOnce({
                status: 200,
                data: { status: DHLITResponseStatus.SUCCESS, errorCode: 0 },
            });

            await client.createProductsOnPartnerSide({
                nodeId,
                agentId,
                time: new Date(),
                products: [product1, product2],
            });

            expect(httpClient.post).toHaveBeenCalledTimes(1);
            expect(httpClient.post).toHaveBeenCalledWith({
                headers,
                url: `${baseURL}/shipment/create`,
                data: {
                    time: expect.any(Date),
                    agentId,
                    fromNode: nodeId,
                    channel: 'APT',
                    products: [
                        {
                            productId: product1.productId,
                            product: product1.product,
                            statoldv: 'ACC',
                        },
                        {
                            productId: product2.productId,
                            product: product2.product,
                            statoldv: 'ACC',
                        },
                    ],
                },
            });
        });

        it('should notify about missing products', async () => {
            const missingProduct = { ...product1, isMissing: true };

            jest.spyOn(httpClient, 'post').mockResolvedValueOnce({
                status: 200,
                data: { status: DHLITResponseStatus.SUCCESS, errorCode: 0 },
            });

            await client.createProductsOnPartnerSide({
                nodeId,
                agentId,
                time: new Date(),
                products: [missingProduct, product2],
            });

            expect(httpClient.post).toHaveBeenCalledTimes(1);
            expect(httpClient.post).toHaveBeenCalledWith({
                headers,
                url: `${baseURL}/shipment/create`,
                data: {
                    time: expect.any(Date),
                    agentId,
                    fromNode: nodeId,
                    channel: 'APT',
                    products: expect.arrayContaining([
                        {
                            productId: missingProduct.productId,
                            product: missingProduct.product,
                            statoldv: 'MAN',
                        },
                    ]),
                },
            });
        });

        it('should notify about expired products', async () => {
            const expiredProduct = { ...product2, isExpired: true };

            jest.spyOn(httpClient, 'post').mockResolvedValueOnce({
                status: 200,
                data: { status: DHLITResponseStatus.SUCCESS, errorCode: 0 },
            });

            await client.createProductsOnPartnerSide({
                nodeId,
                agentId,
                time: new Date(),
                products: [product1, expiredProduct],
            });

            expect(httpClient.post).toHaveBeenCalledTimes(1);
            expect(httpClient.post).toHaveBeenCalledWith({
                headers,
                url: `${baseURL}/shipment/create`,
                data: {
                    time: expect.any(Date),
                    agentId,
                    fromNode: nodeId,
                    channel: 'APT',
                    products: expect.arrayContaining([
                        {
                            productId: expiredProduct.productId,
                            product: expiredProduct.product,
                            statoldv: 'CGI',
                        },
                    ]),
                },
            });
        });

        it('should throw an error if custom status code is not Success', async () => {
            const product1 = {
                productId: randomUUID(),
                product: randomUUID(),
            };

            const httpClient = new FakeHTTPClient();
            jest.spyOn(httpClient, 'post').mockResolvedValueOnce({
                status: 200,
                data: { status: DHLITResponseStatus.FAIL, errorCode: 10 },
            });

            const client = new DHL_ITClientImpl({ httpClient, logger, env });
            await expect(
                client.createProductsOnPartnerSide({
                    nodeId,
                    agentId,
                    time: new Date(),
                    products: [product1],
                }),
            ).rejects.toThrow('Error creating rent on partner side');

            expect(httpClient.post).toHaveBeenCalledTimes(1);
            expect(httpClient.post).toHaveBeenCalledWith({
                headers,
                url: `${baseURL}/shipment/create`,
                data: {
                    time: expect.any(Date),
                    agentId,
                    fromNode: nodeId,
                    channel: 'APT',
                    products: [
                        {
                            productId: product1.productId,
                            product: product1.product,
                            statoldv: 'ACC',
                        },
                    ],
                },
            });
        });
    });

    describe('fetchShipmentInfoForDropoff', () => {
        it('should fetch a shipment at DHL IT API so the customer starts a dropoff', async () => {
            const httpClient = new FakeHTTPClient();
            jest.spyOn(httpClient, 'post').mockResolvedValueOnce({
                status: 200,
                data: {
                    status: DHLITResponseStatus.SUCCESS,
                    productId,
                    errorCode: 0,
                    height: 10,
                    width: 10,
                    depth: 10,
                    weight: 10,
                },
            });

            const client = new DHL_ITClientImpl({ httpClient, logger, env });
            const response = await client.fetchShipmentInfoForCustomerDropoff({
                productId,
                agentId,
                nodeId,
            });

            expect(response.status).toBe(DHLITResponseStatus.SUCCESS);
            expect(httpClient.post).toHaveBeenCalledTimes(1);
            expect(httpClient.post).toHaveBeenCalledWith(
                expect.objectContaining({
                    headers,
                    url: `${baseURL}/customer/dropOff`,
                    data: { productId, agentId, fromNode: nodeId, channel: 'APT' },
                }),
            );
        });
    });

    describe('sendDropOffStatusUpdate', () => {
        it('should send a request to DHL IT to notify an update in the drop off status of a shipment', async () => {
            const httpClient = new FakeHTTPClient();
            jest.spyOn(httpClient, 'post').mockResolvedValueOnce({
                status: 200,
                data: { status: DHLITResponseStatus.SUCCESS, errorCode: 0 },
            });

            const client = new DHL_ITClientImpl({ httpClient, logger, env });
            await client.sendCustomerDropOffStatusUpdate({
                product: {
                    productId,
                    product: productType,
                },
                agentId,
                deviceId,
                fromNode: nodeId,
                customerEmail,
                time,
            });

            expect(httpClient.post).toHaveBeenCalledWith({
                headers,
                url: `${baseURL}/customer/dropOffStatusUpdate`,
                data: {
                    productId,
                    agentId,
                    deviceId,
                    fromNode: nodeId,
                    channel: 'APT',
                    product: productType,
                    time,
                    senderEmail: customerEmail,
                },
            });
        });

        it('should log an error if custom status code is not Success', async () => {
            const status = DHLITResponseStatus.FAIL;
            const code = 10;
            const description = 'Error sending drop off status update';

            jest.spyOn(logger, 'error').mockReturnValueOnce();

            const httpClient = new FakeHTTPClient();
            jest.spyOn(httpClient, 'post').mockResolvedValueOnce({
                status: 200,
                data: { errorCode: 10, errorDesc: description, status: DHLITResponseStatus.FAIL },
            });

            const client = new DHL_ITClientImpl({ httpClient, logger, env });
            await client.sendCustomerDropOffStatusUpdate({
                product: {
                    productId,
                    product: productType,
                },
                agentId,
                deviceId,
                fromNode: nodeId,
                customerEmail,
                time,
            });

            expect(logger.error).toHaveBeenCalledTimes(1);
            expect(logger.error).toHaveBeenCalledWith({
                message: 'Error sending drop off status update',
                error: { status, code, description },
            });
        });
    });

    describe('fetchShipmentInfoForCourierDropoff', () => {
        const shipmentId = 'product-123456789';

        const httpClient = new FakeHTTPClient();

        afterEach(() => {
            jest.clearAllMocks();
        });

        it('should fetch information about a shipment for a courier drop off', async () => {
            jest.spyOn(httpClient, 'post').mockResolvedValueOnce({
                status: 200,
                data: {
                    status: DHLITResponseStatus.SUCCESS,
                    productId: shipmentId,
                    errorCode: 0,
                    height: 10,
                    width: 10,
                    depth: 10,
                    weight: 10,
                },
            });

            const client = new DHL_ITClientImpl({ httpClient, logger, env });
            const response = await client.fetchShipmentInfoForCourierDropoff({
                agentId,
                shipmentId,
                nodeId,
            });

            expect(response.status).toBe(DHLITResponseStatus.SUCCESS);
            expect(httpClient.post).toHaveBeenCalledTimes(1);
            expect(httpClient.post).toHaveBeenCalledWith(
                expect.objectContaining({
                    headers,
                    url: `${baseURL}/shipment/acquire`,
                    data: {
                        agentId,
                        channel: 'APT',
                        details: 'T',
                        code: shipmentId,
                        fromNode: nodeId,
                    },
                }),
            );
        });

        it('should correctly map INTERNAL_ERROR to a meaningful error', async () => {
            jest.spyOn(httpClient, 'post').mockResolvedValueOnce({
                status: 200,
                data: {
                    status: DHLITResponseStatus.FAIL,
                    errorCode: DHLITAcquireShipmentErrorCode.INTERNAL_ERROR,
                },
            });

            const client = new DHL_ITClientImpl({ httpClient, logger, env });

            await expect(
                client.fetchShipmentInfoForCourierDropoff({ agentId, shipmentId, nodeId }),
            ).rejects.toThrow('internal error (1)');

            expect(httpClient.post).toHaveBeenCalledTimes(1);
            expect(httpClient.post).toHaveBeenCalledWith(
                expect.objectContaining({
                    headers,
                    url: `${baseURL}/shipment/acquire`,
                    data: {
                        agentId,
                        channel: 'APT',
                        details: 'T',
                        code: shipmentId,
                        fromNode: nodeId,
                    },
                }),
            );
        });

        it('should correctly map INVALID_CODE to a meaningful error', async () => {
            jest.spyOn(httpClient, 'post').mockResolvedValueOnce({
                status: 200,
                data: {
                    status: DHLITResponseStatus.FAIL,
                    errorCode: DHLITAcquireShipmentErrorCode.INVALID_CODE,
                },
            });

            const client = new DHL_ITClientImpl({ httpClient, logger, env });

            await expect(
                client.fetchShipmentInfoForCourierDropoff({ agentId, shipmentId, nodeId }),
            ).rejects.toThrow('invalid code (2)');

            expect(httpClient.post).toHaveBeenCalledTimes(1);
            expect(httpClient.post).toHaveBeenCalledWith(
                expect.objectContaining({
                    headers,
                    url: `${baseURL}/shipment/acquire`,
                    data: {
                        agentId,
                        channel: 'APT',
                        details: 'T',
                        code: shipmentId,
                        fromNode: nodeId,
                    },
                }),
            );
        });

        it('should correctly map NON_EXISTENT_CODE to a meaningful error', async () => {
            jest.spyOn(httpClient, 'post').mockResolvedValueOnce({
                status: 200,
                data: {
                    status: DHLITResponseStatus.FAIL,
                    errorCode: DHLITAcquireShipmentErrorCode.NON_EXISTENT_CODE,
                },
            });

            const client = new DHL_ITClientImpl({ httpClient, logger, env });

            await expect(
                client.fetchShipmentInfoForCourierDropoff({ agentId, shipmentId, nodeId }),
            ).rejects.toThrow('nonexistent code (3)');

            expect(httpClient.post).toHaveBeenCalledTimes(1);
            expect(httpClient.post).toHaveBeenCalledWith(
                expect.objectContaining({
                    headers,
                    url: `${baseURL}/shipment/acquire`,
                    data: {
                        agentId,
                        channel: 'APT',
                        details: 'T',
                        code: shipmentId,
                        fromNode: nodeId,
                    },
                }),
            );
        });

        it('should correctly map IMPOSSIBLE_OPERATION to a meaningful error', async () => {
            jest.spyOn(httpClient, 'post').mockResolvedValueOnce({
                status: 200,
                data: {
                    status: DHLITResponseStatus.FAIL,
                    errorCode: DHLITAcquireShipmentErrorCode.IMPOSSIBLE_OPERATION,
                },
            });

            const client = new DHL_ITClientImpl({ httpClient, logger, env });

            await expect(
                client.fetchShipmentInfoForCourierDropoff({ agentId, shipmentId, nodeId }),
            ).rejects.toThrow('impossible operation (4)');

            expect(httpClient.post).toHaveBeenCalledTimes(1);
            expect(httpClient.post).toHaveBeenCalledWith(
                expect.objectContaining({
                    headers,
                    url: `${baseURL}/shipment/acquire`,
                    data: {
                        agentId,
                        channel: 'APT',
                        details: 'T',
                        code: shipmentId,
                        fromNode: nodeId,
                    },
                }),
            );
        });

        it('should correctly map CHANNEL_NOT_ENABLED_FOR_REQUEST to a meaningful error', async () => {
            jest.spyOn(httpClient, 'post').mockResolvedValueOnce({
                status: 200,
                data: {
                    status: DHLITResponseStatus.FAIL,
                    errorCode: DHLITAcquireShipmentErrorCode.CHANNEL_NOT_ENABLED_FOR_REQUEST,
                },
            });

            const client = new DHL_ITClientImpl({ httpClient, logger, env });

            await expect(
                client.fetchShipmentInfoForCourierDropoff({ agentId, shipmentId, nodeId }),
            ).rejects.toThrow('channel not enabled for request (5)');

            expect(httpClient.post).toHaveBeenCalledTimes(1);
            expect(httpClient.post).toHaveBeenCalledWith(
                expect.objectContaining({
                    headers,
                    url: `${baseURL}/shipment/acquire`,
                    data: {
                        agentId,
                        channel: 'APT',
                        details: 'T',
                        code: shipmentId,
                        fromNode: nodeId,
                    },
                }),
            );
        });

        it('should correctly map INVALID_FRAZIONARIO_CODE to a meaningful error', async () => {
            jest.spyOn(httpClient, 'post').mockResolvedValueOnce({
                status: 200,
                data: {
                    status: DHLITResponseStatus.FAIL,
                    errorCode: DHLITAcquireShipmentErrorCode.INVALID_FRAZIONARIO_CODE,
                },
            });

            const client = new DHL_ITClientImpl({ httpClient, logger, env });

            await expect(
                client.fetchShipmentInfoForCourierDropoff({ agentId, shipmentId, nodeId }),
            ).rejects.toThrow('invalid frazionario code (6)');

            expect(httpClient.post).toHaveBeenCalledTimes(1);
            expect(httpClient.post).toHaveBeenCalledWith(
                expect.objectContaining({
                    headers,
                    url: `${baseURL}/shipment/acquire`,
                    data: {
                        agentId,
                        channel: 'APT',
                        details: 'T',
                        code: shipmentId,
                        fromNode: nodeId,
                    },
                }),
            );
        });

        it('should correctly map PRODUCT_NOT_ENABLED_FOR_SERVICE to a meaningful error', async () => {
            jest.spyOn(httpClient, 'post').mockResolvedValueOnce({
                status: 200,
                data: {
                    status: DHLITResponseStatus.FAIL,
                    errorCode: DHLITAcquireShipmentErrorCode.PRODUCT_NOT_ENABLED_FOR_SERVICE,
                },
            });

            const client = new DHL_ITClientImpl({ httpClient, logger, env });

            await expect(
                client.fetchShipmentInfoForCourierDropoff({ agentId, shipmentId, nodeId }),
            ).rejects.toThrow('product not enabled for service (7)');

            expect(httpClient.post).toHaveBeenCalledTimes(1);
            expect(httpClient.post).toHaveBeenCalledWith(
                expect.objectContaining({
                    headers,
                    url: `${baseURL}/shipment/acquire`,
                    data: {
                        agentId,
                        channel: 'APT',
                        details: 'T',
                        code: shipmentId,
                        fromNode: nodeId,
                    },
                }),
            );
        });

        it('should correctly map UNKNOWN_PARCEL to a meaningful error', async () => {
            jest.spyOn(httpClient, 'post').mockResolvedValueOnce({
                status: 200,
                data: {
                    status: DHLITResponseStatus.FAIL,
                    errorCode: DHLITAcquireShipmentErrorCode.UNKNOWN_PARCEL,
                },
            });

            const client = new DHL_ITClientImpl({ httpClient, logger, env });

            await expect(
                client.fetchShipmentInfoForCourierDropoff({ agentId, shipmentId, nodeId }),
            ).rejects.toThrow('unknown parcel (8)');

            expect(httpClient.post).toHaveBeenCalledTimes(1);
            expect(httpClient.post).toHaveBeenCalledWith(
                expect.objectContaining({
                    headers,
                    url: `${baseURL}/shipment/acquire`,
                    data: {
                        agentId,
                        channel: 'APT',
                        details: 'T',
                        code: shipmentId,
                        fromNode: nodeId,
                    },
                }),
            );
        });

        it('should map unexpected error codes', async () => {
            jest.spyOn(httpClient, 'post').mockResolvedValueOnce({
                status: 200,
                data: { status: DHLITResponseStatus.FAIL, errorCode: 15 },
            });

            const client = new DHL_ITClientImpl({ httpClient, logger, env });

            await expect(
                client.fetchShipmentInfoForCourierDropoff({ agentId, shipmentId, nodeId }),
            ).rejects.toThrow('unknown error code (15)');

            expect(httpClient.post).toHaveBeenCalledTimes(1);
            expect(httpClient.post).toHaveBeenCalledWith(
                expect.objectContaining({
                    headers,
                    url: `${baseURL}/shipment/acquire`,
                    data: {
                        agentId,
                        channel: 'APT',
                        details: 'T',
                        code: shipmentId,
                        fromNode: nodeId,
                    },
                }),
            );
        });
    });

    describe('notifyCustomerPickUp', () => {
        const fixedDHLArgs = { channel: 'APT', delivered: 'C' };

        it('should notify DHL IT about a customer shipment pick up', async () => {
            const httpClient = new FakeHTTPClient();
            jest.spyOn(httpClient, 'post').mockResolvedValueOnce({
                status: 200,
                data: { status: DHLITResponseStatus.SUCCESS, errorCode: 0 },
            });

            const client = new DHL_ITClientImpl({ httpClient, logger, env });
            await client.notifyCustomerPickUp({
                product: { productId, product: productType },
                nodeId,
                agentId,
                time,
            });

            expect(httpClient.post).toHaveBeenCalledTimes(1);
            expect(httpClient.post).toHaveBeenCalledWith({
                headers,
                url: `${baseURL}/customer/pickUpStatusUpdate`,
                data: {
                    ...fixedDHLArgs,
                    productId,
                    product: productType,
                    time,
                    agentId,
                    fromNode: nodeId,
                },
            });
        });

        it('should log an error if custom status response is not Success', async () => {
            const status = DHLITResponseStatus.FAIL;
            const code = 10;
            const description = 'DHL error';

            const httpClient = new FakeHTTPClient();
            jest.spyOn(httpClient, 'post').mockResolvedValueOnce({
                status: 200,
                data: { status, errorCode: code, errorDesc: description },
            });

            jest.spyOn(logger, 'error').mockReturnValueOnce();

            const client = new DHL_ITClientImpl({ httpClient, logger, env });
            await client.notifyCustomerPickUp({
                product: { productId, product: productType },
                nodeId,
                agentId,
                time,
            });

            expect(logger.error).toHaveBeenCalledTimes(1);
            expect(logger.error).toHaveBeenCalledWith({
                message: 'Error sending customer pickup',
                error: { status, code, description },
            });
        });
    });

    describe('notifyCourierShipmentDropOff', () => {
        afterEach(() => jest.clearAllMocks());

        it('should notify DHL IT about a drop off performed by a courier', async () => {
            const httpClient = new FakeHTTPClient();
            jest.spyOn(httpClient, 'post').mockResolvedValueOnce({
                status: 200,
                data: { status: DHLITResponseStatus.SUCCESS, errorCode: 0 },
            });

            const client = new DHL_ITClientImpl({ httpClient, logger, env });

            const pin = '1234';
            const time = new Date();
            const args = {
                nodeId,
                agentId,
                product: {
                    productId,
                    product: productType,
                },
                pin,
                time,
            };
            await client.notifyCourierShipmentDropOff(args);

            const product = { productId, pin, qrcode: pin, product: productType, traceInfo: 'CP0' };
            const containers = [{ products: [product] }];
            const requestData = { agentId, time, containers, fromNode: nodeId, channel: 'APT' };

            expect(httpClient.post).toHaveBeenCalledTimes(1);
            expect(httpClient.post).toHaveBeenCalledWith({
                headers,
                url: `${baseURL}/shipment/check`,
                data: requestData,
            });
        });

        it('should log an error if custom status response is not Success', async () => {
            const status = DHLITResponseStatus.FAIL;
            const code = 10;
            const description = 'DHL error';

            const httpClient = new FakeHTTPClient();
            jest.spyOn(httpClient, 'post').mockResolvedValueOnce({
                status: 200,
                data: { status, errorCode: code, errorDesc: description },
            });

            jest.spyOn(logger, 'error').mockReturnValueOnce();

            const client = new DHL_ITClientImpl({ httpClient, logger, env });
            await client.notifyCourierShipmentDropOff({
                nodeId,
                agentId,
                product: { productId, product: productType },
                pin: '1234',
                time: new Date(),
            });

            expect(logger.error).toHaveBeenCalledTimes(1);
            expect(logger.error).toHaveBeenCalledWith({
                message: 'Error sending courier shipment dropoff',
                error: { status, code, description },
            });
        });
    });

    describe('authenticateCourier', () => {
        const pin = '1234';
        const sessionId = 'courier-session-id-1234';

        const httpClient = new FakeHTTPClient();
        const client = new DHL_ITClientImpl({ httpClient, logger, env });

        afterEach(() => {
            jest.clearAllMocks();
        });

        it('should throw an error if user is not authorized to perform the operation', async () => {
            jest.spyOn(httpClient, 'post').mockResolvedValueOnce({ status: 401, data: {} });

            await expect(
                client.authenticateCourier({ nodeId, pin, courierSessionId: sessionId }),
            ).rejects.toThrow(
                `The courier with session '${sessionId}' is not authorized to proceed with this operation using the PIN '${pin}'`,
            );

            expect(httpClient.post).toHaveBeenCalledTimes(1);
            expect(httpClient.post).toHaveBeenCalledWith(
                expect.objectContaining({
                    headers,
                    url: `${baseURL}/pins/verify`,
                    data: { pin, sessionId, fromNode: nodeId },
                }),
            );
        });

        it('should throw an error if user is forbidden to perform the operation', async () => {
            const hash = 'hash-1234';
            jest.spyOn(httpClient, 'post').mockResolvedValueOnce({
                status: 403,
                data: { userId_hash: hash },
            });

            await expect(
                client.authenticateCourier({ nodeId, pin, courierSessionId: sessionId }),
            ).rejects.toThrow(
                `The courier with session '${sessionId}' (hash: ${hash}) is forbidden to proceed with this operation using the PIN '1234'`,
            );

            expect(httpClient.post).toHaveBeenCalledTimes(1);
            expect(httpClient.post).toHaveBeenCalledWith(
                expect.objectContaining({
                    headers,
                    url: `${baseURL}/pins/verify`,
                    data: { pin, sessionId, fromNode: nodeId },
                }),
            );
        });

        it('should authenticate a courier', async () => {
            jest.spyOn(httpClient, 'post').mockResolvedValueOnce({
                status: 200,
                data: { status: DHLITResponseStatus.SUCCESS, errorCode: 0 },
            });

            await client.authenticateCourier({ nodeId, pin, courierSessionId: sessionId });

            expect(httpClient.post).toHaveBeenCalledTimes(1);
            expect(httpClient.post).toHaveBeenCalledWith(
                expect.objectContaining({
                    headers,
                    url: `${baseURL}/pins/verify`,
                    data: { pin, sessionId, fromNode: nodeId },
                }),
            );
        });
    });

    describe('notifyExpiredParcels', () => {
        const nodeId1 = 'partner-bloq-id-123';
        const nodeId2 = 'partner-bloq-id-456';
        const productId1 = 'product-123456789';
        const productId2 = 'product-987654321';
        const productId3 = 'product-111111111';

        const httpClient = new FakeHTTPClient();
        const client = new DHL_ITClientImpl({ httpClient, logger, env });

        afterEach(() => {
            jest.clearAllMocks();
        });

        it('should notify DHL IT about expired parcels', async () => {
            jest.spyOn(httpClient, 'post').mockResolvedValueOnce({
                status: 200,
                data: { status: DHLITResponseStatus.SUCCESS, errorCode: 0 },
            });

            await client.notifyExpiredParcels({
                parcels: [
                    { productType, productId: productId1, nodeId: nodeId1 },
                    { productType, productId: productId2, nodeId: nodeId1 },
                    { productType, productId: productId3, nodeId: nodeId2 },
                ],
            });

            expect(httpClient.post).toHaveBeenCalledTimes(1);
            expect(httpClient.post).toHaveBeenCalledWith({
                headers,
                url: `${baseURL}/parcel/notify`,
                data: {
                    channel: 'APT',
                    agentId: 'DHL',
                    products: [
                        {
                            productId: productId1,
                            product: productType,
                            fromNode: nodeId1,
                            status: 'EXP',
                        },
                        {
                            productId: productId2,
                            product: productType,
                            fromNode: nodeId1,
                            status: 'EXP',
                        },
                        {
                            productId: productId3,
                            product: productType,
                            fromNode: nodeId2,
                            status: 'EXP',
                        },
                    ],
                },
            });
        });

        it('should log an error if custom responde is not "Success"', async () => {
            const status = DHLITResponseStatus.FAIL;
            const code = 1;
            const description = 'DHL error';

            jest.spyOn(httpClient, 'post').mockResolvedValueOnce({
                status: 200,
                data: { status, errorCode: code, errorDesc: description },
            });

            jest.spyOn(logger, 'error').mockReturnValueOnce();

            await client.notifyExpiredParcels({
                parcels: [
                    { productType, productId: productId1, nodeId: nodeId1 },
                    { productType, productId: productId2, nodeId: nodeId1 },
                    { productType, productId: productId3, nodeId: nodeId2 },
                ],
            });

            expect(logger.error).toHaveBeenCalledTimes(1);
            expect(logger.error).toHaveBeenCalledWith({
                message: 'Error notifying expired parcels',
                error: { status, code, description },
            });
        });
    });
});
