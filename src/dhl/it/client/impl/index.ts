import {
    type DHLITShipment,
    type DHLIT<PERSON>lient,
    type FetchShipmentForDropoffRequest,
    type DHLITDataResponse,
    type DHLITCourierShipment,
    type DHLITShipmentProduct,
    DHLITResponseStatus,
    DHLITAcquireShipmentErrorCode,
} from '..';

import { type HTTPClient } from '../../../../core/http-client';
import { type ApplicationEnvironment } from '../../../../core/env';
import * as httpCodes from '../../../../http/models/codes';
import { type Product } from '../../models';
import { type Logger } from '../../../../core/logger';
import { StatusValidationStrategy } from '../../../../core/http-client/axios';

export class DHL_ITClientImpl implements DHLITClient {
    protected readonly env: ApplicationEnvironment;
    private readonly httpClient: HTTPClient;
    private readonly authClientId: string;
    private readonly authClientSecret: string;
    private readonly logger: Logger;

    constructor(deps: { httpClient: HTTPClient; logger: Logger; env: ApplicationEnvironment }) {
        this.env = deps.env;
        this.logger = deps.logger;
        this.httpClient = deps.httpClient;
        this.authClientId = deps.env.DHL_IT_AUTH_CLIENT_ID;
        this.authClientSecret = deps.env.DHL_IT_AUTH_CLIENT_SECRET;

        this.createProductsOnPartnerSide = this.createProductsOnPartnerSide.bind(this);
        this.sendCustomerDropOffStatusUpdate = this.sendCustomerDropOffStatusUpdate.bind(this);
        this.notifyCustomerPickUp = this.notifyCustomerPickUp.bind(this);
        this.notifyCourierShipmentDropOff = this.notifyCourierShipmentDropOff.bind(this);
        this.authenticateCourier = this.authenticateCourier.bind(this);

        this.fetchShipmentInfoForCustomerDropoff =
            this.fetchShipmentInfoForCustomerDropoff.bind(this);

        this.fetchShipmentInfoForCourierDropoff =
            this.fetchShipmentInfoForCourierDropoff.bind(this);

        this.resolveStatoldvFor = this.resolveStatoldvFor.bind(this);
        this.mapToDHLITProduct = this.mapToDHLITProduct.bind(this);
    }

    get baseURL(): string {
        return this.env.DHL_IT_API_HOST;
    }

    async createProductsOnPartnerSide(args: {
        nodeId: string;
        agentId: string;
        time: Date;
        products: Product[];
    }): Promise<void> {
        const { nodeId, agentId, time, products } = args;

        const response = await this.httpClient.post<DHLITDataResponse>({
            url: `${this.baseURL}/shipment/create`,
            headers: this.createRequestHeaders(),
            data: {
                time,
                agentId,
                fromNode: nodeId,
                channel: 'APT',
                products: products.map(p => this.mapToDHLITProduct(p)),
            },
        });

        if (response.data.status !== DHLITResponseStatus.SUCCESS) {
            throw new Error('Error creating rent on partner side');
        }
    }

    private mapToDHLITProduct(p: Product): Product & { statoldv: string } {
        const { productId, product } = p;
        return { productId, product, statoldv: this.resolveStatoldvFor(p) };
    }

    private resolveStatoldvFor(product: Product): string {
        if (product.isMissing === true) return 'MAN';
        if (product.isExpired === true) return 'CGI';
        return 'ACC';
    }

    async fetchShipmentInfoForCustomerDropoff(args: {
        productId: string;
        agentId: string;
        nodeId: string;
    }): Promise<DHLITShipment> {
        const { productId, agentId, nodeId } = args;

        const requestData: FetchShipmentForDropoffRequest = {
            productId,
            agentId,
            channel: 'APT',
            fromNode: nodeId,
        };

        const response = await this.httpClient.post<DHLITShipment & { moreInformation?: string }>({
            url: `${this.baseURL}/customer/dropOff`,
            headers: this.createRequestHeaders(),
            data: requestData,
            validateStatus: StatusValidationStrategy.ACCEPT_ALL,
        });

        if (response.data.status !== DHLITResponseStatus.SUCCESS) {
            throw new Error(
                `Error fetching shipment info for customer drop off: ${
                    response.data.moreInformation ?? ''
                }`,
            );
        }

        return response.data;
    }

    async sendCustomerDropOffStatusUpdate(args: {
        product: Product;
        agentId: string;
        fromNode: string;
        deviceId?: string;
        customerEmail: string;
        time: Date;
    }): Promise<void> {
        const { product, agentId, fromNode, deviceId, customerEmail, time } = args;

        const requestData = {
            productId: product.productId,
            fromNode,
            agentId,
            deviceId,
            channel: 'APT',
            product: product.product,
            time,
            senderEmail: customerEmail,
        };

        const response = await this.httpClient.post<DHLITDataResponse>({
            url: `${this.baseURL}/customer/dropOffStatusUpdate`,
            headers: this.createRequestHeaders(),
            data: requestData,
        });

        this.logIfCustomError('Error sending drop off status update', response.data);
    }

    async fetchShipmentInfoForCourierDropoff(args: {
        agentId: string;
        shipmentId: string;
        nodeId?: string;
    }): Promise<DHLITCourierShipment> {
        const { agentId, nodeId, shipmentId } = args;
        const requestData = {
            agentId,
            channel: 'APT',
            details: 'T',
            code: shipmentId,
            fromNode: nodeId,
        };

        const response = await this.httpClient.post<DHLITCourierShipment>({
            url: `${this.baseURL}/shipment/acquire`,
            headers: this.createRequestHeaders(),
            data: requestData,
            validateStatus: StatusValidationStrategy.ACCEPT_ALL,
        });

        if (response.data.status !== DHLITResponseStatus.SUCCESS) {
            const { status, errorCode, errorDesc } = response.data;
            this.logger.error({
                message: `Error fetching shipment info for courier drop off`,
                error: { status, errorCode, errorDesc },
            });

            throw new Error(this.mapAcquireShipmentErrorCodeToMsg(response.data.errorCode));
        }

        return response.data;
    }

    mapAcquireShipmentErrorCodeToMsg(errorCode: DHLITAcquireShipmentErrorCode): string {
        const errStr = errorCode.toString();

        switch (errorCode) {
            case DHLITAcquireShipmentErrorCode.INTERNAL_ERROR:
                return `internal error (${errStr})`;
            case DHLITAcquireShipmentErrorCode.INVALID_CODE:
                return `invalid code (${errStr})`;
            case DHLITAcquireShipmentErrorCode.NON_EXISTENT_CODE:
                return `nonexistent code (${errStr})`;
            case DHLITAcquireShipmentErrorCode.IMPOSSIBLE_OPERATION:
                return `impossible operation (${errStr})`;
            case DHLITAcquireShipmentErrorCode.CHANNEL_NOT_ENABLED_FOR_REQUEST:
                return `channel not enabled for request (${errStr})`;
            case DHLITAcquireShipmentErrorCode.INVALID_FRAZIONARIO_CODE:
                return `invalid frazionario code (${errStr})`;
            case DHLITAcquireShipmentErrorCode.PRODUCT_NOT_ENABLED_FOR_SERVICE:
                return `product not enabled for service (${errStr})`;
            case DHLITAcquireShipmentErrorCode.UNKNOWN_PARCEL:
                return `unknown parcel (${errStr})`;
            default:
                return `unknown error code (${errStr})`;
        }
    }

    async notifyCustomerPickUp(args: {
        product: Product;
        nodeId: string;
        agentId: string;
        time: Date;
    }): Promise<void> {
        const { product, nodeId, agentId, time } = args;
        const requestData = {
            productId: product.productId,
            agentId,
            time,
            fromNode: nodeId,
            product: product.product,
            channel: 'APT',
            delivered: 'C',
        };

        const response = await this.httpClient.post<DHLITDataResponse>({
            url: `${this.baseURL}/customer/pickUpStatusUpdate`,
            headers: this.createRequestHeaders(),
            data: requestData,
        });

        this.logIfCustomError('Error sending customer pickup', response.data);
    }

    async notifyCourierShipmentDropOff(args: {
        nodeId: string;
        agentId: string;
        product: Product;
        pin: string;
        time: Date;
    }): Promise<void> {
        const { nodeId, agentId, product, pin, time } = args;

        const shipmentProduct: DHLITShipmentProduct = {
            productId: product.productId,
            product: product.product,
            pin,
            qrcode: pin,
            traceInfo: 'CP0',
        };
        const containers = [{ products: [shipmentProduct] }];
        const requestData = { agentId, time, containers, fromNode: nodeId, channel: 'APT' };

        const response = await this.httpClient.post<DHLITDataResponse>({
            url: `${this.baseURL}/shipment/check`,
            headers: this.createRequestHeaders(),
            data: requestData,
        });

        this.logIfCustomError('Error sending courier shipment dropoff', response.data);
    }

    private createRequestHeaders(): Record<string, string> {
        return {
            'X-IBM-Client-Id': this.authClientId,
            'X-IBM-Client-Secret': this.authClientSecret,
        };
    }

    async authenticateCourier(args: {
        nodeId: string;
        pin: string;
        courierSessionId: string;
    }): Promise<string> {
        const { nodeId, pin, courierSessionId } = args;

        const response = await this.httpClient.post<{
            status: DHLITResponseStatus;
            userId_hash: string;
        }>({
            url: `${this.baseURL}/pins/verify`,
            headers: this.createRequestHeaders(),
            data: { fromNode: nodeId, pin, sessionId: courierSessionId },
            validateStatus: StatusValidationStrategy.ACCEPT_ALL,
        });

        const hash = response.data.userId_hash;

        if (response.status === httpCodes.UNAUTHORIZED) {
            throw new Error(
                `The courier with session '${courierSessionId}' is not authorized to proceed with this operation using the PIN '${pin}'`,
            );
        }

        if (response.status === httpCodes.FORBIDDEN) {
            throw new Error(
                `The courier with session '${courierSessionId}' (hash: ${hash}) is forbidden to proceed with this operation using the PIN '${pin}'`,
            );
        }

        return response.data.userId_hash;
    }

    async notifyExpiredParcels(args: {
        parcels: Array<{ productId: string; nodeId: string; productType: string }>;
    }): Promise<void> {
        const { parcels } = args;

        const response = await this.httpClient.post<DHLITDataResponse>({
            url: `${this.baseURL}/parcel/notify`,
            headers: this.createRequestHeaders(),
            data: {
                channel: 'APT',
                agentId: 'DHL',
                products: parcels.map(({ nodeId, productId, productType }) => ({
                    productId,
                    status: 'EXP',
                    fromNode: nodeId,
                    product: productType,
                })),
            },
        });

        this.logIfCustomError('Error notifying expired parcels', response.data);
    }

    private logIfCustomError(message: string, response: DHLITDataResponse): void {
        if (response.status !== DHLITResponseStatus.SUCCESS) {
            const { status, errorCode: code, errorDesc: description } = response;
            this.logger.error({ message, error: { status, code, description } });
        }
    }
}
