export interface Product {
    productId: string;
    product: string;
    isMissing?: boolean;
    isExpired?: boolean;
}

export interface CourierPickupSession {
    sessionId: string;
    courierId: string;
    products: Product[];
    createdAt?: Date;
}

export interface ExpiredRent {
    rentId: string;
    partnerFriendlyId: string;
    metadata: any;
}

export interface ExpiredRentsNotificationBatch {
    id?: string;
    failureCount: number;
    createdAt?: Date;
    rentIds: string[];
}
