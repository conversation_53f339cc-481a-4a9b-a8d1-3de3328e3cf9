import Axios from 'axios';
import express, { type RequestHandler } from 'express';
import * as jwtClient from 'jsonwebtoken';

import * as Bloqit from '../bloqit/http';
import * as DHL_CZ from '../dhl/cz/http';
import * as InPost from '../inpost/http';
import * as GenericRequest from '../http/generic-request';

import { DHLCZHandlerImpl } from '../dhl/cz/core/handler';

import { PartnerRegistryImpl } from '../core/partner-registry/impl';
import { PartnerIdentificationMiddleware } from './middleware/partner-identification';
import { DHL_CZClientImpl } from '../dhl/cz/client/impl';
import { AxiosHTTPClient, StatusValidationStrategy } from '../core/http-client/axios';
import { IdMappingRepositoryImpl } from '../core/identifier-mapping/repository/impl';
import * as DocumentDatabase from '../core/data-access/document/loader';
import type * as ENV from '../core/env';
import { type DocumentDatabaseImplementationStrategy } from '../core/data-access/document';
import { WinstonLogger } from '../core/logger/winston';
import * as ManagedWinston from '../core/logger/winston/setup';
import { AxiosInterceptor } from '../core/http-client/axios/interceptor';
import { DHLITHandlerImpl } from '../dhl/it/core/handler';
import { DHL_ITClientImpl } from '../dhl/it/client/impl';
import { DHL_ITRemotelyMockedClientImpl } from '../dhl/it/client/remotely-mocked';
import { CourierPickupSessionsRepositoryImpl } from '../dhl/it/data-access/repositories/courier-pickup-session/impl';
import { ExpiredRentsRepositoryImpl } from '../dhl/it/data-access/repositories/expired-rents/impl';
import { ExpiredRentsNotificationBatchRepositoryImpl } from '../dhl/it/data-access/repositories/expired-rents-notification-batch/impl';
import { DHL_CZRemotelyMockedClientImpl as DHLCZRemotelyMockedClientImpl } from '../dhl/cz/client/remotely-mocked';
import { GenericAuthenticationMiddleware } from './middleware/generic-authentication';
import { InPostApiClientImpl } from '../inpost/client/impl';
import { InpostHandlerImpl } from '../inpost/handler';
import { BloqitClientImpl } from '../bloqit/sdk/impl';
import { LabelAssociationRepositoryImpl } from '../inpost/data-access/repositories/associate-label/impl/label-association-repository-impl';
import { MappedLockerRepositoryImpl } from '../inpost/data-access/repositories/mapped-locker/impl/mapped-locker-repository-impl';

export async function setup(args: {
    env: ENV.ApplicationEnvironment;
}): Promise<Express.Application> {
    const { env } = args;

    const app = express();

    app.use(express.json());
    app.use(express.urlencoded({ extended: true }));

    const { applicationLogger, reqResLogger, errorLogger } = ManagedWinston.setup({ env });
    const logger = new WinstonLogger({ winstonInstance: applicationLogger });
    const partnerRegistry = new PartnerRegistryImpl();

    const {
        idMappingCollection,
        courierPickupSessionsCollection,
        expiredRentsCollection,
        expiredRentsNotificationBatchCollection,
        labelAssociationCollection,
        mappedLockersCollection,
    } = await DocumentDatabase.setup({
        logger,
        dbURL: env.MONGO_DB_URL,
        dbName: env.MONGO_DB_NAME,
        strategy: env.DOCUMENT_DATABASE_STRATEGY as DocumentDatabaseImplementationStrategy,
    });

    const idMappingRepository = new IdMappingRepositoryImpl({ idMappingCollection, logger });

    const axiosClient = Axios.create({ timeout: env.GLOBAL_HTTP_CLIENT_TIMEOUT_MS });
    const axiosInterceptor = new AxiosInterceptor({ logger });

    axiosClient.interceptors.request.use(
        axiosInterceptor.interceptRequest.bind(axiosInterceptor),
        axiosInterceptor.interceptError.bind(axiosInterceptor),
    );

    axiosClient.interceptors.response.use(
        axiosInterceptor.interceptResponse.bind(axiosInterceptor),
        axiosInterceptor.interceptError.bind(axiosInterceptor),
    );

    const availableEnv = ['test', 'dev'];
    if (availableEnv.includes(env.NODE_ENV)) {
        const genericAuthMiddleware = new GenericAuthenticationMiddleware({
            fakeApiKey: env.GENERIC_API_KEY ?? 'GeNEriCBl0qM1Ddl3war6',
        });

        const genericHttpClient = new AxiosHTTPClient({
            axiosClient,
        });

        app.use(
            '/genericCall',
            genericAuthMiddleware.authenticate.bind(
                genericAuthMiddleware,
            ) as unknown as RequestHandler,
            GenericRequest.setup({
                logger,
                httpClient: genericHttpClient,
                router: express.Router(),
                env,
            }),
        );
    }

    const dhlCZHttpClient = new AxiosHTTPClient({
        axiosClient,
        validateStatusFn: StatusValidationStrategy.TWO_XX_RANGE_ONLY,
    });

    const dhlCZClient = new DHL_CZClientImpl({ env, httpClient: dhlCZHttpClient });

    partnerRegistry.register(
        env.DHL_CZ_PARTNER_ID,
        new DHLCZHandlerImpl({ dhlCZClient, idMappingRepository, logger }),
    );

    const dhlCZMockPartnerId = env.DHL_CZ_MOCK_PARTNER_ID;
    if (dhlCZMockPartnerId !== undefined) {
        const remotelyMockedCZClient = new DHLCZRemotelyMockedClientImpl({
            env,
            httpClient: dhlCZHttpClient,
        });

        partnerRegistry.register(
            dhlCZMockPartnerId,
            new DHLCZHandlerImpl({
                idMappingRepository,
                logger,
                dhlCZClient: remotelyMockedCZClient,
            }),
        );
    }

    const inpostHttpClient = new AxiosHTTPClient({
        axiosClient,
        validateStatusFn: StatusValidationStrategy.TWO_XX_RANGE_ONLY,
    });

    const inpostHandlerHttpClient = new AxiosHTTPClient({
        axiosClient,
        validateStatusFn: StatusValidationStrategy.TWO_XX_RANGE_ONLY,
    });

    const labelAssociationRepository = new LabelAssociationRepositoryImpl({
        collection: labelAssociationCollection,
    });

    const mappedLockerRepository = new MappedLockerRepositoryImpl({
        collection: mappedLockersCollection,
    });

    const inpostClient = new InPostApiClientImpl({ httpClient: inpostHttpClient, env });
    const bloqitClient = new BloqitClientImpl({
        httpClient: inpostHandlerHttpClient,
        logger,
        apiKey: env.INPOST_BLOQIT_API_KEY,
    });
    partnerRegistry.register(
        env.INPOST_PARTNER_ID,
        new InpostHandlerImpl({
            logger,
            inpostClient,
            bloqitClient,
            labelAssociationRepository,
            mappedLockerRepository,
        }),
    );

    const courierPickupSessionsRepository = new CourierPickupSessionsRepositoryImpl({
        collection: courierPickupSessionsCollection,
    });

    const expiredRentsRepository = new ExpiredRentsRepositoryImpl({
        collection: expiredRentsCollection,
    });

    const expiredRentsNotificationBatchRepository = new ExpiredRentsNotificationBatchRepositoryImpl(
        { collection: expiredRentsNotificationBatchCollection },
    );

    const dhlITHttpClient = new AxiosHTTPClient({
        axiosClient,
        validateStatusFn: StatusValidationStrategy.TWO_XX_RANGE_ONLY,
    });

    const dhlITClient = new DHL_ITClientImpl({ httpClient: dhlITHttpClient, logger, env });
    partnerRegistry.register(
        env.DHL_IT_PARTNER_ID,
        new DHLITHandlerImpl({
            dhlITClient,
            expiredRentsRepository,
            courierPickupSessionsRepository,
            expiredRentsNotificationBatchRepository,
            env,
            logger,
        }),
    );

    const dhlITMockPartnerId = env.DHL_IT_MOCK_PARTNER_ID;
    if (dhlITMockPartnerId !== undefined) {
        const remotelyMockedITClient = new DHL_ITRemotelyMockedClientImpl({
            env,
            logger,
            httpClient: dhlITHttpClient,
        });

        partnerRegistry.register(
            dhlITMockPartnerId,
            new DHLITHandlerImpl({
                env,
                logger,
                expiredRentsRepository,
                courierPickupSessionsRepository,
                expiredRentsNotificationBatchRepository,
                dhlITClient: remotelyMockedITClient,
            }),
        );
    }

    const partnerIdentificationMiddleware = new PartnerIdentificationMiddleware({
        jwtClient,
        partnerSecrets: {
            bloqit: { apiKeys: [env.BLOQIT_CORE_MIDDLEWARE_API_KEY] },
            dhl_cz: { apiKeys: [env.DHL_CZ_MIDDLEWARE_API_KEY] },
            dhl_it: { apiKeys: [env.DHL_IT_MIDDLEWARE_API_KEY] },
            inpost: { apiKeys: [env.INPOST_MIDDLEWARE_API_KEY] },
        },
    });

    app.use('/healthcheck', (_, res) => res.sendStatus(200));
    app.use(
        partnerIdentificationMiddleware.hook.bind(
            partnerIdentificationMiddleware,
        ) as unknown as RequestHandler,
    );

    app.use(reqResLogger);

    app.use('/auth/check', (_, res) => res.sendStatus(200));

    app.use(
        '/',
        Bloqit.setup({ partnerRegistry, idMappingRepository, logger, router: express.Router() }),
    );

    app.use(
        '/dhl/cz',
        DHL_CZ.setup({
            env,
            logger,
            idMappingRepository,
            httpClient: dhlCZHttpClient,
            router: express.Router(),
        }),
    );

    app.use(
        '/inpost',
        InPost.setup({
            env,
            logger,
            httpClient: inpostHttpClient,
            router: express.Router(),
            mappedLockerRepository,
        }),
    );

    app.use(errorLogger);

    return app;
}
