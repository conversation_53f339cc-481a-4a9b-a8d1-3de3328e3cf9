import { type Request, type Response } from 'express';

declare module 'express' {
    interface Request {
        partnerId?: string;
        context: {
            actionUUID: string;
            reqStartedAt: number;
            method: string;
            url: string;
            partnerId?: string;
        };
    }
}

export interface HTTPRequest extends Request {}
export interface HTTPResponse extends Response {}
