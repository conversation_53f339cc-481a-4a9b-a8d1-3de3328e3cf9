import * as http from 'node:http';
import * as ENV from '../core/env';
import * as APP from './app';

run().catch(err => {
    console.error('Failed to bootstrap Middleware application');
    console.error(err);
    process.exit(1);
});

async function run(): Promise<void> {
    const env = ENV.setup();
    const app = await APP.setup({ env });

    http.createServer(app).listen(env.PORT, () => {
        console.log(`Server running at ${env.PORT.toString()} 🚀`);
    });
}
