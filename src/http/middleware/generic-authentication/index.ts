import { type Request, type Response, type NextFunction } from 'express';
import { HTTPResponse } from '../../models/extensions/express';

export class GenericAuthenticationMiddleware {
    private readonly fakeApiKey: string;

    constructor(deps: { fakeApiKey: string }) {
        this.fakeApiKey = deps.fakeApiKey;

        this.authenticate = this.authenticate.bind(this);
    }

    authenticate(req: Request, res: Response, next: NextFunction): HTTPResponse | undefined {
        const requestApiKey = req.headers['x-fake-api-key']?.toString();
        if (!requestApiKey || requestApiKey !== this.fakeApiKey) {
            return res.status(403).json({ error: 'Forbidden: Invalid API key' });
        }
        next();
    }
}
