import { PartnerIdentificationMiddleware } from '.';
import FakeExpressRequestBuilder from '../../../../test/mocks/http/express/request';
import FakeExpressResponseBuilder from '../../../../test/mocks/http/express/response';
import { simplifyControllerResponse } from '../../../../test/utils';
import { type HTTPResponse } from '../../models/extensions/express';
import * as httpCodes from '../../models/codes';
import { type JWTClient } from '../../../core/utils/jwt-client';

describe('ServiceAuthenticationMiddleware', () => {
    const partnerId = 'partner-id';

    const coreAPIKey = 'core-api-key';
    const base64CoreAPIKey = Buffer.from(coreAPIKey).toString('base64');

    const partnerAPIKey = 'partner-key';
    const base64PartnerAPIKey = Buffer.from(partnerAPIKey).toString('base64');

    const partnerSecrets = {
        bloqit: { apiKeys: [coreAPIKey] },
        [partnerId]: { apiKeys: [partnerAPIKey] },
    };

    const jwtClient: JWTClient = { verify: jest.fn() };

    const next = jest.fn();
    const res = new FakeExpressResponseBuilder().build();

    afterEach(() => {
        next.mockReset();
        jest.restoreAllMocks();
    });

    describe('Partner authentication', () => {
        it('should reject requests without a partner id', async () => {
            const req = new FakeExpressRequestBuilder()
                .withHeaders({ 'x-partner-id': partnerId })
                .build();

            const middleware = new PartnerIdentificationMiddleware({
                partnerSecrets,
                jwtClient,
            });
            const rawResponse = middleware.hook(req, res, next) as HTTPResponse;
            const response = simplifyControllerResponse(rawResponse);

            expect(response.status).toEqual(httpCodes.UNAUTHORIZED);
        });

        it('should reject requests with an invalid partner id', async () => {
            const res = new FakeExpressResponseBuilder().build();
            const req = new FakeExpressRequestBuilder()
                .withHeaders({ 'x-partner-id': 'invalid-partner-id' })
                .build();

            const middleware = new PartnerIdentificationMiddleware({ partnerSecrets, jwtClient });
            const rawResponse = middleware.hook(req, res, next) as HTTPResponse;
            const response = simplifyControllerResponse(rawResponse);

            expect(response.status).toEqual(httpCodes.UNAUTHORIZED);
        });

        it('should reject requests without api key identification', async () => {
            const req = new FakeExpressRequestBuilder()
                .withHeaders({ 'x-partner-id': partnerId, 'x-api-key': undefined })
                .build();

            const middleware = new PartnerIdentificationMiddleware({ partnerSecrets, jwtClient });
            const rawResponse = middleware.hook(req, res, next) as HTTPResponse;
            const response = simplifyControllerResponse(rawResponse);

            expect(response.status).toEqual(httpCodes.UNAUTHORIZED);
        });

        it('should reject requests containing an invalid api key', async () => {
            const req = new FakeExpressRequestBuilder()
                .withHeaders({ 'x-partner-id': partnerId, 'x-api-key': 'invalid-key' })
                .build();

            const middleware = new PartnerIdentificationMiddleware({ partnerSecrets, jwtClient });
            const rawResponse = middleware.hook(req, res, next) as HTTPResponse;
            const response = simplifyControllerResponse(rawResponse);

            expect(response.status).toEqual(httpCodes.UNAUTHORIZED);
        });

        it('should allow authenticated requests', async () => {
            const req = new FakeExpressRequestBuilder()
                .withHeaders({ 'x-partner-id': partnerId, 'x-api-key': base64PartnerAPIKey })
                .build();

            const middleware = new PartnerIdentificationMiddleware({ partnerSecrets, jwtClient });
            middleware.hook(req, res, next) as HTTPResponse;

            expect(next).toHaveBeenCalledTimes(1);
        });
    });

    describe('Core authentication', () => {
        it('should reject requests with an invalid core API key', async () => {
            const req = new FakeExpressRequestBuilder()
                .withHeaders({ 'x-core-api-key': 'invalid-core-api-key' })
                .build();

            const middleware = new PartnerIdentificationMiddleware({ partnerSecrets, jwtClient });
            const rawResponse = middleware.hook(req, res, next) as HTTPResponse;
            const response = simplifyControllerResponse(rawResponse);

            expect(response.status).toEqual(httpCodes.UNAUTHORIZED);
        });

        it('should accept core requests using only its API key as identification token', async () => {
            const res = new FakeExpressResponseBuilder().build();
            const req = new FakeExpressRequestBuilder()
                .withHeaders({ 'x-partner-id': partnerId, 'x-core-api-key': base64CoreAPIKey })
                .build();

            const middleware = new PartnerIdentificationMiddleware({ partnerSecrets, jwtClient });
            middleware.hook(req, res, next) as HTTPResponse;

            expect(next).toHaveBeenCalledTimes(1);
        });
    });

    describe('Webhooks', () => {
        it('should reject requests with an invalid webhook signature', async () => {
            const req = new FakeExpressRequestBuilder()
                .withHeaders({ signature: 'invalid-signature' })
                .build();

            const middleware = new PartnerIdentificationMiddleware({ partnerSecrets, jwtClient });
            const rawResponse = middleware.hook(req, res, next) as HTTPResponse;
            const response = simplifyControllerResponse(rawResponse);

            expect(response.status).toEqual(httpCodes.UNAUTHORIZED);
        });

        it('should accept core webhook requests using a signature', async () => {
            const webhookSignature = 'signature';
            const timestamp = new Date().toString();
            const reqBody = { test: 'payload' };

            jest.spyOn(jwtClient, 'verify').mockImplementationOnce(
                () => `${timestamp.toString()}.${JSON.stringify(reqBody)}`,
            );

            const res = new FakeExpressResponseBuilder().build();
            const req = new FakeExpressRequestBuilder()
                .withHeaders({
                    timestamp,
                    'x-partner-id': partnerId,
                    signature: webhookSignature,
                })
                .withBody(reqBody)
                .build();

            const middleware = new PartnerIdentificationMiddleware({ partnerSecrets, jwtClient });
            middleware.hook(req, res, next) as HTTPResponse;

            expect(next).toHaveBeenCalledTimes(1);
        });
    });
});
