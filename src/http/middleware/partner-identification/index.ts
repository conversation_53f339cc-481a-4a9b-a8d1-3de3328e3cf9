import { type Request, type Response, type NextFunction } from 'express';
import * as httpCodes from '../../models/codes';
import { type HTTPResponse } from '../../models/extensions/express';
import { type JWTClient } from '../../../core/utils/jwt-client';

export enum CallerType {
    PARTNER = 'partner',
    CORE = 'core',
    CORE_WEBHOOKS = 'core-webhooks',
    UNKNOWN = 'unknown',
}

export class PartnerIdentificationMiddleware {
    private readonly partnerSecrets: Record<string, { apiKeys: string[] }>;
    private readonly jwtClient: JWTClient;

    constructor(deps: {
        jwtClient: JWTClient;
        partnerSecrets: Record<string, { apiKeys: string[] }>;
    }) {
        this.partnerSecrets = deps.partnerSecrets;
        this.jwtClient = deps.jwtClient;

        this.hook = this.hook.bind(this);
        this.decryptAPIKey = this.decryptAPIKey.bind(this);
        this.isAPIKeyValid = this.isAPIKeyValid.bind(this);
        this.computeCallerType = this.computeCallerType.bind(this);
        this.isWebhookSignatureValid = this.isWebhookSignatureValid.bind(this);
    }

    hook(req: Request, res: Response, next: NextFunction): HTTPResponse | undefined {
        const partnerId = req.headers['x-partner-id']?.toString();
        if (partnerId === undefined) {
            return res.status(httpCodes.UNAUTHORIZED).send();
        }

        req.partnerId = partnerId;

        const coreAPIKey = req.headers['x-core-api-key']?.toString();
        const apiKey = req.headers['x-api-key']?.toString();
        const signature = req.headers.signature?.toString();

        const callerType = this.computeCallerType({ coreAPIKey, apiKey, signature });

        switch (callerType) {
            case CallerType.PARTNER:
                if (!this.isAPIKeyValid({ partnerId, key: apiKey })) {
                    return res.status(httpCodes.UNAUTHORIZED).send();
                }
                break;
            case CallerType.CORE:
                if (!this.isAPIKeyValid({ partnerId: 'bloqit', key: coreAPIKey })) {
                    return res.status(httpCodes.UNAUTHORIZED).send();
                }
                break;
            case CallerType.CORE_WEBHOOKS:
                if (
                    !this.isWebhookSignatureValid({
                        signature,
                        body: req.body,
                        timestamp: req.headers.timestamp?.toString() ?? '',
                    })
                ) {
                    return res.status(httpCodes.UNAUTHORIZED).send();
                }
                break;
            case CallerType.UNKNOWN:
                return res.status(httpCodes.UNAUTHORIZED).send();
            default:
                throw new Error('Invalid caller type');
        }

        next();
    }

    private decryptAPIKey(key: string): string {
        return Buffer.from(key, 'base64').toString();
    }

    private isAPIKeyValid(props: { partnerId: string; key?: string }): boolean {
        const { partnerId, key } = props;
        return (
            key !== undefined &&
            this.partnerSecrets[partnerId]?.apiKeys.includes(this.decryptAPIKey(key))
        );
    }

    private isWebhookSignatureValid(props: {
        body: any;
        timestamp: string;
        signature?: string;
    }): boolean {
        const { body, timestamp, signature } = props;
        if (signature === undefined) return false;

        try {
            const result = this.jwtClient.verify(
                signature,
                Buffer.from(this.partnerSecrets.bloqit.apiKeys[0]).toString('base64'),
            );
            return result === `${timestamp}.${JSON.stringify(body)}`;
        } catch (ex) {
            return false;
        }
    }

    private computeCallerType(props: {
        coreAPIKey?: string;
        apiKey?: string;
        signature?: string;
    }): CallerType {
        const { coreAPIKey, apiKey, signature } = props;
        if (apiKey !== undefined) {
            return CallerType.PARTNER;
        } else if (coreAPIKey !== undefined) {
            return CallerType.CORE;
        } else if (signature !== undefined) {
            return CallerType.CORE_WEBHOOKS;
        }

        return CallerType.UNKNOWN;
    }
}
