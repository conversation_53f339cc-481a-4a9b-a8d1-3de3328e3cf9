import { type RequestHandler, type Router } from 'express';
import { GenericRequestController } from './controller';
import { type Logger } from '../../core/logger';
import { type HTTPClient } from '../../core/http-client';
import type * as ENV from '../../core/env';

export const setup = (deps: {
    router: Router;
    httpClient: HTTPClient;
    logger: Logger;
    env: ENV.ApplicationEnvironment;
}): Router => {
    const { router, httpClient, logger, env } = deps;

    const ctrl = new GenericRequestController({
        logger,
        genericHttpClient: httpClient,
        env,
    });

    router.post('/', ctrl.handler as unknown as <PERSON><PERSON><PERSON><PERSON><PERSON>);

    return router;
};
