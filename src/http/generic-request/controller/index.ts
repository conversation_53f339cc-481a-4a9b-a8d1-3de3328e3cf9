import { HTTPClient } from '../../../core/http-client';
import { type Logger } from '../../../core/logger';
import { type HTTPRequest, type HTTPResponse } from '../../../http/models/extensions/express';
import type * as ENV from '../../../core/env';

export class GenericRequestController {
    private readonly genericHttpClient: HTTPClient;
    private readonly logger: Logger;
    private readonly env: ENV.ApplicationEnvironment;

    constructor(deps: {
        genericHttpClient: HTTPClient;
        logger: Logger;
        env: ENV.ApplicationEnvironment;
    }) {
        this.genericHttpClient = deps.genericHttpClient;
        this.logger = deps.logger;
        this.env = deps.env;

        this.handler = this.handler.bind(this);
    }

    async handler(req: HTTPRequest, res: HTTPResponse): Promise<HTTPResponse> {
        const { method, path, payload, headers, partner } = req.body;

        if (!method || !path || !partner) {
            return res
                .status(400)
                .json({ error: 'Missing required fields: method, path or partner' });
        }

        try {
            const partnerUrls: Record<string, string> = {
                inpost: this.env.INPOST_API_HOST.startsWith('https://')
                    ? this.env.INPOST_API_HOST
                    : `https://${this.env.INPOST_API_HOST}`,
                inpostAuth: this.env.INPOST_OAUTH_HOST.startsWith('https://')
                    ? this.env.INPOST_OAUTH_HOST
                    : `https://${this.env.INPOST_OAUTH_HOST}`,
            };

            if (!partnerUrls[partner]) {
                return res.status(400).json({ error: 'Invalid partner' });
            }

            const request = {
                method: method.toLowerCase(),
                url: `${partnerUrls[partner]}/${path}`,
                data: payload,
                headers: headers || {},
            };

            const response = await this.genericHttpClient.request<{ data: any; status: any }>(
                request,
            );

            this.logger.info({
                message: 'Generic call',
                request,
                response: response.data,
            });

            return res.status(response.status).json({
                request,
                response: response.data,
            });
        } catch (error: any) {
            return res.status(error.response?.status || 500).json({ error: error.message });
        }
    }
}
