const fs = require('fs');

const env = {
    id: 'f005cd78-3e09-4b0a-92f6-794d1144cd42',
    name: 'middleawre-demo',
    values: [
        {
            key: 'url',
            value: process.env.URL,
            type: 'default',
            enabled: true,
        },
        {
            key: 'core_api_key',
            value: process.env.CORE_API_KEY,
            type: 'default',
            enabled: true,
        },
        {
            key: 'dhl_it_partner_id',
            value: process.env.DHL_IT_PARTNER_ID,
            type: 'default',
            enabled: true,
        },
        {
            key: 'courier_drop_off_code',
            value: process.env.COURIER_DROP_OFF_CODE,
            type: 'default',
            enabled: true,
        },
        {
            key: 'boq_external_id',
            value: process.env.BLOQ_EXTERNAL_ID,
            type: 'default',
            enabled: true,
        },
        {
            key: 'customer_rent_external_id',
            value: process.env.CUSTOMER_RENT_EXTERNAL_ID,
            type: 'default',
            enabled: true,
        },
        {
            key: 'response_time_slo',
            value: process.env.RESPONSE_TIME_SLO,
            type: 'default',
            enabled: true,
        },
        {
            key: 'pick_up_confirmation_response_time_slo',
            value: process.env.PICK_UP_CONFIRMATION_RESPONSE_TIME_SLO,
            type: 'default',
            enabled: true,
        },
    ],
    _postman_variable_scope: 'environment',
    _postman_exported_at: '2023-10-17T15:38:26.963Z',
    _postman_exported_using: 'Postman/10.18.11',
};

fs.writeFileSync('test/integration/middleware-postman-env.json', JSON.stringify(env));
