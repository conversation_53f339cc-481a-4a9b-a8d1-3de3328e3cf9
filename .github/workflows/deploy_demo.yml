name: Deploy demo

on:
    push:
        branches: [main]

env:
    PORT: 3000
    NODE_ENV: test
    DHL_CZ_AUTH_PORT: 3001
    DHL_CZ_API_PORT: 3002
    BLOQIT_CORE_PORT: 3003
    USE_STATIC_UUIDS: 1
    DHL_CZ_OAUTH_HOST: http://localhost:3001
    DHL_CZ_API_HOST: http://localhost:3002
    DHL_CZ_OAUTH_CLIENT_ID: client-id
    DHL_CZ_OAUTH_CLIENT_SECRET: client-secret
    DHL_CZ_BLOQIT_API_KEY: bloqit-api-key
    BLOQIT_CORE_HOST: http://localhost:3003
    DHL_CZ_BLOQIT_CORE_API_KEY: dhl-cz-bloqit-core-key
    DHL_CZ_MIDDLEWARE_API_KEY: dhl-cz-middleware-key
    DHL_CZ_PARTNER_ID: dhl_cz
    BLOQIT_CORE_MIDDLEWARE_API_KEY: bloqit-core-middleware-key
    MONGO_DB_URL: mongodb://localhost:27017
    MONGO_DB_NAME: partner-integrations-middleware
    DOCUMENT_DATABASE_STRATEGY: IN_PROCESS_MEMORY
    LOG_TO_CONSOLE_ENABLED: disabled
    DATADOG_LOGGING_ENABLED: disabled
    REQ_RES_PAYLOAD_LOGGING_ENABLED: disabled
    DHL_IT_API_PORT: 3004
    DHL_IT_API_HOST: http://localhost:3004
    DHL_IT_PARTNER_ID: dhl_it
    DHL_IT_MIDDLEWARE_API_KEY: dhl_it_middleware_key
    DHL_IT_AUTH_CLIENT_ID: DHL_IT_AUTH_CLIENT_ID
    DHL_IT_AUTH_CLIENT_SECRET: DHL_IT_AUTH_CLIENT_SECRET
    BLOQIT_COURIER_BYPASS_PIN: 123456
    DHL_IT_BATCH_EXPIRED_RENTS_NOTIFICATION_WINDOW_START: '20:00'
    DHL_IT_BATCH_EXPIRED_RENTS_NOTIFICATION_WINDOW_END: '21:00'
    APP_NAME: Partner Integrations Middleware
    NEW_APP_NAME: partner-integration-service
    ENV_NAME: demo-partner-integrations-middleware
    NEW_ENV_NAME: pis-demo
    AWS_REGION: eu-central-1
    PIPELINE_ID: cicd-${GITHUB_RUN_ID}-${GITHUB_RUN_NUMBER}
    AWS_PLATFORM: Node.js
    SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
    SLACK_CHANNEL: ${{ vars.SLACK_CHANNEL }}
    GREEN_COLOR: 28a745
    RED_COLOR: a74528
    INPOST_API_HOST: http://localhost:3006
    INPOST_PARTNER_ID: inpost
    INPOST_OAUTH_HOST: http://localhost:3007
    INPOST_OAUTH_CLIENT_ID: oauth_id
    INPOST_OAUTH_CLIENT_SECRET: oauth_secret
    INPOST_MIDDLEWARE_API_KEY: inpost-middleware-key
    INPOST_BLOQIT_API_KEY: bloqit-api-key

jobs:
    Integration:
        runs-on: ubuntu-latest

        strategy:
            matrix:
                node-version: [18.x]

        steps:
            - name: Check out the repository
              uses: actions/checkout@v3

            - name: Use Node.js ${{ matrix.node-version }}
              uses: actions/setup-node@v3
              with:
                  node-version: ${{ matrix.node-version }}

            - name: Install dependencies
              run: yarn install

            - name: Check formatting
              run: yarn prettier:check

            - name: Check code style
              run: yarn lint

            - name: Build project
              run: yarn build

            - name: Run unit tests
              run: yarn test

            - name: Spin up server
              run: yarn start &

            - name: Run acceptance tests
              run: yarn test:acceptance

    deploy:
        runs-on: ubuntu-latest
        needs: Integration
        if: success()
        environment: Dev

        strategy:
            matrix:
                node-version: [18.x]

        steps:
            - uses: actions/checkout@v3
            - name: Configure AWS creds
              uses: aws-actions/configure-aws-credentials@v1
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: ${{ env.AWS_REGION }}

            - name: Use Node.js ${{ matrix.node-version }}
              uses: actions/setup-node@v3
              with:
                  node-version: ${{ matrix.node-version }}

            - name: Install dependencies
              run: yarn install

            - name: Build project
              run: yarn build

            - name: Install AWS EB CLI
              run: |
                  sudo apt install python3-pip -y
                  pip3 install awsebcli

            - name: Deploy init
              run: eb init "${{env.APP_NAME}}" -k ${{secrets.AWS_ID}} --region ${{env.AWS_REGION}} --platform ${{env.AWS_PLATFORM}}

            - name: Deploying to demo (CZ env)
              run: eb deploy ${{env.ENV_NAME}} -l ${{env.PIPELINE_ID}}

            - name: Deploy init
              run: eb init ${{env.NEW_APP_NAME}} -k ${{secrets.AWS_ID}} --region ${{env.AWS_REGION}} --platform ${{env.AWS_PLATFORM}}

            - name: Deploying to demo (new)
              run: eb deploy ${{env.NEW_ENV_NAME}} -l ${{env.PIPELINE_ID}}

    notify:
        runs-on: ubuntu-latest
        needs: deploy
        if: always()
        steps:
            - name: Post to Slack
              uses: slackapi/slack-github-action@v1.23.0
              with:
                  channel-id: '${{ env.SLACK_CHANNEL }}'
                  payload: |
                      {
                        "text": "Github Notification",
                        "attachments": [
                          {
                            "color": "${{ needs.deploy.result == 'success' && env.GREEN_COLOR || env.RED_COLOR }}",
                            "fields": [
                              {
                                "title": "${{ needs.deploy.result == 'success' && 'Deploy PIS to Demo Finished Successfully' || 'Deploy PIS to Demo Finished with Failure :alert:' }}",
                                "short": true,
                                "value": "${{ github.event.head_commit.url }}"
                              }
                            ]
                          }
                        ]
                      }
