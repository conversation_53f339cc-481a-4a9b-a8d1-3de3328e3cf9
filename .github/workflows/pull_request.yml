name: Continuous Integration - Pull request

on:
    pull_request:
        branches: [main]

env:
    PORT: 3000
    NODE_ENV: test
    DHL_CZ_AUTH_PORT: 3001
    DHL_CZ_API_PORT: 3002
    BLOQIT_CORE_PORT: 3003
    BLOQIT_CORE_HOST: http://localhost:3003
    USE_STATIC_UUIDS: 1
    DHL_CZ_OAUTH_HOST: http://localhost:3001
    DHL_CZ_OAUTH_CLIENT_ID: oauth_id
    DHL_CZ_OAUTH_CLIENT_SECRET: oauth_secret
    DHL_CZ_API_HOST: http://localhost:3002
    DHL_CZ_MOCK_PARTNER_ID: dhl_cz_mock
    DHL_CZ_BLOQIT_API_KEY: dhl-cz-bloqit-key
    DHL_CZ_BLOQIT_CORE_API_KEY: dhl-cz-bloqit-core-key
    DHL_CZ_MIDDLEWARE_API_KEY: dhl-cz-middleware-key
    DHL_CZ_PARTNER_ID: dhl_cz
    BLOQIT_CORE_MIDDLEWARE_API_KEY: bloqit-core-middleware-key
    MONGO_DB_URL: mongodb://localhost:27017
    MONGO_DB_NAME: partner-integrations-middleware
    DOCUMENT_DATABASE_STRATEGY: IN_PROCESS_MEMORY
    LOG_TO_CONSOLE_ENABLED: disabled
    DATADOG_LOGGING_ENABLED: disabled
    REQ_RES_PAYLOAD_LOGGING_ENABLED: disabled
    DHL_IT_API_PORT: 3004
    DHL_IT_API_HOST: http://localhost:3004
    DHL_IT_PARTNER_ID: dhl_it
    DHL_IT_MOCK_PARTNER_ID: dhl_it_mock
    DHL_IT_MIDDLEWARE_API_KEY: dhl_it_middleware_key
    DHL_IT_AUTH_CLIENT_ID: DHL_IT_AUTH_CLIENT_ID
    DHL_IT_AUTH_CLIENT_SECRET: DHL_IT_AUTH_CLIENT_SECRET
    BLOQIT_COURIER_BYPASS_PIN: 123456
    DHL_IT_BATCH_EXPIRED_RENTS_NOTIFICATION_WINDOW_START: '20:00'
    DHL_IT_BATCH_EXPIRED_RENTS_NOTIFICATION_WINDOW_END: '21:00'
    PARTNER_API_MOCK_SERVER_HOST: http://localhost:3005
    INPOST_API_HOST: http://localhost:3006
    INPOST_PARTNER_ID: inpost
    INPOST_OAUTH_HOST: http://localhost:3007
    INPOST_OAUTH_CLIENT_ID: oauth_id
    INPOST_OAUTH_CLIENT_SECRET: oauth_secret
    INPOST_MIDDLEWARE_API_KEY: inpost-middleware-key
    INPOST_BLOQIT_API_KEY: inpost-bloqit-key

jobs:
    Integration:
        runs-on: ubuntu-latest

        strategy:
            matrix:
                node-version: [18.x]

        steps:
            - name: Check out the repository
              uses: actions/checkout@v3

            - name: Use Node.js ${{ matrix.node-version }}
              uses: actions/setup-node@v3
              with:
                  node-version: ${{ matrix.node-version }}

            - name: Install dependencies
              run: yarn install

            - name: Check formatting
              run: yarn prettier:check

            - name: Check code style
              run: yarn lint

            - name: Build project
              run: yarn build

            - name: Run unit tests
              run: yarn test

            - name: Spin up server
              run: yarn start &

            - name: Run acceptance tests
              run: yarn test:acceptance
