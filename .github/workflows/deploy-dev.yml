---
name: Deploy Development

on:   # yamllint disable-line rule:truthy
  pull_request:
    types:
      - opened
      - reopened
      - synchronize
      - ready_for_review
    branch:
      - main

  workflow_dispatch:

env:
  PORT: 3000
  NODE_ENV: dev
  DHL_CZ_AUTH_PORT: 3001
  DHL_CZ_API_PORT: 3002
  BLOQIT_CORE_PORT: 3003
  USE_STATIC_UUIDS: 1
  DHL_CZ_OAUTH_HOST: http://localhost:3001
  DHL_CZ_API_HOST: http://localhost:3002
  DHL_CZ_OAUTH_CLIENT_ID: client-id
  DHL_CZ_OAUTH_CLIENT_SECRET: client-secret
  DHL_CZ_BLOQIT_API_KEY: bloqit-api-key
  BLOQIT_CORE_HOST: http://localhost:3003
  DHL_CZ_BLOQIT_CORE_API_KEY: dhl-cz-bloqit-core-key
  DHL_CZ_MIDDLEWARE_API_KEY: dhl-cz-middleware-key
  DHL_CZ_PARTNER_ID: dhl_cz
  BLOQIT_CORE_MIDDLEWARE_API_KEY: bloqit-core-middleware-key
  MONGO_DB_URL: mongodb://localhost:27017
  MONGO_DB_NAME: partner-integrations-middleware
  DOCUMENT_DATABASE_STRATEGY: IN_PROCESS_MEMORY
  LOG_TO_CONSOLE_ENABLED: disabled
  DATADOG_LOGGING_ENABLED: disabled
  REQ_RES_PAYLOAD_LOGGING_ENABLED: disabled
  DHL_IT_API_PORT: 3004
  DHL_IT_API_HOST: http://localhost:3004
  DHL_IT_PARTNER_ID: dhl_it
  DHL_IT_MIDDLEWARE_API_KEY: dhl_it_middleware_key
  DHL_IT_AUTH_CLIENT_ID: DHL_IT_AUTH_CLIENT_ID
  DHL_IT_AUTH_CLIENT_SECRET: DHL_IT_AUTH_CLIENT_SECRET
  BLOQIT_COURIER_BYPASS_PIN: 123456
  DHL_IT_BATCH_EXPIRED_RENTS_NOTIFICATION_WINDOW_START: '20:00'
  DHL_IT_BATCH_EXPIRED_RENTS_NOTIFICATION_WINDOW_END: '21:00'
  APP_NAME: partner-integration-service
  ENV_NAME: pis-dev
  AWS_REGION: eu-central-1
  PIPELINE_ID: cicd-${GITHUB_RUN_ID}-${GITHUB_RUN_NUMBER}
  AWS_PLATFORM: Node.js
  SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
  SLACK_CHANNEL: ${{ vars.SLACK_CHANNEL }}
  GREEN_COLOR: 28a745
  RED_COLOR: a74528
  INPOST_API_HOST: http://localhost:3006
  INPOST_PARTNER_ID: inpost
  INPOST_OAUTH_HOST: http://localhost:3007
  INPOST_OAUTH_CLIENT_ID: oauth_id
  INPOST_OAUTH_CLIENT_SECRET: oauth_secret
  INPOST_MIDDLEWARE_API_KEY: inpost-middleware-key
  INPOST_BLOQIT_API_KEY: bloqit-api-key

jobs:
  integration:
    runs-on: ubuntu-latest
    timeout-minutes: 5

    strategy:
      # matrix field used in the future to support multiple versions
      matrix:
        node-version: [18.x]

    steps:
      - name: Check out the repository
        uses: actions/checkout@v4

      - name: Cache node modules
        uses: actions/cache@v4
        env:
          cache-name: cache-node-modules
        with:
          # yarn cache files are stored in `~/.yarn` on Linux/macOS
          path: ~/.cache
          # yamllint disable-line rule:line-length
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-build-${{ env.cache-name }}-
            ${{ runner.os }}-build-
            ${{ runner.os }}-

      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}

      - name: Install dependencies
        run: yarn install  --immutable --immutable-cache --check-cache

      - name: Check formatting
        run: yarn prettier:check

      - name: Check code style
        run: yarn lint

      - name: Run unit tests
        run: yarn test

      - name: Build project
        run: yarn build

      - name: Spin up server
        run: yarn start &

      - name: Run acceptance tests
        run: yarn test:acceptance

      - name: Archive build directory
        uses: actions/upload-artifact@v4
        with:
          name: PIS-develop
          include-hidden-files: true
          if-no-files-found: warn
          compression-level: 1
          # 0 no compression, 1 best speed, 9 best compression
          overwrite: true
          path: |
            ./dist
            ./package.json
            ./package-lock.json
            ./.platform
            ./.ebextensions

  deploy:
    runs-on: ubuntu-latest
    needs: integration
    if: success()

    environment:
      name: development

    steps:
      - name: Download artifact
        uses: actions/download-artifact@v4
        with:
          name: PIS-develop

      - name: Configure AWS creds
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Install AWS EB CLI
        run: |
            sudo apt install python3-pip -y
            pip3 install awsebcli


      # old environment removed left only the new one
      - name: Deploy init
        # yamllint disable rule:line-length
        run: eb init ${{env.APP_NAME}} -k ${{secrets.AWS_ID}} --region ${{env.AWS_REGION}} --platform ${{env.AWS_PLATFORM}}

      - name: Deploying (new)
        run: eb deploy ${{env.ENV_NAME}} -l ${{env.PIPELINE_ID}}

  notify:
    runs-on: ubuntu-latest
    needs: deploy
    if: always()
    steps:
      - name: Post to Slack
        uses: slackapi/slack-github-action@v1.23.0
        with:
          channel-id: '${{ env.SLACK_CHANNEL }}'
          # yamllint disable rule:line-length
          payload: |
              {
                "text": "Github Notification",
                "attachments": [
                  {
                    "color": "${{ needs.deploy.result == 'success' && env.GREEN_COLOR || env.RED_COLOR }}",
                    "fields": [
                      {
                        "title": "${{ needs.deploy.result == 'success' && 'Deploy PIS to DEV Finished Successfully' || 'Deploy PIS to DEV Finished with Failure :alert:' }}",
                        "short": true,
                        "value": "${{ github.event.head_commit.url }}"
                      }
                    ]
                  }
                ]
              }
