name: Continuous Integration - Push button test

on: workflow_dispatch

jobs:
    integration:
        runs-on: ubuntu-latest
        env:
            URL: ${{ vars.URL }}
            CORE_API_KEY: ${{ secrets.DEMO_CORE_API_KEY }}
            DHL_IT_PARTNER_ID: ${{ vars.DHL_IT_PARTNER_ID }}
            COURIER_DROP_OFF_CODE: ${{ vars.COURIER_DROP_OFF_CODE }}
            BLOQ_EXTERNAL_ID: ${{ vars.BLOQ_EXTERNAL_ID }}
            CUSTOMER_RENT_EXTERNAL_ID: ${{ vars.CUSTOMER_RENT_EXTERNAL_ID }}
            RESPONSE_TIME_SLO: ${{ vars.RESPONSE_TIME_SLO }}
            PICK_UP_CONFIRMATION_RESPONSE_TIME_SLO: ${{ vars.PICK_UP_CONFIRMATION_RESPONSE_TIME_SLO }}
            DHL_IT_BATCH_EXPIRED_RENTS_NOTIFICATION_WINDOW_START: '20:00'
            DHL_IT_BATCH_EXPIRED_RENTS_NOTIFICATION_WINDOW_END: '21:00'

        strategy:
            matrix:
                node-version: [18.x]

        steps:
            - name: Check out the repository
              uses: actions/checkout@v3

            - name: Use Node.js ${{ matrix.node-version }}
              uses: actions/setup-node@v3
              with:
                  node-version: ${{ matrix.node-version }}

            - name: Install dependencies
              run: yarn install

            - name: Generate postman env
              run: yarn generate-postman-env

            - name: Run integration tests
              run: yarn test:integration
