{"name": "partner-integrations-middleware", "version": "1.0.2", "main": "index.js", "license": "MIT", "scripts": {"dev": "tsnd --ignore-watch node_modules src/http/index.ts", "start": "node ./dist/src/http/index.js", "serve": "yarn build && yarn start", "build": "rimraf ./dist && tsc", "test": "jest --config=jest.config.js", "test:acceptance": "jest --config=test/acceptance/jest.config.js --runInBand", "test:integration": "jest --config=test/integration/jest.config.js --runInBand", "generate-postman-env": "node tasks/generate-postman-env.js", "prettier:check": "npx prettier --check ./src", "prettier:fix": "npx prettier --write ./src", "lint": "npx eslint ./src/**/*.ts", "cmt": "npx git-cz"}, "devDependencies": {"@commitlint/cli": "^19.7.1", "@commitlint/config-conventional": "^19.6.0", "@types/datadog-winston": "^1.0.5", "@types/express": "^4.17.20", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.7", "@types/node": "^22.14.0", "@typescript-eslint/eslint-plugin": "6.21.0", "cz-conventional-changelog": "^3.3.0", "eslint": "^9.25.1", "eslint-config-prettier": "^10.1.1", "eslint-config-standard-with-typescript": "43.0.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-n": "^16.6.2", "eslint-plugin-promise": "^6.6.0", "git-cz": "^4.9.0", "husky": "^9.1.7", "jest": "^29.7.0", "lint-staged": "^15.5.1", "mongodb-memory-server": "^10.1.4", "pactum": "^3.7.6", "prettier": "^3.5.3", "rimraf": "^5.0.8", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "5.8.2", "wirepig": "^0.1.0"}, "lint-staged": {"*.{css,less,scss,html,json,jsx,js}": ["prettier --write ."], "*.js": "eslint --fix"}, "engines": {"npm": ">=9.3.0", "node": ">=18.18.0", "yarn": ">=1.22.19"}, "dependencies": {"@typescript-eslint/parser": "^6.21.0", "axios": "^1.7.9", "datadog-winston": "^1.6.0", "express": "^4.21.2", "express-winston": "^4.2.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.9.6", "winston": "^3.17.0"}, "overrides": {"@typescript-eslint/parser": {"eslint": "$eslint"}, "@typescript-eslint/eslint-plugin": {"eslint": "$eslint"}, "eslint-config-standard-with-typescript": {"eslint": "$eslint"}}}